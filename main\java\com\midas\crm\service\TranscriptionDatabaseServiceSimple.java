package com.midas.crm.service;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Servicio simplificado para consultas directas a transcriptor_db usando SQL nativo
 * ⚠️ IMPORTANTE: Solo consultas de LECTURA - NO modificar tabla en producción
 */
@Service
public class TranscriptionDatabaseServiceSimple {

    private final JdbcTemplate transcriptorJdbcTemplate;

    public TranscriptionDatabaseServiceSimple(@Qualifier("transcriptorDataSource") DataSource transcriptorDataSource) {
        this.transcriptorJdbcTemplate = new JdbcTemplate(transcriptorDataSource);
    }

    /**
     * 🎯 MÉTODO PRINCIPAL: Búsqueda inteligente de transcripciones
     */
    public Optional<Map<String, Object>> findTranscriptionIntelligent(String callId, String agentId,
                                                                      String callerPhone, String audioFileName) {

        System.out.println("🔍 BÚSQUEDA INTELIGENTE BD - Call ID: " + callId + ", Agent: " + agentId +
                ", Phone: " + callerPhone + ", File: " + audioFileName);

        // ESTRATEGIA 1: Por call_id exacto (más eficiente)
        if (callId != null && !callId.trim().isEmpty()) {
            Optional<Map<String, Object>> result = findByCallId(callId);
            if (result.isPresent()) {
                System.out.println("✅ ENCONTRADO por call_id: " + callId);
                return result;
            }
        }

        // ESTRATEGIA 2: Por audio_file_name exacto
        if (audioFileName != null && !audioFileName.trim().isEmpty()) {
            Optional<Map<String, Object>> result = findByAudioFileName(audioFileName);
            if (result.isPresent()) {
                System.out.println("✅ ENCONTRADO por audio_file_name: " + audioFileName);
                return result;
            }
        }

        // ESTRATEGIA 3: Por agent_id + caller_phone + fecha actual
        LocalDate today = LocalDate.now();
        if (agentId != null && callerPhone != null) {
            List<Map<String, Object>> results = findByAgentIdAndCallerPhoneAndDate(agentId, callerPhone, today);
            if (!results.isEmpty()) {
                System.out.println("✅ ENCONTRADO por agent_id + caller_phone + fecha");
                return Optional.of(results.get(0));
            }
        }

        // ESTRATEGIA 4: Por agent_id + fecha
        if (agentId != null) {
            List<Map<String, Object>> results = findByAgentIdAndDate(agentId, today);
            if (!results.isEmpty()) {
                System.out.println("✅ ENCONTRADO por agent_id + fecha - " + results.size() + " resultados");
                return Optional.of(results.get(0));
            }
        }

        // ESTRATEGIA 5: Por caller_phone + fecha
        if (callerPhone != null) {
            List<Map<String, Object>> results = findByCallerPhoneAndDate(callerPhone, today);
            if (!results.isEmpty()) {
                System.out.println("✅ ENCONTRADO por caller_phone + fecha - " + results.size() + " resultados");
                return Optional.of(results.get(0));
            }
        }

        // ESTRATEGIA 6: Búsqueda reciente (últimas 48 horas)
        LocalDateTime since48Hours = LocalDateTime.now().minusHours(48);

        if (agentId != null) {
            List<Map<String, Object>> results = findRecentByAgentId(agentId, since48Hours);
            if (!results.isEmpty()) {
                System.out.println("✅ ENCONTRADO por agent_id reciente (48h) - " + results.size() + " resultados");
                return Optional.of(results.get(0));
            }
        }

        if (callerPhone != null) {
            List<Map<String, Object>> results = findRecentByCallerPhone(callerPhone, since48Hours);
            if (!results.isEmpty()) {
                System.out.println("✅ ENCONTRADO por caller_phone reciente (48h) - " + results.size() + " resultados");
                return Optional.of(results.get(0));
            }
        }

        System.out.println("❌ NO ENCONTRADO - Ninguna estrategia tuvo éxito");
        return Optional.empty();
    }

    /**
     * 🔍 Buscar por call_id exacto
     */
    private Optional<Map<String, Object>> findByCallId(String callId) {
        try {
            String sql = "SELECT * FROM TRA_DATASET_AUDIO_001 WHERE call_id = ? LIMIT 1";
            List<Map<String, Object>> results = transcriptorJdbcTemplate.query(sql, transcriptionRowMapper(), callId);
            return results.isEmpty() ? Optional.empty() : Optional.of(results.get(0));
        } catch (Exception e) {
            System.out.println("⚠️ Error buscando por call_id: " + e.getMessage());
            return Optional.empty();
        }
    }

    /**
     * 🔍 Buscar por audio_file_name
     */
    private Optional<Map<String, Object>> findByAudioFileName(String audioFileName) {
        try {
            String sql = "SELECT * FROM TRA_DATASET_AUDIO_001 WHERE audio_file_name = ? ORDER BY created_at DESC LIMIT 1";
            List<Map<String, Object>> results = transcriptorJdbcTemplate.query(sql, transcriptionRowMapper(), audioFileName);
            return results.isEmpty() ? Optional.empty() : Optional.of(results.get(0));
        } catch (Exception e) {
            System.out.println("⚠️ Error buscando por audio_file_name: " + e.getMessage());
            return Optional.empty();
        }
    }

    /**
     * 🔍 Buscar por agent_id + caller_phone + fecha
     */
    private List<Map<String, Object>> findByAgentIdAndCallerPhoneAndDate(String agentId, String callerPhone, LocalDate date) {
        try {
            String sql = "SELECT * FROM TRA_DATASET_AUDIO_001 WHERE agent_id = ? AND caller_phone = ? AND DATE(call_datetime) = ? ORDER BY created_at DESC LIMIT 5";
            return transcriptorJdbcTemplate.query(sql, transcriptionRowMapper(), agentId, callerPhone, date.toString());
        } catch (Exception e) {
            System.out.println("⚠️ Error buscando por agent_id + caller_phone + fecha: " + e.getMessage());
            return List.of();
        }
    }

    /**
     * 🔍 Buscar por agent_id + fecha
     */
    private List<Map<String, Object>> findByAgentIdAndDate(String agentId, LocalDate date) {
        try {
            String sql = "SELECT * FROM TRA_DATASET_AUDIO_001 WHERE agent_id = ? AND DATE(call_datetime) = ? ORDER BY created_at DESC LIMIT 5";
            return transcriptorJdbcTemplate.query(sql, transcriptionRowMapper(), agentId, date.toString());
        } catch (Exception e) {
            System.out.println("⚠️ Error buscando por agent_id + fecha: " + e.getMessage());
            return List.of();
        }
    }

    /**
     * 🔍 Buscar por caller_phone + fecha
     */
    private List<Map<String, Object>> findByCallerPhoneAndDate(String callerPhone, LocalDate date) {
        try {
            String sql = "SELECT * FROM TRA_DATASET_AUDIO_001 WHERE caller_phone = ? AND DATE(call_datetime) = ? ORDER BY created_at DESC LIMIT 5";
            return transcriptorJdbcTemplate.query(sql, transcriptionRowMapper(), callerPhone, date.toString());
        } catch (Exception e) {
            System.out.println("⚠️ Error buscando por caller_phone + fecha: " + e.getMessage());
            return List.of();
        }
    }

    /**
     * 🔍 Buscar recientes por agent_id
     */
    private List<Map<String, Object>> findRecentByAgentId(String agentId, LocalDateTime since) {
        try {
            String sql = "SELECT * FROM TRA_DATASET_AUDIO_001 WHERE agent_id = ? AND created_at >= ? ORDER BY created_at DESC LIMIT 5";
            return transcriptorJdbcTemplate.query(sql, transcriptionRowMapper(), agentId, since.toString());
        } catch (Exception e) {
            System.out.println("⚠️ Error buscando recientes por agent_id: " + e.getMessage());
            return List.of();
        }
    }

    /**
     * 🔍 Buscar recientes por caller_phone
     */
    private List<Map<String, Object>> findRecentByCallerPhone(String callerPhone, LocalDateTime since) {
        try {
            String sql = "SELECT * FROM TRA_DATASET_AUDIO_001 WHERE caller_phone = ? AND created_at >= ? ORDER BY created_at DESC LIMIT 5";
            return transcriptorJdbcTemplate.query(sql, transcriptionRowMapper(), callerPhone, since.toString());
        } catch (Exception e) {
            System.out.println("⚠️ Error buscando recientes por caller_phone: " + e.getMessage());
            return List.of();
        }
    }

    /**
     * 🔍 Contar transcripciones del día
     */
    public long countTodayTranscriptions() {
        try {
            String sql = "SELECT COUNT(*) FROM TRA_DATASET_AUDIO_001 WHERE DATE(call_datetime) = ?";
            Long count = transcriptorJdbcTemplate.queryForObject(sql, Long.class, LocalDate.now().toString());
            System.out.println("📊 Transcripciones del día " + LocalDate.now() + ": " + count);
            return count != null ? count : 0;
        } catch (Exception e) {
            System.out.println("⚠️ Error contando transcripciones: " + e.getMessage());
            return 0;
        }
    }

    /**
     * 🔍 Obtener últimas transcripciones
     */
    public List<Map<String, Object>> getLatestTranscriptions(int limit) {
        try {
            String sql = "SELECT * FROM TRA_DATASET_AUDIO_001 ORDER BY created_at DESC LIMIT ?";
            List<Map<String, Object>> results = transcriptorJdbcTemplate.query(sql, transcriptionRowMapper(), limit);
            System.out.println("📋 Últimas " + limit + " transcripciones obtenidas: " + results.size());
            return results;
        } catch (Exception e) {
            System.out.println("⚠️ Error obteniendo últimas transcripciones: " + e.getMessage());
            return List.of();
        }
    }

    /**
     * 🎯 Método específico para el flujo de transcripción
     */
    public Optional<Map<String, Object>> findForTranscriptionFlow(String callId, String agentId,
                                                                  String callerPhone, String audioFileName) {

        System.out.println("🎯 BÚSQUEDA PARA FLUJO DE TRANSCRIPCIÓN");
        System.out.println("  📋 Call ID: " + callId);
        System.out.println("  👤 Agent ID: " + agentId);
        System.out.println("  📱 Caller Phone: " + callerPhone);
        System.out.println("  🎵 Audio File: " + audioFileName);

        // Mostrar estadísticas antes de buscar
        long todayCount = countTodayTranscriptions();
        System.out.println("  📊 Transcripciones hoy: " + todayCount);

        // Realizar búsqueda inteligente
        Optional<Map<String, Object>> result = findTranscriptionIntelligent(callId, agentId, callerPhone, audioFileName);

        if (result.isPresent()) {
            Map<String, Object> record = result.get();
            System.out.println("✅ TRANSCRIPCIÓN ENCONTRADA:");
            System.out.println("  🆔 Call ID real: " + record.get("call_id"));
            System.out.println("  👤 Agent ID real: " + record.get("agent_id"));
            System.out.println("  📱 Phone real: " + record.get("caller_phone"));
            System.out.println("  🎵 File real: " + record.get("audio_file_name"));
            System.out.println("  📅 Created: " + record.get("created_at"));
            System.out.println("  📝 Text length: " + ((String) record.get("original_text")).length() + " chars");
        } else {
            System.out.println("❌ TRANSCRIPCIÓN NO ENCONTRADA");
            // Mostrar últimas transcripciones para debugging
            getLatestTranscriptions(5);
        }

        return result;
    }

    /**
     * RowMapper para convertir ResultSet a Map
     */
    private RowMapper<Map<String, Object>> transcriptionRowMapper() {
        return new RowMapper<Map<String, Object>>() {
            @Override
            public Map<String, Object> mapRow(ResultSet rs, int rowNum) throws SQLException {
                Map<String, Object> result = new HashMap<>();

                // Campos principales
                result.put("id", rs.getLong("id"));
                result.put("call_id", rs.getString("call_id"));
                result.put("transcription", rs.getString("original_text"));
                result.put("original_text", rs.getString("original_text"));
                result.put("translated_text", rs.getString("translated_text"));

                // Metadatos
                result.put("whisper_model", rs.getString("whisper_model"));
                result.put("device_used", rs.getString("device_used"));
                result.put("processing_time", rs.getDouble("processing_time"));
                result.put("word_count", rs.getInt("word_count"));
                result.put("language_detected", rs.getString("language_detected"));
                result.put("audio_quality_score", rs.getDouble("audio_quality_score"));

                // Análisis de sentimientos
                result.put("mood", rs.getString("mood_analysis"));
                result.put("mood_analysis", rs.getString("mood_analysis"));
                result.put("mood_confidence", rs.getDouble("mood_confidence"));
                result.put("sentiment_score", rs.getDouble("sentiment_score"));

                // Información de la llamada
                result.put("call_type", rs.getString("call_type"));
                result.put("call_duration", rs.getDouble("call_duration"));
                result.put("caller_phone", rs.getString("caller_phone"));
                result.put("agent_id", rs.getString("agent_id"));
                result.put("audio_file_name", rs.getString("audio_file_name"));

                // Fechas
                result.put("call_datetime", rs.getTimestamp("call_datetime"));
                result.put("created_at", rs.getTimestamp("created_at"));
                result.put("updated_at", rs.getTimestamp("updated_at"));

                // Indicadores de éxito
                result.put("success", true);
                result.put("source", "database_direct_sql");

                return result;
            }
        };
    }
}

package com.midas.crm.mapper;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.midas.crm.entity.TranscriptionAnalysis;
import com.midas.crm.entity.DTO.TranscriptionAnalysisDTO;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * Mapper para convertir entre TranscriptionAnalysis y TranscriptionAnalysisDTO
 */
@Slf4j
public class TranscriptionAnalysisMapper {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * Convierte una entidad TranscriptionAnalysis a DTO completo
     */
    public static TranscriptionAnalysisDTO toDTO(TranscriptionAnalysis entity) {
        if (entity == null) {
            return null;
        }

        TranscriptionAnalysisDTO.TranscriptionAnalysisDTOBuilder builder = TranscriptionAnalysisDTO.builder()
                .id(entity.getId())
                .clienteResidencialId(entity.getClienteResidencial() != null ? entity.getClienteResidencial().getId() : null)
                .porcentajePromedio(entity.getPorcentajePromedio())
                .porcentajePonderado(entity.getPorcentajePonderado())
                .nivelConfianza(entity.getNivelConfianza())
                .totalCampos(entity.getTotalCampos())
                .camposExactos(entity.getCamposExactos())
                .camposBuenos(entity.getCamposBuenos())
                .camposRegulares(entity.getCamposRegulares())
                .camposMalos(entity.getCamposMalos())
                .fechaCreacion(entity.getFechaCreacion())
                .fechaActualizacion(entity.getFechaActualizacion())
                .estado(entity.getEstado())
                .versionApi(entity.getVersionApi())
                .tiempoProcesamiento(entity.getTiempoProcesamiento())
                .observaciones(entity.getObservaciones());

        // Agregar datos del cliente si están disponibles
        if (entity.getClienteResidencial() != null) {
            builder.nombreCliente(entity.getClienteResidencial().getNombresApellidos())
                   .movilContacto(entity.getClienteResidencial().getMovilContacto())
                   .numeroAgente(entity.getClienteResidencial().getNumeroAgente())
                   .campania(entity.getClienteResidencial().getCampania())
                   .nifNie(entity.getClienteResidencial().getNifNie())
                   .fechaCreacionCliente(entity.getClienteResidencial().getFechaCreacion());
        }

        // Convertir JSON strings a Maps (opcional para DTO completo)
        try {
            if (entity.getCamposAnalizados() != null) {
                builder.camposAnalizados(jsonStringToMap(entity.getCamposAnalizados()));
            }
            if (entity.getEstadisticas() != null) {
                builder.estadisticas(jsonStringToMap(entity.getEstadisticas()));
            }
            if (entity.getResumen() != null) {
                builder.resumen(jsonStringToMap(entity.getResumen()));
            }
            if (entity.getRecomendaciones() != null) {
                builder.recomendaciones(jsonStringToStringArray(entity.getRecomendaciones()));
            }
        } catch (Exception e) {
        }

        return builder.build();
    }

    /**
     * Convierte una entidad TranscriptionAnalysis a DTO simplificado (para listados)
     */
    public static TranscriptionAnalysisDTO toSimpleDTO(TranscriptionAnalysis entity) {
        if (entity == null) {
            return null;
        }

        String nombreCliente = entity.getClienteResidencial() != null ? 
                entity.getClienteResidencial().getNombresApellidos() : null;
        String movilContacto = entity.getClienteResidencial() != null ? 
                entity.getClienteResidencial().getMovilContacto() : null;
        String numeroAgente = entity.getClienteResidencial() != null ? 
                entity.getClienteResidencial().getNumeroAgente() : null;

        return new TranscriptionAnalysisDTO(
                entity.getId(),
                entity.getClienteResidencial() != null ? entity.getClienteResidencial().getId() : null,
                nombreCliente,
                movilContacto,
                numeroAgente,
                entity.getPorcentajePromedio(),
                entity.getNivelConfianza(),
                entity.getFechaCreacion(),
                entity.getEstado()
        );
    }

    /**
     * Convierte un DTO a entidad (para actualizaciones)
     */
    public static TranscriptionAnalysis toEntity(TranscriptionAnalysisDTO dto) {
        if (dto == null) {
            return null;
        }

        TranscriptionAnalysis.TranscriptionAnalysisBuilder builder = TranscriptionAnalysis.builder()
                .id(dto.getId())
                .porcentajePromedio(dto.getPorcentajePromedio())
                .porcentajePonderado(dto.getPorcentajePonderado())
                .nivelConfianza(dto.getNivelConfianza())
                .totalCampos(dto.getTotalCampos())
                .camposExactos(dto.getCamposExactos())
                .camposBuenos(dto.getCamposBuenos())
                .camposRegulares(dto.getCamposRegulares())
                .camposMalos(dto.getCamposMalos())
                .fechaCreacion(dto.getFechaCreacion())
                .fechaActualizacion(dto.getFechaActualizacion())
                .estado(dto.getEstado())
                .versionApi(dto.getVersionApi())
                .tiempoProcesamiento(dto.getTiempoProcesamiento())
                .observaciones(dto.getObservaciones());

        // Convertir Maps a JSON strings
        try {
            if (dto.getCamposAnalizados() != null) {
                builder.camposAnalizados(mapToJsonString(dto.getCamposAnalizados()));
            }
            if (dto.getEstadisticas() != null) {
                builder.estadisticas(mapToJsonString(dto.getEstadisticas()));
            }
            if (dto.getResumen() != null) {
                builder.resumen(mapToJsonString(dto.getResumen()));
            }
            if (dto.getRecomendaciones() != null) {
                builder.recomendaciones(stringArrayToJsonString(dto.getRecomendaciones()));
            }
        } catch (Exception e) {
        }

        return builder.build();
    }

    /**
     * Actualiza una entidad existente con datos del DTO
     */
    public static void updateEntityFromDTO(TranscriptionAnalysis entity, TranscriptionAnalysisDTO dto) {
        if (entity == null || dto == null) {
            return;
        }

        entity.setPorcentajePromedio(dto.getPorcentajePromedio());
        entity.setPorcentajePonderado(dto.getPorcentajePonderado());
        entity.setNivelConfianza(dto.getNivelConfianza());
        entity.setTotalCampos(dto.getTotalCampos());
        entity.setCamposExactos(dto.getCamposExactos());
        entity.setCamposBuenos(dto.getCamposBuenos());
        entity.setCamposRegulares(dto.getCamposRegulares());
        entity.setCamposMalos(dto.getCamposMalos());
        entity.setEstado(dto.getEstado());
        entity.setObservaciones(dto.getObservaciones());

        // Actualizar JSON strings si están presentes en el DTO
        try {
            if (dto.getCamposAnalizados() != null) {
                entity.setCamposAnalizados(mapToJsonString(dto.getCamposAnalizados()));
            }
            if (dto.getEstadisticas() != null) {
                entity.setEstadisticas(mapToJsonString(dto.getEstadisticas()));
            }
            if (dto.getResumen() != null) {
                entity.setResumen(mapToJsonString(dto.getResumen()));
            }
            if (dto.getRecomendaciones() != null) {
                entity.setRecomendaciones(stringArrayToJsonString(dto.getRecomendaciones()));
            }
        } catch (Exception e) {
        }
    }

    // Métodos auxiliares para conversión JSON

    private static Map<String, Object> jsonStringToMap(String jsonString) {
        try {
            if (jsonString == null || jsonString.trim().isEmpty()) {
                return null;
            }
            return objectMapper.readValue(jsonString, Map.class);
        } catch (JsonProcessingException e) {
            return null;
        }
    }

    private static String[] jsonStringToStringArray(String jsonString) {
        try {
            if (jsonString == null || jsonString.trim().isEmpty()) {
                return null;
            }
            return objectMapper.readValue(jsonString, String[].class);
        } catch (JsonProcessingException e) {
            return null;
        }
    }

    private static String mapToJsonString(Map<String, Object> map) {
        try {
            if (map == null) {
                return null;
            }
            return objectMapper.writeValueAsString(map);
        } catch (JsonProcessingException e) {
            return null;
        }
    }

    private static String stringArrayToJsonString(String[] array) {
        try {
            if (array == null) {
                return null;
            }
            return objectMapper.writeValueAsString(array);
        } catch (JsonProcessingException e) {
            return null;
        }
    }
}

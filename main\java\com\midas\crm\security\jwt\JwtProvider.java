package com.midas.crm.security.jwt;


import com.midas.crm.entity.User;
import com.midas.crm.security.UserPrincipal;
import io.jsonwebtoken.Claims;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.security.core.Authentication;

import java.util.Date;

public interface JwtProvider {

    Date getExpirationDate(HttpServletRequest request);

    String generateToken(UserPrincipal auth);

    Authentication getAuthentication(HttpServletRequest request);

    String generateToken(User user);

    boolean isTokenValid(HttpServletRequest request);

    Claims extractClaims(HttpServletRequest request);

}
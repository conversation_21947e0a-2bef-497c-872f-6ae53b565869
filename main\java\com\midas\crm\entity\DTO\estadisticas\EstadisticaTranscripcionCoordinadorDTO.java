package com.midas.crm.entity.DTO.estadisticas;

import lombok.Data;

@Data
public class EstadisticaTranscripcionCoordinadorDTO {
    private Long coordinadorId;
    private String sede;
    private String coordinador;
    private long leadsContacto;
    private long leadsRegistrado;
    private long bienRegistrado;
    private long malRegistrado;
    private long noRegistrado;
    private double eficiencia; // <-- NUEVO CAMPO PARA LA EFICIENCIA


    // Constructor actualizado para recibir todos los valores
    public EstadisticaTranscripcionCoordinadorDTO(
            Long coordinadorId, String sede, String coordinador, long leadsContacto,
            long leadsRegistrado, long bienRegistrado, long malRegistrado, long noRegistrado, double eficiencia) {
        this.coordinadorId = coordinadorId;
        this.sede = sede;
        this.coordinador = coordinador;
        this.leadsContacto = leadsContacto;
        this.leadsRegistrado = leadsRegistrado;
        this.bienRegistrado = bienRegistrado;
        this.malRegistrado = malRegistrado;
        this.noRegistrado = noRegistrado;
        this.eficiencia = eficiencia; // <-- ASIGNACIÓN EN EL CONSTRUCTOR
    }

    // Constructor anterior (puedes mantenerlo si es necesario en otras partes del código)
    public EstadisticaTranscripcionCoordinadorDTO(
            Long coordinadorId, String sede, String coordinador, long leadsContacto,
            long leadsRegistrado, long bienRegistrado, long malRegistrado, long noRegistrado) {
        this(coordinadorId, sede, coordinador, leadsContacto, leadsRegistrado, bienRegistrado, malRegistrado, noRegistrado, 0.0);
    }
}
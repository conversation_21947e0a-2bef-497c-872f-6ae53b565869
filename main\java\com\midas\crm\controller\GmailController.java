package com.midas.crm.controller;

import com.midas.crm.entity.DTO.gmail.EmailDtos;
import com.midas.crm.entity.User;
import com.midas.crm.repository.UserRepository;
import com.midas.crm.service.GmailService;
import com.midas.crm.service.GoogleOAuthService;
import com.midas.crm.utils.GenericResponse;
import com.midas.crm.utils.GenericResponseConstants;
import com.midas.crm.utils.ResponseBuilder;
import jakarta.mail.MessagingException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

/**
 * Controlador para gestionar las operaciones de Gmail de forma segura por usuario.
 * Cada endpoint requiere un usuario autenticado y opera sobre su cuenta de Google conectada.
 */
@RestController
@RequestMapping("${api.route.gmail}")
@RequiredArgsConstructor
@Slf4j
public class GmailController {

    private final GmailService gmailService;
    private final UserRepository userRepository;
    private final GoogleOAuthService googleOAuthService;

    /**
     * Envía un correo electrónico desde la cuenta de Google conectada del usuario autenticado.
     */
    @PostMapping("/enviar")
    public ResponseEntity<GenericResponse<Void>> enviarEmail(
            @RequestBody EmailDtos.EnviarEmailRequest request,
            @AuthenticationPrincipal UserDetails userDetails) {

        User crmUser = getAuthenticatedUser(userDetails);

        try {
            log.info("Usuario del CRM '{}' está enviando un email desde: {} hacia: {}", crmUser.getUsername(), request.de(), request.para());

            gmailService.enviarEmail(crmUser.getId(), request);

            log.info("Email enviado exitosamente para el usuario: {}", crmUser.getUsername());
            // CORRECCIÓN: Argumentos en el orden correcto (datos, mensaje)
            return ResponseBuilder.success(null, "Email enviado exitosamente a " + request.para());

        } catch (MessagingException | IOException e) {
            log.error("Error de API al enviar email para el usuario {}: {}", crmUser.getUsername(), e.getMessage(), e);
            // CORRECCIÓN: Construcción manual de la respuesta de error para asegurar el tipo.
            return new ResponseEntity<>(new GenericResponse<>(GenericResponseConstants.ERROR, "Error al enviar el email: " + e.getMessage(), null), HttpStatus.INTERNAL_SERVER_ERROR);
        } catch (IllegalStateException e) {
            return handleNotConnectedException(e, crmUser.getUsername());
        }
    }

    /**
     * Obtiene la bandeja de entrada del usuario autenticado.
     */
    @GetMapping("/bandeja-entrada")
    public ResponseEntity<GenericResponse<List<EmailDtos.EmailMensajeResumen>>> getBandejaDeEntrada(
            @RequestParam(defaultValue = "25") int limit,
            @AuthenticationPrincipal UserDetails userDetails) {

        User crmUser = getAuthenticatedUser(userDetails);

        try {
            List<EmailDtos.EmailMensajeResumen> mensajes = gmailService.getBandejaDeEntrada(crmUser.getId(), limit);
            // CORRECCIÓN: Argumentos en el orden correcto (datos, mensaje)
            return ResponseBuilder.success(mensajes, "Bandeja de entrada obtenida exitosamente.");
        } catch (IOException e) {
            log.error("Error al obtener la bandeja de entrada para el usuario {}: {}", crmUser.getUsername(), e.getMessage(), e);
            // CORRECCIÓN: Construcción manual de la respuesta de error para asegurar el tipo.
            return new ResponseEntity<>(new GenericResponse<>(GenericResponseConstants.ERROR, "Fallo al obtener la bandeja de entrada: " + e.getMessage(), null), HttpStatus.INTERNAL_SERVER_ERROR);
        } catch (IllegalStateException e) {
            return handleNotConnectedException(e, crmUser.getUsername());
        }
    }

    /**
     * Obtiene los detalles completos de un mensaje específico de la cuenta del usuario autenticado.
     */
    @GetMapping("/mensajes/{idMensaje}")
    public ResponseEntity<GenericResponse<EmailDtos.EmailMensajeDetalle>> getMensaje(
            @PathVariable String idMensaje,
            @AuthenticationPrincipal UserDetails userDetails) {

        User crmUser = getAuthenticatedUser(userDetails);

        try {
            EmailDtos.EmailMensajeDetalle detalleMensaje = gmailService.getDetalleMensaje(crmUser.getId(), idMensaje);
            // CORRECCIÓN: Argumentos en el orden correcto (datos, mensaje)
            return ResponseBuilder.success(detalleMensaje, "Detalles del mensaje obtenidos exitosamente.");
        } catch (IOException e) {
            log.error("Error al obtener detalles del mensaje {} para el usuario {}: {}", idMensaje, crmUser.getUsername(), e.getMessage(), e);
            // CORRECCIÓN: Construcción manual de la respuesta de error para asegurar el tipo.
            return new ResponseEntity<>(new GenericResponse<>(GenericResponseConstants.ERROR, "Fallo al obtener los detalles del mensaje: " + e.getMessage(), null), HttpStatus.INTERNAL_SERVER_ERROR);
        } catch (IllegalStateException e) {
            return handleNotConnectedException(e, crmUser.getUsername());
        }
    }

    /**
     * Descarga un archivo adjunto de un mensaje específico de la cuenta del usuario autenticado.
     */
    @GetMapping("/mensajes/{idMensaje}/adjuntos/{idAdjunto}")
    public ResponseEntity<?> descargarAdjunto(
            @PathVariable String idMensaje,
            @PathVariable String idAdjunto,
            @AuthenticationPrincipal UserDetails userDetails) {

        User crmUser = getAuthenticatedUser(userDetails);

        try {
            byte[] data = gmailService.getAdjunto(crmUser.getId(), idMensaje, idAdjunto);

            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"adjunto-" + idAdjunto + "\"")
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .body(data);
        } catch (IOException e) {
            log.error("Error al descargar adjunto {} del mensaje {} para el usuario {}: {}", idAdjunto, idMensaje, crmUser.getUsername(), e.getMessage());
            // CORRECCIÓN: Construcción manual de la respuesta de error para asegurar el tipo.
            return new ResponseEntity<>(new GenericResponse<>(GenericResponseConstants.ERROR, "No se pudo descargar el archivo adjunto.", null), HttpStatus.INTERNAL_SERVER_ERROR);
        } catch (IllegalStateException e) {
            return handleNotConnectedException(e, crmUser.getUsername());
        }
    }

    /**
     * Verifica el estado de conexión de Gmail del usuario autenticado.
     */
    @GetMapping("/estado-conexion")
    public ResponseEntity<GenericResponse<Boolean>> verificarEstadoConexion(
            @AuthenticationPrincipal UserDetails userDetails) {

        User crmUser = getAuthenticatedUser(userDetails);

        try {
            boolean conectado = gmailService.isUserConnected(crmUser.getId());
            String mensaje = conectado ? "Usuario conectado a Gmail" : "Usuario no conectado a Gmail";
            return ResponseBuilder.success(conectado, mensaje);
        } catch (Exception e) {
            log.error("Error al verificar estado de conexión Gmail para usuario {}: {}", crmUser.getUsername(), e.getMessage(), e);
            return new ResponseEntity<>(
                    new GenericResponse<>(GenericResponseConstants.ERROR, "Error al verificar estado de conexión", false),
                    HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Obtiene la URL de autorización OAuth para Gmail sin hacer redirección automática.
     * Esto resuelve los problemas de CORS al permitir que el frontend maneje la redirección.
     */
    @GetMapping("/url-autorizacion")
    public ResponseEntity<GenericResponse<String>> obtenerUrlAutorizacion(
            @AuthenticationPrincipal UserDetails userDetails) {

        User crmUser = getAuthenticatedUser(userDetails);

        try {
            String authUrl = googleOAuthService.getAuthorizationUrl(crmUser.getId().toString());
            log.info("URL de autorización OAuth generada para usuario: {}", crmUser.getUsername());
            return ResponseBuilder.success(authUrl, "URL de autorización generada exitosamente");
        } catch (Exception e) {
            log.error("Error al generar URL de autorización OAuth para usuario {}: {}", crmUser.getUsername(), e.getMessage(), e);
            return new ResponseEntity<>(
                    new GenericResponse<>(GenericResponseConstants.ERROR, "Error al generar URL de autorización: " + e.getMessage(), null),
                    HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Helper para obtener el usuario autenticado desde el contexto de seguridad.
     * Lanza una excepción si el usuario no está logueado o no se encuentra en la BD.
     */
    private User getAuthenticatedUser(UserDetails userDetails) {
        if (userDetails == null) {
            throw new SecurityException("Acceso no autorizado. Se requiere autenticación.");
        }
        return userRepository.findByUsername(userDetails.getUsername())
                .orElseThrow(() -> new IllegalStateException("El usuario autenticado '" + userDetails.getUsername() + "' no fue encontrado en la base de datos."));
    }

    /**
     * Helper para manejar la excepción específica de cuando un usuario no ha conectado su cuenta de Google.
     */
    private <T> ResponseEntity<GenericResponse<T>> handleNotConnectedException(IllegalStateException e, String username) {
        log.warn("Intento de acceso a Gmail sin autorización para el usuario: {}. Mensaje: {}", username, e.getMessage());
        return new ResponseEntity<>(
                new GenericResponse<>(GenericResponseConstants.ERROR, "No has conectado tu cuenta de Google. Por favor, ve a tu perfil para autorizar el acceso.", null),
                HttpStatus.FORBIDDEN
        );
    }
}

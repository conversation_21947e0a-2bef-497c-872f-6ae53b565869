package com.midas.crm.service.serviceImpl;

import com.midas.crm.entity.Cuestionario;
import com.midas.crm.entity.DTO.cuestionario.CuestionarioCreateDTO;
import com.midas.crm.entity.DTO.cuestionario.CuestionarioDTO;
import com.midas.crm.entity.DTO.cuestionario.CuestionarioUpdateDTO;
import com.midas.crm.entity.Leccion;
import com.midas.crm.entity.Leccion.TipoLeccion;
import com.midas.crm.exceptions.MidasExceptions;
import com.midas.crm.mapper.CuestionarioMapper;
import com.midas.crm.repository.CuestionarioRepository;
import com.midas.crm.repository.LeccionRepository;
import com.midas.crm.service.CuestionarioService;
import com.midas.crm.utils.MidasErrorMessage;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class CuestionarioServiceImpl implements CuestionarioService {

    private final CuestionarioRepository cuestionarioRepository;
    private final LeccionRepository leccionRepository;

    @Override
    @Transactional
    public CuestionarioDTO createCuestionario(CuestionarioCreateDTO dto) {
        // Obtener la lección
        Leccion leccion = leccionRepository.findById(dto.getLeccionId())
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.LECCION_NOT_FOUND));

        // Verificar si ya existe un cuestionario para esta lección
        if (cuestionarioRepository.existsByLeccionId(dto.getLeccionId())) {
            throw new MidasExceptions(MidasErrorMessage.CUESTIONARIO_ALREADY_EXISTS);
        }

        // Cambiar el tipo de lección a CUESTIONARIO
        leccion.setTipoLeccion(TipoLeccion.CUESTIONARIO);
        leccionRepository.save(leccion);

        // Crear el cuestionario
        Cuestionario cuestionario = CuestionarioMapper.toEntity(dto, leccion);
        
        return CuestionarioMapper.toDTO(cuestionarioRepository.save(cuestionario));
    }

    @Override
    @Transactional(readOnly = true)
    public List<CuestionarioDTO> listCuestionarios() {
        return cuestionarioRepository.findAll().stream()
                .map(CuestionarioMapper::toDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public CuestionarioDTO getCuestionarioById(Long id) {
        return cuestionarioRepository.findById(id)
                .map(CuestionarioMapper::toDTO)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.CUESTIONARIO_NOT_FOUND));
    }

    @Override
    @Transactional(readOnly = true)
    public CuestionarioDTO getCuestionarioByLeccionId(Long leccionId) {
        return cuestionarioRepository.findByLeccionId(leccionId)
                .map(CuestionarioMapper::toDTO)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.CUESTIONARIO_NOT_FOUND));
    }

    @Override
    @Transactional
    public CuestionarioDTO updateCuestionario(Long id, CuestionarioUpdateDTO dto) {
        Cuestionario cuestionario = cuestionarioRepository.findById(id)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.CUESTIONARIO_NOT_FOUND));

        CuestionarioMapper.updateEntity(cuestionario, dto);
        
        return CuestionarioMapper.toDTO(cuestionarioRepository.save(cuestionario));
    }

    @Override
    @Transactional
    public void deleteCuestionario(Long id) {
        Cuestionario cuestionario = cuestionarioRepository.findById(id)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.CUESTIONARIO_NOT_FOUND));
        
        // Cambiar el tipo de lección a VIDEO
        Leccion leccion = cuestionario.getLeccion();
        leccion.setTipoLeccion(TipoLeccion.VIDEO);
        leccionRepository.save(leccion);
        
        cuestionarioRepository.deleteById(id);
    }
}

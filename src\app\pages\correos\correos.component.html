<div class="min-h-screen bg-gray-50 dark:bg-gray-900 p-4">
  <div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6">
      <div class="flex items-center space-x-3">
        <div class="flex-shrink-0">
          <svg class="w-8 h-8 text-red-500" viewBox="0 0 24 24" fill="currentColor">
            <path d="M24 5.457v13.909c0 .904-.732 1.636-1.636 1.636h-3.819V11.73L12 16.64l-6.545-4.91v9.273H1.636A1.636 1.636 0 0 1 0 19.366V5.457c0-.904.732-1.636 1.636-1.636h3.819v9.273L12 8.183l6.545 4.91V3.82h3.819c.904 0 1.636.733 1.636 1.637z"/>
          </svg>
        </div>
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Gmail</h1>
          <p class="text-gray-600 dark:text-gray-400">Gestiona tu correo electrónico</p>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div *ngIf="cargando" class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-8">
      <div class="flex flex-col items-center justify-center space-y-4">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        <p class="text-gray-600 dark:text-gray-400">Verificando conexión con Gmail...</p>
      </div>
    </div>

    <!-- Error State -->
    <div *ngIf="error && !cargando" class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-red-200 dark:border-red-700 p-6">
      <div class="flex items-center space-x-3 text-red-600 dark:text-red-400">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <div>
          <h3 class="font-semibold">Error de conexión</h3>
          <p class="text-sm">{{ error }}</p>
        </div>
      </div>
      <div class="mt-4">
        <button 
          (click)="verificarConexionGmail()"
          class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
          Reintentar
        </button>
      </div>
    </div>

    <!-- Not Connected State -->
    <div *ngIf="estadoConexion && !estadoConexion.conectado && !cargando" 
         class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-8">
      <div class="text-center space-y-6">
        <!-- Gmail Icon -->
        <div class="flex justify-center">
          <div class="w-20 h-20 bg-red-50 dark:bg-red-900/20 rounded-full flex items-center justify-center">
            <svg class="w-10 h-10 text-red-500" viewBox="0 0 24 24" fill="currentColor">
              <path d="M24 5.457v13.909c0 .904-.732 1.636-1.636 1.636h-3.819V11.73L12 16.64l-6.545-4.91v9.273H1.636A1.636 1.636 0 0 1 0 19.366V5.457c0-.904.732-1.636 1.636-1.636h3.819v9.273L12 8.183l6.545 4.91V3.82h3.819c.904 0 1.636.733 1.636 1.637z"/>
            </svg>
          </div>
        </div>

        <!-- Title and Description -->
        <div class="space-y-2">
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
            Conecta tu cuenta de Gmail
          </h2>
          <p class="text-gray-600 dark:text-gray-400 max-w-md mx-auto">
            Para acceder a tu bandeja de entrada y gestionar tus correos, necesitas conectar tu cuenta de Gmail.
          </p>
        </div>

        <!-- Status Message -->
        <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4">
          <p class="text-yellow-800 dark:text-yellow-200 text-sm">
            {{ estadoConexion.mensaje }}
          </p>
        </div>

        <!-- Connect Button -->
        <div class="space-y-3">
          <button 
            (click)="conectarGmail()"
            class="inline-flex items-center px-6 py-3 bg-red-600 text-white font-medium rounded-lg hover:bg-red-700 transition-colors space-x-2">
            <svg class="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
              <path d="M24 5.457v13.909c0 .904-.732 1.636-1.636 1.636h-3.819V11.73L12 16.64l-6.545-4.91v9.273H1.636A1.636 1.636 0 0 1 0 19.366V5.457c0-.904.732-1.636 1.636-1.636h3.819v9.273L12 8.183l6.545 4.91V3.82h3.819c.904 0 1.636.733 1.636 1.637z"/>
            </svg>
            <span>Conectar Gmail</span>
          </button>
          
          <div class="text-xs text-gray-500 dark:text-gray-400">
            Se abrirá una nueva ventana para autorizar el acceso
          </div>
        </div>

        <!-- Refresh Button -->
        <div class="pt-4 border-t border-gray-200 dark:border-gray-700">
          <button 
            (click)="recargarPagina()"
            class="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 text-sm font-medium">
            ¿Ya conectaste tu cuenta? Actualizar página
          </button>
        </div>
      </div>
    </div>

    <!-- Router Outlet for child components -->
    <router-outlet></router-outlet>
  </div>
</div>

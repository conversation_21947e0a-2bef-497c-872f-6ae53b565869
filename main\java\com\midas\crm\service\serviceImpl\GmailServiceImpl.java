package com.midas.crm.service.serviceImpl;

import com.google.api.client.auth.oauth2.Credential;
import com.google.api.client.googleapis.auth.oauth2.GoogleClientSecrets;
import com.google.api.client.googleapis.auth.oauth2.GoogleCredential;
import com.google.api.client.googleapis.batch.BatchRequest;
import com.google.api.client.googleapis.batch.json.JsonBatchCallback;
import com.google.api.client.googleapis.json.GoogleJsonError;
import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.http.HttpHeaders;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.JsonFactory;
import com.google.api.client.json.gson.GsonFactory;
import com.google.api.services.gmail.Gmail;
import com.google.api.services.gmail.model.*;
import com.midas.crm.entity.DTO.gmail.EmailDtos;
import com.midas.crm.entity.User;
import com.midas.crm.repository.UserRepository;
import com.midas.crm.service.GmailService;
import jakarta.mail.MessagingException;
import jakarta.mail.Session;
import jakarta.mail.internet.InternetAddress;
import jakarta.mail.internet.MimeMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.security.GeneralSecurityException;
import java.util.*;

/**
 * Implementación del servicio de Gmail para un entorno multiusuario.
 * Cada operación se realiza en nombre del usuario del CRM especificado,
 * utilizando sus propias credenciales de Google almacenadas.
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class GmailServiceImpl implements GmailService {

    private static final String APPLICATION_NAME = "MIDAS CRM Gmail";
    private static final JsonFactory JSON_FACTORY = GsonFactory.getDefaultInstance();
    private static final String USER_ID_API = "me"; // "me" siempre se refiere al usuario autenticado por el token

    private final UserRepository userRepository;

    @Value("${google.drive.credentials.file:client_secret_com.json}")
    private String credentialsFilePath;

    /**
     * Envía un correo electrónico utilizando la cuenta de Google del usuario autenticado.
     * El campo 'de' en la solicitud debe coincidir con el email de la cuenta conectada.
     */
    @Override
    public void enviarEmail(Long crmUserId, EmailDtos.EnviarEmailRequest request) throws MessagingException, IOException {
        log.info("Iniciando envío de email para el usuario del CRM con ID: {}", crmUserId);
        try {
            Gmail gmailService = getGmailServiceForUser(crmUserId);
            Message message = crearMensajeMime(request.de(), request.para(), request.asunto(), request.cuerpo());

            // La API enviará el correo desde la cuenta del usuario cuya credencial se usó.
            Message sentMessage = gmailService.users().messages().send(USER_ID_API, message).execute();
            log.info("Email enviado exitosamente desde la cuenta del usuario {}. ID del mensaje: {}", crmUserId, sentMessage.getId());

        } catch (GeneralSecurityException e) {
            log.error("Error de seguridad al enviar email para el usuario {}: {}", crmUserId, e.getMessage(), e);
            throw new IOException("Error de seguridad al intentar enviar el email.", e);
        }
    }

    /**
     * Obtiene los resúmenes de mensajes de la bandeja de entrada para un usuario específico.
     * Utiliza peticiones por lotes (batching) para un rendimiento óptimo.
     */
    @Override
    public List<EmailDtos.EmailMensajeResumen> getBandejaDeEntrada(Long crmUserId, int maxResults) throws IOException {
        log.info("Obteniendo bandeja de entrada para el usuario del CRM con ID: {}", crmUserId);
        try {
            Gmail gmailService = getGmailServiceForUser(crmUserId);

            ListMessagesResponse listResponse = gmailService.users().messages().list(USER_ID_API)
                    .setLabelIds(List.of("INBOX")).setMaxResults((long) maxResults).execute();

            List<Message> messages = listResponse.getMessages();
            if (messages == null || messages.isEmpty()) {
                log.info("No se encontraron mensajes en la bandeja de entrada para el usuario {}", crmUserId);
                return Collections.emptyList();
            }

            BatchRequest batch = gmailService.batch();
            List<EmailDtos.EmailMensajeResumen> summaries = Collections.synchronizedList(new ArrayList<>());
            JsonBatchCallback<Message> callback = new JsonBatchCallback<>() {
                @Override
                public void onSuccess(Message message, HttpHeaders responseHeaders) {
                    summaries.add(crearResumenDesdeMensaje(message));
                }
                @Override
                public void onFailure(GoogleJsonError e, HttpHeaders responseHeaders) {
                    log.error("Fallo al obtener un mensaje en el lote para el usuario {}: {}", crmUserId, e.getMessage());
                }
            };

            for (Message msg : messages) {
                gmailService.users().messages().get(USER_ID_API, msg.getId()).setFormat("metadata").queue(batch, callback);
            }

            long startTime = System.currentTimeMillis();
            batch.execute();
            log.info("Lote de bandeja de entrada completado en {} ms para {} mensajes del usuario {}", (System.currentTimeMillis() - startTime), messages.size(), crmUserId);

            return summaries;
        } catch (GeneralSecurityException e) {
            throw new IOException("Error de seguridad al obtener la bandeja de entrada.", e);
        }
    }

    @Override
    public EmailDtos.EmailMensajeDetalle getDetalleMensaje(Long crmUserId, String idMensaje) throws IOException {
        log.info("Obteniendo detalles del mensaje {} para el usuario {}", idMensaje, crmUserId);
        try {
            Gmail gmailService = getGmailServiceForUser(crmUserId);
            Message mensaje = gmailService.users().messages().get(USER_ID_API, idMensaje).setFormat("full").execute();

            String de = getCabecera(mensaje.getPayload().getHeaders(), "From").orElse("N/A");
            String para = getCabecera(mensaje.getPayload().getHeaders(), "To").orElse("N/A");
            String asunto = getCabecera(mensaje.getPayload().getHeaders(), "Subject").orElse("(Sin asunto)");
            String fecha = getCabecera(mensaje.getPayload().getHeaders(), "Date").orElse("N/A");

            ParsedPayload parsedPayload = parsearCuerpoYAdjuntos(mensaje.getPayload());
            return new EmailDtos.EmailMensajeDetalle(mensaje.getId(), de, para, asunto, parsedPayload.body(), fecha, parsedPayload.attachments());
        } catch (GeneralSecurityException e) {
            throw new IOException("Error de seguridad al obtener el detalle del mensaje.", e);
        }
    }

    @Override
    public byte[] getAdjunto(Long crmUserId, String idMensaje, String idAdjunto) throws IOException {
        log.info("Descargando adjunto {} del mensaje {} para el usuario {}", idAdjunto, idMensaje, crmUserId);
        try {
            Gmail gmailService = getGmailServiceForUser(crmUserId);
            MessagePartBody body = gmailService.users().messages().attachments().get(USER_ID_API, idMensaje, idAdjunto).execute();
            return Base64.getUrlDecoder().decode(body.getData());
        } catch (GeneralSecurityException e) {
            throw new IOException("Error de seguridad al descargar el adjunto.", e);
        }
    }

    /**
     * Crea y devuelve una instancia del servicio de Gmail autenticada para un usuario específico del CRM.
     * Reconstruye la credencial a partir de los tokens almacenados en la base de datos.
     * La librería de Google se encarga de refrescar el token de acceso si ha expirado.
     *
     * @param crmUserId El ID del usuario en la base de datos del CRM.
     * @return Una instancia de Gmail lista para usar.
     * @throws IOException Si hay un error al leer los archivos de configuración o de red.
     * @throws GeneralSecurityException Si hay un error de seguridad en el transporte HTTP.
     * @throws IllegalStateException Si el usuario no ha conectado su cuenta de Google.
     */
    private Gmail getGmailServiceForUser(Long crmUserId) throws IOException, GeneralSecurityException {
        User crmUser = userRepository.findById(crmUserId)
                .orElseThrow(() -> new IllegalStateException("Usuario del CRM no encontrado: " + crmUserId));

        if (crmUser.getGoogleRefreshToken() == null || crmUser.getGoogleRefreshToken().isEmpty()) {
            throw new IllegalStateException("El usuario " + crmUser.getUsername() + " no ha conectado su cuenta de Google.");
        }

        InputStream in = new ClassPathResource(credentialsFilePath).getInputStream();
        GoogleClientSecrets clientSecrets = GoogleClientSecrets.load(JSON_FACTORY, new InputStreamReader(in));

        Credential credential = new GoogleCredential.Builder()
                .setTransport(GoogleNetHttpTransport.newTrustedTransport())
                .setJsonFactory(JSON_FACTORY)
                .setClientSecrets(clientSecrets.getDetails().getClientId(), clientSecrets.getDetails().getClientSecret())
                .build();

        credential.setAccessToken(crmUser.getGoogleAccessToken());
        credential.setRefreshToken(crmUser.getGoogleRefreshToken());
        credential.setExpiresInSeconds((crmUser.getGoogleTokenExpiryTime() - System.currentTimeMillis()) / 1000);

        final NetHttpTransport HTTP_TRANSPORT = GoogleNetHttpTransport.newTrustedTransport();
        return new Gmail.Builder(HTTP_TRANSPORT, JSON_FACTORY, credential)
                .setApplicationName(APPLICATION_NAME)
                .build();
    }

    // --- MÉTODOS PRIVADOS DE AYUDA (SIN CAMBIOS) ---

    private Message crearMensajeMime(String remitente, String destinatario, String asunto, String cuerpo) throws MessagingException, IOException {
        Properties props = new Properties();
        Session session = Session.getDefaultInstance(props, null);
        MimeMessage mimeMessage = new MimeMessage(session);
        mimeMessage.setFrom(new InternetAddress(remitente));
        mimeMessage.addRecipient(jakarta.mail.Message.RecipientType.TO, new InternetAddress(destinatario));
        mimeMessage.setSubject(asunto, "UTF-8");
        mimeMessage.setText(cuerpo, "UTF-8", "html");

        ByteArrayOutputStream buffer = new ByteArrayOutputStream();
        mimeMessage.writeTo(buffer);
        byte[] bytes = buffer.toByteArray();
        String encodedEmail = Base64.getUrlEncoder().encodeToString(bytes);

        Message message = new Message();
        message.setRaw(encodedEmail);
        return message;
    }

    private EmailDtos.EmailMensajeResumen crearResumenDesdeMensaje(Message messageDetails) {
        String de = getCabecera(messageDetails.getPayload().getHeaders(), "From").orElse("N/A");
        String asunto = getCabecera(messageDetails.getPayload().getHeaders(), "Subject").orElse("(Sin asunto)");
        String fecha = getCabecera(messageDetails.getPayload().getHeaders(), "Date").orElse("N/A");
        return new EmailDtos.EmailMensajeResumen(messageDetails.getId(), de, asunto, messageDetails.getSnippet(), fecha);
    }

    private Optional<String> getCabecera(List<MessagePartHeader> cabeceras, String nombre) {
        if (cabeceras == null) return Optional.empty();
        return cabeceras.stream()
                .filter(h -> h.getName().equalsIgnoreCase(nombre))
                .map(MessagePartHeader::getValue)
                .findFirst();
    }

    private record ParsedPayload(String body, List<EmailDtos.AdjuntoDetalle> attachments) {}

    private ParsedPayload parsearCuerpoYAdjuntos(MessagePart startPart) {
        StringBuilder body = new StringBuilder();
        List<EmailDtos.AdjuntoDetalle> attachments = new ArrayList<>();
        parsearParteRecursivamente(startPart, body, attachments);
        return new ParsedPayload(body.toString(), attachments);
    }

    private void parsearParteRecursivamente(MessagePart part, StringBuilder body, List<EmailDtos.AdjuntoDetalle> attachments) {
        if (part == null) return;
        String mimeType = part.getMimeType();

        if (part.getParts() != null && !part.getParts().isEmpty()) {
            if ("multipart/alternative".equalsIgnoreCase(mimeType)) {
                part.getParts().stream()
                        .filter(p -> "text/html".equalsIgnoreCase(p.getMimeType())).findFirst()
                        .ifPresentOrElse(
                                htmlPart -> parsearParteRecursivamente(htmlPart, body, attachments),
                                () -> part.getParts().stream()
                                        .filter(p -> "text/plain".equalsIgnoreCase(p.getMimeType())).findFirst()
                                        .ifPresent(plainPart -> parsearParteRecursivamente(plainPart, body, attachments))
                        );
            } else {
                for (MessagePart subPart : part.getParts()) {
                    parsearParteRecursivamente(subPart, body, attachments);
                }
            }
        } else if (part.getBody() != null && part.getBody().getData() != null) {
            if ("text/html".equalsIgnoreCase(mimeType) || "text/plain".equalsIgnoreCase(mimeType)) {
                body.append(new String(Base64.getUrlDecoder().decode(part.getBody().getData()), StandardCharsets.UTF_8));
            }
        }

        if (part.getFilename() != null && !part.getFilename().isEmpty() && part.getBody() != null && part.getBody().getAttachmentId() != null) {
            attachments.add(new EmailDtos.AdjuntoDetalle(
                    part.getBody().getAttachmentId(),
                    part.getFilename(),
                    part.getMimeType(),
                    part.getBody().getSize()
            ));
        }
    }
}
// Estilos específicos para la bandeja de entrada
.mensaje-item {
  transition: all 0.2s ease-in-out;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

// Estilos para el avatar
.avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

// Responsive design
@media (max-width: 768px) {
  .mensaje-item {
    padding: 0.75rem;
  }
  
  .avatar {
    width: 2rem;
    height: 2rem;
  }
  
  .mensaje-content {
    font-size: 0.875rem;
  }
}

// Dark mode adjustments
@media (prefers-color-scheme: dark) {
  .mensaje-item:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }
}

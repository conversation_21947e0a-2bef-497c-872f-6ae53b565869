package com.midas.crm.repository;

import com.midas.crm.entity.Pregunta;
import com.midas.crm.entity.Respuesta;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RespuestaRepository extends JpaRepository<Respuesta, Long> {
    List<Respuesta> findByPreguntaOrderByOrdenAsc(Pregunta pregunta);
    List<Respuesta> findByPreguntaIdOrderByOrdenAsc(Long preguntaId);
    List<Respuesta> findByPreguntaIdAndEsCorrectaTrue(Long preguntaId);
    int countByPreguntaId(Long preguntaId);
}

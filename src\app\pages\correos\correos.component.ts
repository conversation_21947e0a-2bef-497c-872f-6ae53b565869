import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { GmailService } from './services/gmail.service';
import { EstadoConexionGmail } from './models/gmail.models';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-correos',
  templateUrl: './correos.component.html',
  styleUrls: ['./correos.component.scss']
})
export class CorreosComponent implements OnInit {
  estadoConexion: EstadoConexionGmail | null = null;
  cargando = true;
  error: string | null = null;
  cargandoConexion = false;

  constructor(
    private gmailService: GmailService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.verificarConexionGmail();
  }

  verificarConexionGmail(): void {
    this.cargando = true;
    this.error = null;

    this.gmailService.verificarConexion().subscribe({
      next: (response) => {
        if (response.rpta === 1) {
          this.estadoConexion = response.data;
          
          // Si está conectado, redirigir a la bandeja de entrada
          if (this.estadoConexion.conectado) {
            this.router.navigate(['/correos/bandeja-entrada']);
          }
        } else {
          this.error = response.msg || 'Error al verificar la conexión';
        }
        this.cargando = false;
      },
      error: (error) => {
        console.error('Error al verificar conexión Gmail:', error);
        this.error = 'Error de conexión. Intente nuevamente.';
        this.cargando = false;
      }
    });
  }

  conectarGmail(): void {
    this.cargandoConexion = true;

    // Mostrar mensaje informativo antes de redirigir
    Swal.fire({
      title: 'Conectando Gmail',
      text: 'Será redirigido a Google para autorizar el acceso a su cuenta de Gmail.',
      icon: 'info',
      confirmButtonText: 'Continuar',
      confirmButtonColor: '#3b82f6'
    }).then((result) => {
      if (result.isConfirmed) {
        // Usar el nuevo método que obtiene la URL del backend
        this.gmailService.iniciarAutorizacionOAuth().subscribe({
          next: () => {
            console.log('Proceso OAuth iniciado - redirigiendo a Google');
            // La redirección ya se realizó en el servicio
          },
          error: (error) => {
            console.error('Error al iniciar autorización:', error);
            this.cargandoConexion = false;

            Swal.fire({
              title: 'Error',
              text: error.message || 'Error al iniciar el proceso de autorización. Intente nuevamente.',
              icon: 'error',
              confirmButtonColor: '#3b82f6'
            });
          }
        });
      } else {
        this.cargandoConexion = false;
      }
    });
  }

  recargarPagina(): void {
    window.location.reload();
  }
}

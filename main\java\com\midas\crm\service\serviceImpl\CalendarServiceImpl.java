package com.midas.crm.service.serviceImpl;

import com.midas.crm.entity.Calendar;
import com.midas.crm.entity.DTO.calendar.CalendarDTO;
import com.midas.crm.entity.DTO.calendar.CalendarResponseDTO;
import com.midas.crm.entity.User;
import com.midas.crm.mapper.CalendarMapper;
import com.midas.crm.repository.CalendarRepository;
import com.midas.crm.repository.UserRepository;
import com.midas.crm.service.CalendarService;
import com.midas.crm.utils.GenericResponse;
import com.midas.crm.validator.CalendarValidator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

@Service
@RequiredArgsConstructor
@Slf4j
public class CalendarServiceImpl implements CalendarService {

    private final CalendarRepository calendarRepository;
    private final UserRepository userRepository;
    private final CalendarValidator calendarValidator;

    /* ---------- L1 cache ---------- */
    private final Map<Long, Calendar> calendarL1 = new ConcurrentHashMap<>();
    private final Map<String, List<Calendar>> listL1 = new ConcurrentHashMap<>();
    private final Map<String, List<CalendarResponseDTO>> responseListL1 = new ConcurrentHashMap<>();

    @Override
    @Transactional(readOnly = true)
    @Cacheable(cacheNames = "calendarList", key = "'all'")
    public GenericResponse<List<Calendar>> getAll() {
        try {
            List<Calendar> result = listL1.computeIfAbsent("all", k -> {
                return calendarRepository.findByDeletedFalse();
            });
            return new GenericResponse<>(1, "Agendas listadas correctamente", result);
        } catch (Exception e) {
            return new GenericResponse<>(0, "Error al listar las agendas: " + e.getMessage(), null);
        }
    }

    @Override
    @Transactional(readOnly = true)
    @Cacheable(cacheNames = "calendarList", key = "{'user',#userId}")
    public GenericResponse<List<Calendar>> getFilterByUser(Long userId) {
        try {
            String cacheKey = "user:" + userId;
            List<Calendar> result = listL1.computeIfAbsent(cacheKey, k -> {
                return calendarRepository.findByUserCreateId(userId);
            });
            return new GenericResponse<>(1, "Agendas del usuario listadas correctamente", result);
        } catch (Exception e) {
            return new GenericResponse<>(0, "Error al listar las agendas: " + e.getMessage(), null);
        }
    }


    private CalendarResponseDTO mapToResponseDTO(Calendar c) {
        return CalendarMapper.toResponseDTO(c);
    }


    @Override
    public GenericResponse<List<CalendarResponseDTO>> getFilterByDates(CalendarDTO filter) {
        try {
            if (filter.getFechaInicio() == null || filter.getFechaFinal() == null) {
                return new GenericResponse<>(0, "Las fechas son obligatorias", null);
            }

            List<CalendarResponseDTO> result = calendarRepository.findAll().stream()
                    .filter(c -> !Boolean.TRUE.equals(c.getDeleted()))
                    .filter(c -> !c.getFechaInicio().isAfter(filter.getFechaFinal()))
                    .filter(c -> !c.getFechaFinal().isBefore(filter.getFechaInicio()))
                    .filter(c -> filter.getUserCreateId() == null ||
                            (c.getUserCreate() != null && c.getUserCreate().getId().equals(filter.getUserCreateId())))
                    .map(this::mapToResponseDTO)
                    .toList();

            return new GenericResponse<>(1, "Agendas filtradas correctamente", result);
        } catch (Exception e) {
            e.printStackTrace();  // para debug
            return new GenericResponse<>(0, "Operación fallida", null);
        }
    }



    @Override
    @Transactional(readOnly = true)
    @Cacheable(cacheNames = "calendarById", key = "#id")
    public GenericResponse<Calendar> getById(Long id) {
        try {
            Calendar result = calendarL1.computeIfAbsent(id, k -> {
                return calendarRepository.findById(k)
                        .filter(c -> !Boolean.TRUE.equals(c.getDeleted()))
                        .orElse(null);
            });

            if (result == null) {
                return new GenericResponse<>(0, "Agenda no encontrada", null);
            }

            return new GenericResponse<>(1, "Agenda obtenida correctamente", result);
        } catch (Exception e) {
            return new GenericResponse<>(0, "Error al obtener la agenda: " + e.getMessage(), null);
        }
    }

    @Override
    @Transactional
    @CacheEvict(cacheNames = {"calendarList"}, allEntries = true)
    public GenericResponse<?> create(CalendarDTO dto) {
        try {
            var errors = calendarValidator.validate(dto);
            if (!errors.isEmpty()) {
                return new GenericResponse<>(0, "Errores de validación", errors);
            }
            Calendar result = CalendarMapper.toEntity(dto, userRepository);
            result = calendarRepository.save(result);
            clearL1(); // Limpiar todo el cache L1 después de crear (afecta listas)
            return new GenericResponse<>(1, "Agenda creada correctamente", result);
        } catch (Exception e) {
            return new GenericResponse<>(0, "Error al crear la agenda: " + e.getMessage(), null);
        }
    }

    @Override
    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames = "calendarById", key = "#id"),
            @CacheEvict(cacheNames = "calendarList", allEntries = true)
    })
    public GenericResponse<?> update(CalendarDTO dto, Long id) {
        try {
            var errors = calendarValidator.validate(dto);
            if (!errors.isEmpty()) {
                return new GenericResponse<>(0, "Errores de validación", errors);
            }

            Optional<Calendar> optional = calendarRepository.findById(id);
            if (optional.isEmpty()) {
                return new GenericResponse<>(0, "Agenda no encontrada para actualizar", null);
            }

            Calendar calendar = optional.get();
            CalendarMapper.mapDtoToEntity(dto, calendar, userRepository);
            Calendar result = calendarRepository.save(calendar);
            invalidate(id); // Invalidar cache específico después de actualizar
            return new GenericResponse<>(1, "Agenda actualizada correctamente", result);
        } catch (Exception e) {
            return new GenericResponse<>(0, "Error al actualizar la agenda: " + e.getMessage(), null);
        }
    }

    @Override
    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames = "calendarById", key = "#id"),
            @CacheEvict(cacheNames = "calendarList", allEntries = true)
    })
    public GenericResponse<String> delete(Long id) {
        try {
            Optional<Calendar> optional = calendarRepository.findById(id);
            if (optional.isPresent()) {
                Calendar calendar = optional.get();
                calendar.setDeleted(true);
                calendarRepository.save(calendar);
                invalidate(id); // Invalidar cache específico después de eliminar
                return new GenericResponse<>(1, "Agenda eliminada correctamente", "Eliminado");
            } else {
                return new GenericResponse<>(0, "No se encontró la agenda", null);
            }
        } catch (Exception e) {
            return new GenericResponse<>(0, "Error al eliminar la agenda: " + e.getMessage(), null);
        }
    }

    @Override
    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames = "calendarById", key = "#id"),
            @CacheEvict(cacheNames = "calendarList", allEntries = true)
    })
    public GenericResponse<String> restore(Long id) {
        try {
            Optional<Calendar> optional = calendarRepository.findById(id);
            if (optional.isPresent()) {
                Calendar calendar = optional.get();
                if (Boolean.FALSE.equals(calendar.getDeleted())) {
                    return new GenericResponse<>(0, "La agenda ya fue restaurada previamente", null);
                }
                calendar.setDeleted(false);
                calendarRepository.save(calendar);
                invalidate(id); // Invalidar cache específico después de restaurar
                return new GenericResponse<>(1, "Agenda restaurada correctamente", "Restaurado");
            } else {
                return new GenericResponse<>(0, "No se encontró la agenda", null);
            }
        } catch (Exception e) {
            return new GenericResponse<>(0, "Error al restaurar la agenda: " + e.getMessage(), null);
        }
    }

    /* ---------- Cache management ---------- */
    private void clearL1() {
        calendarL1.clear();
        listL1.clear();
        responseListL1.clear();
    }

    private void invalidate(Long id) {
        calendarL1.remove(id);
        listL1.clear(); // Limpiar todas las listas ya que pueden contener el elemento modificado
        responseListL1.clear();
    }

}
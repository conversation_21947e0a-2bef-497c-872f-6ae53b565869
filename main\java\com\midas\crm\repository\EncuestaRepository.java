package com.midas.crm.repository;

import com.midas.crm.entity.Encuesta;
import com.midas.crm.entity.Sede;
import com.midas.crm.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface EncuestaRepository extends JpaRepository<Encuesta, Long> {

    // Buscar encuestas por estado
    List<Encuesta> findByEstado(String estado);

    // Buscar encuestas activas
    @Query("SELECT e FROM Encuesta e WHERE e.estado = 'A'")
    List<Encuesta> findAllActive();

    // Buscar encuestas por tipo de asignación
    List<Encuesta> findByTipoAsignacion(Encuesta.TipoAsignacion tipoAsignacion);

    // Buscar encuestas por sede
    List<Encuesta> findBySede(Sede sede);
    List<Encuesta> findBySedeAndEstado(Sede sede, String estado);

    // Buscar encuestas por coordinador
    List<Encuesta> findByCoordinador(User coordinador);
    List<Encuesta> findByCoordinadorAndEstado(User coordinador, String estado);

    // Buscar encuestas por usuario específico
    List<Encuesta> findByUsuario(User usuario);
    List<Encuesta> findByUsuarioAndEstado(User usuario, String estado);

    // Buscar encuestas por creador
    List<Encuesta> findByCreador(User creador);
    List<Encuesta> findByCreadorAndEstado(User creador, String estado);

    // Buscar encuestas disponibles para un usuario específico
    @Query("SELECT e FROM Encuesta e WHERE e.estado = 'A' AND " +
            "(e.tipoAsignacion = 'TODOS' OR " +
            "(e.tipoAsignacion = 'SEDE' AND e.sede.id = :sedeId) OR " +
            "(e.tipoAsignacion = 'COORDINACION' AND e.coordinador.id = :coordinadorId) OR " +
            "(e.tipoAsignacion = 'PERSONAL' AND (e.usuario.id = :usuarioId OR " +
            "(e.usuarioIds IS NOT NULL AND CONCAT(',', e.usuarioIds, ',') LIKE CONCAT('%,', :usuarioId, ',%')))))")
    List<Encuesta> findEncuestasDisponiblesParaUsuario(
            @Param("sedeId") Long sedeId,
            @Param("coordinadorId") Long coordinadorId,
            @Param("usuarioId") Long usuarioId);

    // Buscar encuestas activas y vigentes (dentro del rango de fechas)
    @Query("SELECT e FROM Encuesta e WHERE e.estado = 'A' AND " +
            "(:fechaActual BETWEEN e.fechaInicio AND e.fechaFin OR " +
            "(e.fechaInicio <= :fechaActual AND e.fechaFin IS NULL) OR " +
            "(e.fechaInicio IS NULL AND e.fechaFin >= :fechaActual) OR " +
            "(e.fechaInicio IS NULL AND e.fechaFin IS NULL))")
    List<Encuesta> findEncuestasVigentes(@Param("fechaActual") LocalDateTime fechaActual);

    // Buscar encuestas con paginación y filtro
    @Query("SELECT e FROM Encuesta e WHERE " +
            "(:search IS NULL OR LOWER(e.titulo) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
            "LOWER(e.descripcion) LIKE LOWER(CONCAT('%', :search, '%')))")
    Page<Encuesta> findBySearchTerm(@Param("search") String search, Pageable pageable);
}

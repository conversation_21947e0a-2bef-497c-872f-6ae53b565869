package com.midas.crm.repository;

import com.midas.crm.entity.Leccion;
import com.midas.crm.entity.VideoInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface VideoInfoRepository extends JpaRepository<VideoInfo, Long> {
    Optional<VideoInfo> findByLeccion(Leccion leccion);
    Optional<VideoInfo> findByLeccionId(Long leccionId);
}

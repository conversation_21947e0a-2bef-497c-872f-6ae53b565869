package com.midas.crm.repository;

import com.midas.crm.entity.Cuestionario;
import com.midas.crm.entity.Leccion;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface CuestionarioRepository extends JpaRepository<Cuestionario, Long> {
    Optional<Cuestionario> findByLeccion(Leccion leccion);
    Optional<Cuestionario> findByLeccionId(Long leccionId);
    boolean existsByLeccionId(Long leccionId);
}

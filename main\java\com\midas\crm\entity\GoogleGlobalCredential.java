package com.midas.crm.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Lob;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "google_global_credential")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GoogleGlobalCredential {

    @Id
    private String id; // Un ID fijo, por ejemplo "DRIVE_API_TOKEN"

    @Lob
    @Column(columnDefinition = "TEXT")
    private String accessToken;

    @Lob
    @Column(columnDefinition = "TEXT")
    private String refreshToken;

    private Long expiryTimeMillis;
}
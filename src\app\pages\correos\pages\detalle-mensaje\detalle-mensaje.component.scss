// Estilos específicos para el detalle del mensaje
.mensaje-header {
  border-bottom: 1px solid #e5e7eb;
}

.adjunto-item {
  transition: all 0.2s ease-in-out;
  
  &:hover {
    background-color: #f3f4f6;
  }
}

// Estilos para el contenido del email
.prose {
  // Asegurar que las imágenes no se desborden
  img {
    max-width: 100%;
    height: auto;
  }
  
  // Estilos para tablas en emails
  table {
    width: 100%;
    border-collapse: collapse;
    margin: 1rem 0;
  }
  
  th, td {
    border: 1px solid #e5e7eb;
    padding: 0.5rem;
    text-align: left;
  }
  
  th {
    background-color: #f9fafb;
    font-weight: 600;
  }
  
  // Estilos para enlaces
  a {
    color: #3b82f6;
    text-decoration: underline;
    
    &:hover {
      color: #1d4ed8;
    }
  }
  
  // Estilos para citas
  blockquote {
    border-left: 4px solid #e5e7eb;
    padding-left: 1rem;
    margin: 1rem 0;
    font-style: italic;
    color: #6b7280;
  }
}

// Dark mode adjustments
@media (prefers-color-scheme: dark) {
  .adjunto-item:hover {
    background-color: #374151;
  }
  
  .prose {
    th {
      background-color: #374151;
    }
    
    th, td {
      border-color: #4b5563;
    }
    
    blockquote {
      border-left-color: #4b5563;
      color: #9ca3af;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .mensaje-header {
    padding: 1rem;
  }
  
  .adjunto-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .prose {
    font-size: 0.875rem;
  }
}

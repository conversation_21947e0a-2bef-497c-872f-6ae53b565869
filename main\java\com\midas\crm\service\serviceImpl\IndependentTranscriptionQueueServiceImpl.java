package com.midas.crm.service.serviceImpl;

import com.midas.crm.config.RabbitMQConfig;
import com.midas.crm.entity.DTO.queue.IndependentTranscriptionQueueMessage;
import com.midas.crm.entity.DTO.queue.IndependentTranscriptionQueueMessage.AudioFileInfo;
import com.midas.crm.service.IndependentTranscriptionQueueService;
import com.midas.crm.service.GoogleDriveService;
import com.midas.crm.service.AudioConversionService;
import com.midas.crm.entity.DTO.GoogleDriveFileDTO;
import com.midas.crm.utils.GoogleDriveOrganizationHelper;
import com.midas.crm.utils.GoogleDriveOrganizationHelper.FolderType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Implementación del servicio de colas de transcripciones independientes
 */
@Service
public class IndependentTranscriptionQueueServiceImpl implements IndependentTranscriptionQueueService {

    private static final Logger log = LoggerFactory.getLogger(IndependentTranscriptionQueueServiceImpl.class);

    private final RabbitTemplate rabbitTemplate;
    private final GoogleDriveService googleDriveService;
    private final RestTemplate restTemplate;
    private final AudioConversionService audioConversionService;
    private final GoogleDriveOrganizationHelper driveOrganizationHelper;

    // Constructor manual para evitar problemas con múltiples beans
    public IndependentTranscriptionQueueServiceImpl(
            RabbitTemplate rabbitTemplate,
            GoogleDriveService googleDriveService,
            @Qualifier("transcriptionRestTemplate") RestTemplate restTemplate,
            AudioConversionService audioConversionService,
            GoogleDriveOrganizationHelper driveOrganizationHelper) {
        this.rabbitTemplate = rabbitTemplate;
        this.googleDriveService = googleDriveService;
        this.restTemplate = restTemplate;
        this.audioConversionService = audioConversionService;
        this.driveOrganizationHelper = driveOrganizationHelper;
    }

    // Cache en memoria para el estado de los lotes (en producción usar Redis)
    private final Map<String, IndependentTranscriptionQueueMessage> batchCache = new ConcurrentHashMap<>();

    // Control de procesamiento
    private final AtomicBoolean processingActive = new AtomicBoolean(true);

    @Value("${transcription.api.url:https://gni8cguopmely9-8000.proxy.runpod.net/api/transcribe/}")
    private String transcriptionApiUrl;

    @Override
    public String processMultipleFiles(List<GoogleDriveFileDTO> googleDriveFiles, String userId, String userName,
                                       String whisperModel, String device, String targetLanguage,
                                       List<String> tags, String notes) {

        // 🔍 VALIDACIONES PARA EVITAR ERRORES 500
        if (googleDriveFiles == null || googleDriveFiles.isEmpty()) {
            log.error("❌ Lista de archivos vacía o nula");
            throw new IllegalArgumentException("La lista de archivos no puede estar vacía");
        }

        if (userId == null || userId.trim().isEmpty()) {
            log.error("❌ User ID vacío o nulo");
            throw new IllegalArgumentException("El User ID es requerido");
        }

        if (userName == null || userName.trim().isEmpty()) {
            log.error("❌ User Name vacío o nulo");
            throw new IllegalArgumentException("El User Name es requerido");
        }

        log.info("📋 Iniciando procesamiento de lote: {} archivos para usuario {}",
                googleDriveFiles.size(), userName);

        String batchId = UUID.randomUUID().toString();

        // Crear información de archivos
        List<AudioFileInfo> audioFiles = new ArrayList<>();
        for (GoogleDriveFileDTO file : googleDriveFiles) {
            AudioFileInfo audioFile = AudioFileInfo.builder()
                    .fileId(UUID.randomUUID().toString())
                    .fileName(file.getName())
                    .originalFileName(file.getName())
                    .googleDriveFileId(file.getId())
                    .googleDriveUrl(file.getWebContentLink())
                    .fileSize(file.getSize())
                    .mimeType(file.getMimeType())
                    .status("PENDING")
                    .build();
            audioFiles.add(audioFile);
        }

        // Crear mensaje de lote
        IndependentTranscriptionQueueMessage batchMessage = IndependentTranscriptionQueueMessage.builder()
                .batchId(batchId)
                .userId(userId)
                .userName(userName)
                .audioFiles(audioFiles)
                .totalFiles(googleDriveFiles.size())
                .processedFiles(0)
                .failedFiles(0)
                .whisperModel(whisperModel != null ? whisperModel : "medium")
                .device(device != null ? device : "gpu")
                .targetLanguage(targetLanguage != null ? targetLanguage : "es")
                .tags(tags)
                .notes(notes)
                .estadoProcesamiento("PENDING")
                .fechaEnvio(LocalDateTime.now())
                .intentos(0)
                .maxIntentos(3)
                .queueName(RabbitMQConfig.INDEPENDENT_TRANSCRIPTION_QUEUE)
                .routingKey(RabbitMQConfig.INDEPENDENT_TRANSCRIPTION_ROUTING_KEY)
                .maxConcurrentProcessing(1)  // 🔄 CAMBIO: Procesar de 1 en 1 en lugar de 3 en 3
                .currentlyProcessing(0)
                .build();

        // Guardar en cache
        batchCache.put(batchId, batchMessage);

        // Enviar a la cola
        rabbitTemplate.convertAndSend(
                RabbitMQConfig.INDEPENDENT_TRANSCRIPTION_EXCHANGE,
                RabbitMQConfig.INDEPENDENT_TRANSCRIPTION_ROUTING_KEY,
                batchMessage
        );
        return batchId;
    }

    @Override
    public String processSingleFile(GoogleDriveFileDTO googleDriveFile, String userId, String userName,
                                    String whisperModel, String device, String targetLanguage,
                                    List<String> tags, String notes) {

        // Aquí se podría llamar directamente a la API de transcripción
        // o crear un lote de un solo archivo
        return processMultipleFiles(List.of(googleDriveFile), userId, userName,
                whisperModel, device, targetLanguage, tags, notes);
    }

    @Override
    public IndependentTranscriptionQueueMessage getBatchStatus(String batchId) {
        return batchCache.get(batchId);
    }

    @Override
    public Map<String, Object> getQueueStatistics() {
        Map<String, Object> stats = new HashMap<>();

        long totalBatches = batchCache.size();
        long activeBatches = batchCache.values().stream()
                .filter(batch -> "PROCESSING".equals(batch.getEstadoProcesamiento()) ||
                        "PENDING".equals(batch.getEstadoProcesamiento()))
                .count();
        long completedBatches = batchCache.values().stream()
                .filter(batch -> "COMPLETED".equals(batch.getEstadoProcesamiento()))
                .count();
        long failedBatches = batchCache.values().stream()
                .filter(batch -> "FAILED".equals(batch.getEstadoProcesamiento()))
                .count();

        stats.put("totalBatches", totalBatches);
        stats.put("activeBatches", activeBatches);
        stats.put("completedBatches", completedBatches);
        stats.put("failedBatches", failedBatches);
        stats.put("processingActive", processingActive.get());
        stats.put("timestamp", LocalDateTime.now());

        return stats;
    }

    @Override
    public boolean cancelBatch(String batchId) {
        IndependentTranscriptionQueueMessage batch = batchCache.get(batchId);
        if (batch != null) {
            batch.setEstadoProcesamiento("CANCELLED");
            batch.setFechaFinProcesamiento(LocalDateTime.now());
            return true;
        }
        return false;
    }

    @Override
    public boolean retryFailedFiles(String batchId) {
        IndependentTranscriptionQueueMessage batch = batchCache.get(batchId);
        if (batch != null && batch.getAudioFiles() != null) {
            // Reiniciar archivos fallidos
            batch.getAudioFiles().stream()
                    .filter(file -> "FAILED".equals(file.getStatus()))
                    .forEach(file -> {
                        file.setStatus("PENDING");
                        file.setErrorMessage(null);
                        file.setStartTime(null);
                        file.setEndTime(null);
                    });

            // Reiniciar contadores
            batch.setFailedFiles(0);
            batch.setEstadoProcesamiento("PENDING");
            batch.incrementarIntentos();

            // Reenviar a la cola
            rabbitTemplate.convertAndSend(
                    RabbitMQConfig.INDEPENDENT_TRANSCRIPTION_EXCHANGE,
                    RabbitMQConfig.INDEPENDENT_TRANSCRIPTION_ROUTING_KEY,
                    batch
            );
            return true;
        }
        return false;
    }

    @Override
    public List<IndependentTranscriptionQueueMessage> getUserBatches(String userId, int page, int size) {
        return batchCache.values().stream()
                .filter(batch -> userId.equals(batch.getUserId()))
                .skip((long) page * size)
                .limit(size)
                .toList();
    }

    @Override
    public List<IndependentTranscriptionQueueMessage> getActiveBatches(int page, int size) {
        return batchCache.values().stream()
                .filter(batch -> "PROCESSING".equals(batch.getEstadoProcesamiento()) ||
                        "PENDING".equals(batch.getEstadoProcesamiento()))
                .skip((long) page * size)
                .limit(size)
                .toList();
    }

    @Override
    public void pauseProcessing() {
        processingActive.set(false);
    }

    @Override
    public void resumeProcessing() {
        processingActive.set(true);
    }

    @Override
    public boolean isProcessingActive() {
        return processingActive.get();
    }

    @Override
    public int cleanupOldBatches(int daysOld) {
        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(daysOld);
        List<String> toRemove = new ArrayList<>();

        batchCache.entrySet().stream()
                .filter(entry -> {
                    IndependentTranscriptionQueueMessage batch = entry.getValue();
                    return batch.getFechaFinProcesamiento() != null &&
                            batch.getFechaFinProcesamiento().isBefore(cutoffDate) &&
                            ("COMPLETED".equals(batch.getEstadoProcesamiento()) ||
                                    "FAILED".equals(batch.getEstadoProcesamiento()) ||
                                    "CANCELLED".equals(batch.getEstadoProcesamiento()));
                })
                .forEach(entry -> toRemove.add(entry.getKey()));

        toRemove.forEach(batchCache::remove);
        return toRemove.size();
    }

    /**
     * Listener de RabbitMQ para procesar lotes de transcripciones independientes
     * MEJORADO CON VALIDACIONES Y MANEJO DE ERRORES
     */
    @RabbitListener(queues = RabbitMQConfig.INDEPENDENT_TRANSCRIPTION_QUEUE)
    public void processIndependentTranscriptionBatch(IndependentTranscriptionQueueMessage batchMessage) {
        if (!processingActive.get()) {
            log.warn("⏸️ Procesamiento pausado, reenvío mensaje a la cola");
            rabbitTemplate.convertAndSend(
                    RabbitMQConfig.INDEPENDENT_TRANSCRIPTION_EXCHANGE,
                    RabbitMQConfig.INDEPENDENT_TRANSCRIPTION_ROUTING_KEY,
                    batchMessage
            );
            return;
        }

        // 🔍 VALIDACIONES CRÍTICAS
        if (batchMessage == null) {
            log.error("❌ Mensaje de lote nulo recibido");
            return;
        }

        String batchId = batchMessage.getBatchId();
        if (batchId == null || batchId.trim().isEmpty()) {
            log.error("❌ Batch ID nulo o vacío en mensaje");
            return;
        }

        if (batchMessage.getAudioFiles() == null || batchMessage.getAudioFiles().isEmpty()) {
            log.error("❌ Lista de archivos vacía en lote {}", batchId);
            batchMessage.setEstadoProcesamiento("FAILED");
            batchMessage.setMensajeError("Lista de archivos vacía");
            batchMessage.setFechaFinProcesamiento(LocalDateTime.now());
            batchCache.put(batchId, batchMessage);
            return;
        }

        log.info("📋 Procesando lote {} con {} archivos", batchId, batchMessage.getAudioFiles().size());

        try {
            // Actualizar estado del lote
            batchMessage.setEstadoProcesamiento("PROCESSING");
            batchMessage.setFechaInicioProcesamiento(LocalDateTime.now());
            batchCache.put(batchId, batchMessage);

            // 🔄 CAMBIO: Procesar archivos de 1 en 1 para mayor control
            processFilesInBatch(batchMessage);

        } catch (Exception e) {
            log.error("❌ Error crítico procesando lote {}: {}", batchId, e.getMessage(), e);
            batchMessage.setEstadoProcesamiento("FAILED");
            batchMessage.setMensajeError("Error crítico: " + e.getMessage());
            batchMessage.setFechaFinProcesamiento(LocalDateTime.now());
            batchCache.put(batchId, batchMessage);
        }
    }

    /**
     * Procesa los archivos del lote de forma secuencial (1 por 1 para mayor control)
     */
    private void processFilesInBatch(IndependentTranscriptionQueueMessage batchMessage) {
        String batchId = batchMessage.getBatchId();

        while (batchMessage.canProcessMoreFiles() && processingActive.get()) {
            // 🔄 PROCESAMIENTO SECUENCIAL: 1 archivo por vez
            AudioFileInfo nextFile = batchMessage.getNextPendingFile();
            if (nextFile != null) {
                log.info("📋 Procesando archivo: {} (ID: {})", nextFile.getFileName(), nextFile.getFileId());

                batchMessage.markFileAsProcessing(nextFile.getFileId());

                try {
                    processIndividualFile(batchMessage, nextFile);
                } catch (Exception e) {
                    log.error("❌ Error procesando archivo {}: {}", nextFile.getFileName(), e.getMessage(), e);
                    batchMessage.markFileAsFailed(nextFile.getFileId(), e.getMessage());
                } finally {
                    batchMessage.decrementCurrentlyProcessing();
                }
            }

            // Actualizar cache
            batchCache.put(batchId, batchMessage);

            // Pequeña pausa para evitar sobrecarga
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }

        // Verificar si el lote está completo
        if (batchMessage.isCompleto()) {
            batchMessage.setEstadoProcesamiento("COMPLETED");
            batchMessage.setFechaFinProcesamiento(LocalDateTime.now());
            batchCache.put(batchId, batchMessage);
        }
    }

    /**
     * Procesa un archivo individual - VERSIÓN SIMPLIFICADA
     * El frontend ya maneja la conversión GSM a MP3, solo necesitamos transcribir
     */
    private void processIndividualFile(IndependentTranscriptionQueueMessage batchMessage, AudioFileInfo file) {
        String fileId = file.getFileId();
        String fileName = file.getFileName();

        log.info("🎵 Iniciando procesamiento de archivo independiente: {} (ID: {})", fileName, fileId);

        try {
            // 🔍 VALIDACIONES ADICIONALES
            if (file.getGoogleDriveFileId() == null || file.getGoogleDriveFileId().trim().isEmpty()) {
                throw new IllegalArgumentException("Google Drive File ID no puede estar vacío");
            }

            if (batchMessage.getUserName() == null || batchMessage.getUserName().trim().isEmpty()) {
                throw new IllegalArgumentException("User Name no puede estar vacío");
            }

            // 1. Generar Call ID único para este archivo
            String callId = generateCallId(fileName);
            log.info("🆔 Call ID generado para archivo independiente: {}", callId);

            // 2. Obtener URL directa del archivo desde Google Drive
            String audioUrl = googleDriveService.getDirectDownloadUrl(file.getGoogleDriveFileId());
            log.info("📁 URL de descarga obtenida: {}", audioUrl != null ? "✅" : "❌");

            if (audioUrl == null || audioUrl.trim().isEmpty()) {
                throw new RuntimeException("No se pudo obtener URL de descarga del archivo: " + file.getGoogleDriveFileId());
            }

            // 3. Llamar directamente a la API de transcripción con URL
            Map<String, Object> transcriptionResult = callTranscriptionAPIDirectly(
                    audioUrl, fileName, callId,
                    batchMessage.getWhisperModel(),
                    batchMessage.getDevice(),
                    batchMessage.getTargetLanguage()
            );

            // 4. Extraer resultados del formato de respuesta
            if (transcriptionResult != null && Boolean.TRUE.equals(transcriptionResult.get("success"))) {
                String transcriptionText = (String) transcriptionResult.get("transcription");
                String language = (String) transcriptionResult.get("language_detected");
                Double confidence = (Double) transcriptionResult.get("mood_confidence");

                log.info("✅ Transcripción exitosa para archivo {}: {} caracteres",
                        fileName, transcriptionText != null ? transcriptionText.length() : 0);

                // 5. Guardar transcripción en Google Drive
                String googleDriveUrl = saveTranscriptionToGoogleDrive(transcriptionText, fileName,
                        batchMessage.getUserName(), callId);

                // 6. Marcar como completado
                batchMessage.markFileAsCompleted(fileId, transcriptionText, language, confidence, googleDriveUrl);
                log.info("✅ Archivo {} procesado exitosamente", fileName);

            } else {
                String error = transcriptionResult != null ?
                        (String) transcriptionResult.get("error") : "Respuesta vacía de la API";
                log.error("❌ Error en transcripción de archivo {}: {}", fileName, error);
                batchMessage.markFileAsFailed(fileId, "Error en API de transcripción: " + error);
            }

        } catch (Exception e) {
            log.error("❌ Error procesando archivo independiente {}: {}", fileName, e.getMessage(), e);
            batchMessage.markFileAsFailed(fileId, "Error procesando archivo: " + e.getMessage());
        }
    }

    /**
     * Genera un Call ID único para archivos independientes
     */
    private String generateCallId(String fileName) {
        long timestamp = System.currentTimeMillis();
        int randomSuffix = (int) (Math.random() * 100000);

        // Extraer información del nombre del archivo si es posible
        String baseId = fileName.replaceAll("[^a-zA-Z0-9]", "").substring(0, Math.min(10, fileName.length()));

        return String.format("INDEP_%d_%s_%d", timestamp, baseId, randomSuffix);
    }

    /**
     * Llama directamente a la API de transcripción con URL
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> callTranscriptionAPIDirectly(String audioUrl, String fileName, String callId,
                                                             String whisperModel, String device, String targetLanguage) {
        try {
            log.info("🎙️ Llamando a API de transcripción para archivo independiente: {}", fileName);

            // Preparar la petición HTTP
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // Crear el cuerpo de la petición
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("audio_url", audioUrl);
            requestBody.put("whisper_model", whisperModel != null ? whisperModel : "small");
            requestBody.put("device", device != null ? device : "gpu");
            requestBody.put("target_language", targetLanguage != null ? targetLanguage : "es");
            requestBody.put("call_id", callId);
            requestBody.put("call_type", "independent");

            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);

            // Llamar a la API
            ResponseEntity<Map<String, Object>> response = restTemplate.exchange(
                    transcriptionApiUrl,
                    HttpMethod.POST,
                    requestEntity,
                    (Class<Map<String, Object>>) (Class<?>) Map.class
            );

            log.info("✅ Respuesta de API de transcripción recibida para: {}", fileName);
            return response.getBody();

        } catch (Exception e) {
            log.error("❌ Error en API de transcripción para archivo {}: {}", fileName, e.getMessage(), e);
            throw new RuntimeException("Error en la API de transcripción: " + e.getMessage(), e);
        }
    }

    /**
     * Llama a la API de transcripción (MÉTODO OBSOLETO - mantener por compatibilidad)
     */
    private Map<String, Object> callTranscriptionAPI(byte[] audioData, String fileName,
                                                     String whisperModel, String device, String targetLanguage) {
        try {
            // Preparar headers
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);

            // Preparar form data
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            body.add("audio_file", new org.springframework.core.io.ByteArrayResource(audioData) {
                @Override
                public String getFilename() {
                    return fileName;
                }
            });
            body.add("model", whisperModel);
            body.add("device", device);
            body.add("language", targetLanguage);

            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

            // Llamar a la API
            ResponseEntity<Map> response = restTemplate.exchange(
                    transcriptionApiUrl,
                    HttpMethod.POST,
                    requestEntity,
                    Map.class
            );

            return response.getBody();

        } catch (Exception e) {
            throw new RuntimeException("Error en la API de transcripción: " + e.getMessage(), e);
        }
    }

    /**
     * Guarda la transcripción en Google Drive - VERSIÓN MEJORADA CON HELPER
     */
    private String saveTranscriptionToGoogleDrive(String transcriptionText, String fileName, String userName, String callId) {
        try {
            log.info("📁 Guardando transcripción independiente organizada: {}", fileName);

            // 1. 📅 OBTENER FECHA ACTUAL
            LocalDate fechaTranscripcion = LocalDate.now();

            // 2. 📁 OBTENER ESTRUCTURA DE CARPETAS USANDO EL HELPER
            String folderId = driveOrganizationHelper.getOrCreateDateStructure(
                    FolderType.TRANSCRIPCIONES_INDEPENDIENTES,
                    fechaTranscripcion
            );

            // 3. 📝 CREAR NOMBRE DE ARCHIVO MEJORADO
            String transcriptionFileName = generateEnhancedTranscriptionFileName(fileName, callId, fechaTranscripcion);

            // 4. 📄 CREAR CONTENIDO ENRIQUECIDO
            String content = createEnhancedTranscriptionContent(transcriptionText, fileName, userName, callId);

            // 5. ☁️ SUBIR ARCHIVO A LA CARPETA ORGANIZADA
            String fileId = googleDriveService.uploadFile(
                    content.getBytes(StandardCharsets.UTF_8),
                    transcriptionFileName,
                    "text/plain",
                    folderId
            );

            // 6. 🔗 OBTENER URL PÚBLICA
            String publicUrl = googleDriveService.getPublicLink(fileId);

            log.info("✅ Transcripción independiente guardada en estructura organizada");
            return publicUrl;

        } catch (Exception e) {
            log.error("❌ Error guardando transcripción independiente: {}", e.getMessage(), e);
            throw new RuntimeException("Error guardando en Google Drive: " + e.getMessage(), e);
        }
    }

    /**
     * Procesa un archivo de audio asumiendo que es GSM (para archivos de grabaciones)
     * Basado en el método processAudioFileAsGsm de TranscriptionQueueServiceImpl
     */
    private String processAudioFileAsGsm(String audioUrl, String fileName, String batchId) {
        try {

            // Verificar si FFmpeg está disponible
            if (!audioConversionService.isFFmpegAvailable()) {
                return audioUrl; // Enviar GSM directamente como fallback
            }
            // Generar nombre único para el archivo MP3 convertido
            String outputFileName = generateMp3FileName(fileName, batchId);

            // Convertir GSM a MP3 usando el servicio de conversión
            String mp3Url = audioConversionService.convertGsmToMp3(audioUrl, outputFileName);
            return mp3Url;

        } catch (Exception e) {
            return audioUrl; // Fallback en caso de error
        }
    }

    /**
     * Genera un nombre único para el archivo MP3 convertido
     */
    private String generateMp3FileName(String originalFileName, String batchId) {
        // Remover extensión del archivo original
        String baseName = originalFileName.replaceAll("\\.[^.]+$", "");

        // Agregar timestamp y batchId para unicidad
        String timestamp = String.valueOf(System.currentTimeMillis());

        return String.format("%s_%s_%s", baseName, batchId, timestamp);
    }

    /**
     * Descarga un archivo procesado desde una URL
     */
    private byte[] downloadProcessedAudio(String audioUrl) throws IOException {
        try {
            // Si es una URL de archivo local (file://), usar el servicio de conversión
            if (audioUrl.startsWith("file://")) {
                String filePath = audioUrl.substring(7); // Remover "file://"
                return java.nio.file.Files.readAllBytes(java.nio.file.Paths.get(filePath));
            }

            // Si es una URL HTTP/HTTPS, descargar normalmente
            URL url = new URL(audioUrl);
            try (java.io.InputStream inputStream = url.openStream()) {
                return inputStream.readAllBytes();
            }

        } catch (Exception e) {
            throw new IOException("Error al descargar archivo procesado: " + e.getMessage(), e);
        }
    }

    /**
     * Verifica si un archivo es GSM basándose en la URL o extensión
     */
    private boolean isGsmFile(String audioUrl) {
        if (audioUrl == null) return false;

        String lowerUrl = audioUrl.toLowerCase();
        return lowerUrl.contains(".gsm") || lowerUrl.contains("gsm");
    }

    /**
     * Genera un nombre de archivo mejorado para transcripciones independientes
     */
    private String generateEnhancedTranscriptionFileName(String originalFileName, String callId, LocalDate fecha) {
        // Remover extensión del archivo original
        String baseName = originalFileName.replaceAll("\\.[^.]+$", "");

        // Formato: YYYYMMDD-HHMMSS_nombreArchivo_callId_transcripcion.txt
        String timestamp = fecha.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String timeNow = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HHmmss"));

        return String.format("%s-%s_%s_%s_transcripcion.txt",
                timestamp, timeNow, baseName, callId.substring(0, Math.min(8, callId.length())));
    }

    /**
     * Crea contenido enriquecido para transcripciones independientes
     */
    private String createEnhancedTranscriptionContent(String transcriptionText, String fileName, String userName, String callId) {
        return String.format(
                "TRANSCRIPCIÓN INDEPENDIENTE\n" +
                "============================\n" +
                "📄 Archivo Original: %s\n" +
                "👤 Usuario: %s\n" +
                "🆔 Call ID: %s\n" +
                "📅 Fecha de Procesamiento: %s\n" +
                "🎙️ Tipo: Transcripción Independiente\n" +
                "============================\n\n" +
                "📝 CONTENIDO DE LA TRANSCRIPCIÓN:\n" +
                "--------------------------------\n\n%s\n\n" +
                "============================\n" +
                "✅ Procesado por MIDAS CRM - Sistema de Transcripciones\n" +
                "🕒 Generado: %s",
                fileName, userName, callId,
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss")),
                transcriptionText,
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss"))
        );
    }
}

package com.midas.crm.repository;

import com.midas.crm.entity.Leccion;
import com.midas.crm.entity.Seccion;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface LeccionRepository extends JpaRepository<Leccion, Long> {
    List<Leccion> findBySeccionIdOrderByOrdenAsc(Long seccionId);
    List<Leccion> findBySeccionAndEstadoOrderByOrdenAsc(Seccion seccion, String estado);
    Optional<Leccion> findByTituloAndSeccionId(String titulo, Long seccionId);
    boolean existsByTituloAndSeccionId(String titulo, Long seccionId);

    @Query("SELECT l FROM Leccion l JOIN l.seccion s JOIN s.modulo m JOIN m.curso c WHERE c.id = :cursoId ORDER BY m.orden, s.orden, l.orden")
    List<Leccion> findAllByCursoIdOrderByModuloOrdenAndSeccionOrdenAndLeccionOrden(Long cursoId);

    @Query("SELECT l FROM Leccion l JOIN l.seccion s JOIN s.modulo m WHERE m.id = :moduloId ORDER BY s.orden, l.orden")
    List<Leccion> findAllByModuloIdOrderBySeccionOrdenAndLeccionOrden(Long moduloId);
}

import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { CorreosComponent } from './correos.component';
import { BandejaEntradaComponent } from './pages/bandeja-entrada/bandeja-entrada.component';
import { DetalleMensajeComponent } from './pages/detalle-mensaje/detalle-mensaje.component';
import { AuthGuard } from '@app/guards/auth/auth.guard';

const routes: Routes = [
  {
    path: '',
    component: CorreosComponent,
    canActivate: [AuthGuard],
    children: [
      {
        path: 'bandeja-entrada',
        component: BandejaEntradaComponent,
        canActivate: [AuthGuard]
      },
      {
        path: 'mensaje/:id',
        component: DetalleMensajeComponent,
        canActivate: [AuthGuard]
      },
      {
        path: '',
        redirectTo: '',
        pathMatch: 'full'
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class CorreosRoutingModule { }

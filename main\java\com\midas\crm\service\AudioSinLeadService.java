package com.midas.crm.service;

import com.midas.crm.entity.AudioSinLead;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Servicio para manejar audios sin leads correspondientes
 */
public interface AudioSinLeadService {

    /**
     * Registra un nuevo audio sin lead
     */
    AudioSinLead registrarAudioSinLead(Map<String, Object> audioInfo, String motivoSinLead);

    /**
     * Busca audio por ID
     */
    Optional<AudioSinLead> findById(Long id);

    /**
     * Busca audio por nombre de archivo
     */
    Optional<AudioSinLead> findByNombreArchivo(String nombreArchivo);

    /**
     * Busca audios por móvil extraído
     */
    List<AudioSinLead> findByMovilExtraido(String movilExtraido);

    /**
     * Busca audios por agente extraído
     */
    List<AudioSinLead> findByAgenteExtraido(String agenteExtraido);

    /**
     * Busca audios por estado
     */
    List<AudioSinLead> findByEstado(String estado);

    /**
     * Busca audios por rango de fechas
     */
    List<AudioSinLead> findByFechaProcesamientoBetween(LocalDateTime inicio, LocalDateTime fin);

    /**
     * Busca audios por rango de fechas con paginación
     */
    Page<AudioSinLead> findByFechaProcesamientoBetween(LocalDateTime inicio, LocalDateTime fin, Pageable pageable);

    /**
     * Busca audios que requieren procesamiento
     */
    List<AudioSinLead> findAudiosRequierenProcesamiento();

    /**
     * Busca audios por prioridad
     */
    List<AudioSinLead> findByPrioridad(String prioridad);

    /**
     * Busca audios por móvil y agente
     */
    List<AudioSinLead> findByMovilYAgente(String movilExtraido, String agenteExtraido);

    /**
     * Verifica si existe un audio con el mismo nombre
     */
    boolean existsByNombreArchivo(String nombreArchivo);

    /**
     * Verifica si existe un audio con el mismo ID de Google Drive
     */
    boolean existsByGoogleDriveFileId(String googleDriveFileId);

    /**
     * Cuenta audios por estado
     */
    long countByEstado(String estado);

    /**
     * Cuenta audios que requieren procesamiento
     */
    long countAudiosRequierenProcesamiento();

    /**
     * Obtiene estadísticas de audios sin lead por fecha
     */
    List<Map<String, Object>> getEstadisticasPorFecha(LocalDateTime inicio, LocalDateTime fin);

    /**
     * Obtiene estadísticas de audios sin lead por agente
     */
    List<Map<String, Object>> getEstadisticasPorAgente(LocalDateTime inicio, LocalDateTime fin);

    /**
     * Busca audios duplicados
     */
    List<AudioSinLead> findDuplicados(String movil, String agente, LocalDateTime fecha);

    /**
     * Busca audios recientes (últimas 24 horas)
     */
    List<AudioSinLead> findAudiosRecientes();



    // **FIXED**: Modified method signature and implementation
    @Transactional(readOnly = true)
    Page<AudioSinLead> findRecientesYRequierenProcesamiento(String prioridad, Pageable pageable);

    // **FIXED**: Modified method signature and implementation
    @Transactional(readOnly = true)
    List<AudioSinLead> findRecientesYRequierenProcesamiento(String prioridad);

    /**
     * Busca audios por carpeta de origen
     */
    List<AudioSinLead> findByCarpetaOrigen(String carpetaOrigen);

    /**
     * Busca audios con información válida
     */
    List<AudioSinLead> findConInformacionValida();

    /**
     * Busca audios con información incompleta
     */
    List<AudioSinLead> findConInformacionIncompleta();

    /**
     * Actualiza estado de un audio
     */
    AudioSinLead updateEstado(Long id, String nuevoEstado, String observaciones);

    /**
     * Actualiza estado de múltiples audios
     */
    void updateEstadoMultiple(List<Long> ids, String nuevoEstado);

    /**
     * Marca audio como procesado
     */
    AudioSinLead marcarComoProcesado(Long id, String observaciones);

    /**
     * Marca audio como ignorado
     */
    AudioSinLead marcarComoIgnorado(Long id, String motivo);

    /**
     * Elimina audio por ID
     */
    void deleteById(Long id);

    /**
     * Elimina audios antiguos
     */
    void deleteAudiosAntiguos(int diasAntiguedad);

    /**
     * Busca audios por patrón en el nombre
     */
    List<AudioSinLead> findByNombreArchivoContaining(String patron);

    /**
     * Obtiene resumen de audios sin lead
     */
    Map<String, Object> getResumenAudiosSinLead();

    /**
     * Procesa información extraída del archivo de audio
     */
    Map<String, String> procesarInformacionAudio(Map<String, Object> audioFile);

    /**
     * Valida si la información del audio es suficiente para buscar lead
     */
    boolean isInformacionSuficiente(Map<String, String> infoExtraida);

    /**
     * Busca audios sin lead con filtros múltiples aplicados
     * Incluye búsqueda por fecha en el nombre del archivo (formato YYYYMMDD)
     */
    Page<AudioSinLead> findWithFilters(String estado, String prioridad, String movil, String agente,
                                       String carpetaOrigen, Boolean requiereProcesamiento,
                                       Boolean busquedaLeadRealizada, LocalDateTime fechaInicio,
                                       LocalDateTime fechaFin, String fechaInicioStr,
                                       String fechaFinStr, Pageable pageable);

    /**
     * Busca leads correspondientes para todos los audios sin lead de forma masiva
     * Retorna un mapa con los resultados encontrados y no encontrados
     */
    Map<String, Object> buscarLeadsMasivo();

    /**
     * Busca audios sin lead con filtros múltiples aplicados (sin paginación)
     * Incluye búsqueda por fecha en el nombre del archivo (formato YYYYMMDD)
     */
    List<AudioSinLead> findWithFilters(String estado, String prioridad, String movil, String agente,
                                       String carpetaOrigen, Boolean requiereProcesamiento,
                                       Boolean busquedaLeadRealizada, LocalDateTime fechaInicio,
                                       LocalDateTime fechaFin, String fechaInicioStr,
                                       String fechaFinStr);
}

package com.midas.crm.service;

import com.midas.crm.entity.DTO.auth.AuthRequest;
import com.midas.crm.entity.DTO.auth.AuthResponse;
import com.midas.crm.entity.DTO.auth.SignOutRequest;
import com.midas.crm.entity.DTO.auth.SignUpRequest;
import com.midas.crm.entity.User;
import jakarta.servlet.http.HttpServletRequest;

import java.util.Optional;

public interface AuthService {
    AuthResponse refreshToken(HttpServletRequest request);
    void signUp(SignUpRequest request);
    AuthResponse signIn(AuthRequest request);
    void signOut(SignOutRequest request);

    Optional<User> findByIdWithSede(Long id);

    Optional<User> findByIdWithSedeAndCoordinador(Long id);
}
package com.midas.crm.service;

import com.midas.crm.entity.DTO.certificado.CertificadoCreateDTO;
import com.midas.crm.entity.DTO.certificado.CertificadoDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

public interface CertificadoService {

    /**
     * Crear un nuevo certificado
     */
    CertificadoDTO crearCertificado(CertificadoCreateDTO certificadoCreateDTO, Long adminId);

    /**
     * Obtener certificado por ID
     */
    CertificadoDTO obtenerCertificadoPorId(Long id);

    /**
     * Obtener certificados de un usuario
     */
    List<CertificadoDTO> obtenerCertificadosPorUsuario(Long usuarioId);

    /**
     * Obtener certificados de un curso
     */
    List<CertificadoDTO> obtenerCertificadosPorCurso(Long cursoId);

    /**
     * Verificar si un usuario puede recibir certificado de un curso
     */
    boolean puedeRecibirCertificado(Long usuarioId, Long cursoId);

    /**
     * Obtener progreso detallado de un usuario en un curso
     */
    Map<String, Object> obtenerProgresoDetallado(Long usuarioId, Long cursoId);

    /**
     * Verificar si ya existe certificado para usuario y curso
     */
    boolean existeCertificado(Long usuarioId, Long cursoId);

    /**
     * Obtener certificados con paginación y filtros
     */
    Page<CertificadoDTO> obtenerCertificadosPaginados(String search, String estado, Pageable pageable);

    /**
     * Obtener certificado por código
     */
    CertificadoDTO obtenerCertificadoPorCodigo(String codigo);

    /**
     * Eliminar certificado (cambiar estado a inactivo)
     */
    void eliminarCertificado(Long id);

    /**
     * Obtener usuarios elegibles para certificado de un curso
     */
    List<Map<String, Object>> obtenerUsuariosElegibles(Long cursoId);
}

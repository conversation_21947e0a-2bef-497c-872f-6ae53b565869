package com.midas.crm.repository;

import com.midas.crm.entity.IndependentTranscription;
import com.midas.crm.entity.IndependentTranscription.TranscriptionStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repositorio para la entidad IndependentTranscription
 */
@Repository
public interface IndependentTranscriptionRepository extends JpaRepository<IndependentTranscription, Long> {

    /**
     * Busca transcripciones por estado
     */
    Page<IndependentTranscription> findByStatus(TranscriptionStatus status, Pageable pageable);

    /**
     * Busca transcripciones por múltiples estados
     */
    Page<IndependentTranscription> findByStatusIn(List<TranscriptionStatus> statuses, Pageable pageable);

    /**
     * Busca transcripciones por nombre de archivo (contiene)
     */
    Page<IndependentTranscription> findByFileNameContainingIgnoreCase(String fileName, Pageable pageable);

    /**
     * Busca transcripciones por idioma
     */
    Page<IndependentTranscription> findByLanguage(String language, Pageable pageable);

    /**
     * Busca transcripciones por usuario creador
     */
    Page<IndependentTranscription> findByCreatedBy(String createdBy, Pageable pageable);

    /**
     * Busca transcripciones en un rango de fechas
     */
    Page<IndependentTranscription> findByCreatedAtBetween(LocalDateTime startDate, LocalDateTime endDate, Pageable pageable);

    /**
     * Busca transcripciones por duración mínima
     */
    Page<IndependentTranscription> findByDurationGreaterThanEqual(Integer minDuration, Pageable pageable);

    /**
     * Busca transcripciones por duración máxima
     */
    Page<IndependentTranscription> findByDurationLessThanEqual(Integer maxDuration, Pageable pageable);

    /**
     * Busca transcripciones por confianza mínima
     */
    Page<IndependentTranscription> findByConfidenceGreaterThanEqual(Double minConfidence, Pageable pageable);

    /**
     * Busca transcripciones por confianza máxima
     */
    Page<IndependentTranscription> findByConfidenceLessThanEqual(Double maxConfidence, Pageable pageable);

    /**
     * Busca transcripciones que contengan etiquetas específicas
     */
    @Query("SELECT t FROM IndependentTranscription t WHERE t.tags LIKE %:tag%")
    Page<IndependentTranscription> findByTagsContaining(@Param("tag") String tag, Pageable pageable);

    /**
     * Búsqueda de texto completo en el contenido de la transcripción
     */
    @Query("SELECT t FROM IndependentTranscription t WHERE t.transcriptionText LIKE %:query% OR t.fileName LIKE %:query% OR t.notes LIKE %:query%")
    Page<IndependentTranscription> searchByContent(@Param("query") String query, Pageable pageable);

    /**
     * Búsqueda avanzada con múltiples filtros
     */
    @Query("SELECT t FROM IndependentTranscription t WHERE " +
            "(:statuses IS NULL OR t.status IN :statuses) AND " +
            "(:fileName IS NULL OR LOWER(t.fileName) LIKE LOWER(CONCAT('%', :fileName, '%'))) AND " +
            "(:language IS NULL OR t.language = :language) AND " +
            "(:createdBy IS NULL OR t.createdBy = :createdBy) AND " +
            "(:startDate IS NULL OR t.createdAt >= :startDate) AND " +
            "(:endDate IS NULL OR t.createdAt <= :endDate) AND " +
            "(:minDuration IS NULL OR t.duration >= :minDuration) AND " +
            "(:maxDuration IS NULL OR t.duration <= :maxDuration) AND " +
            "(:minConfidence IS NULL OR t.confidence >= :minConfidence) AND " +
            "(:maxConfidence IS NULL OR t.confidence <= :maxConfidence)")
    Page<IndependentTranscription> findWithFilters(
            @Param("statuses") List<TranscriptionStatus> statuses,
            @Param("fileName") String fileName,
            @Param("language") String language,
            @Param("createdBy") String createdBy,
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate,
            @Param("minDuration") Integer minDuration,
            @Param("maxDuration") Integer maxDuration,
            @Param("minConfidence") Double minConfidence,
            @Param("maxConfidence") Double maxConfidence,
            Pageable pageable);

    /**
     * Cuenta transcripciones por estado
     */
    @Query("SELECT t.status, COUNT(t) FROM IndependentTranscription t GROUP BY t.status")
    List<Object[]> countByStatus();

    /**
     * Cuenta transcripciones por idioma
     */
    @Query("SELECT t.language, COUNT(t) FROM IndependentTranscription t WHERE t.language IS NOT NULL GROUP BY t.language")
    List<Object[]> countByLanguage();

    /**
     * Obtiene estadísticas generales
     */
    @Query("SELECT COUNT(t), " +
            "COALESCE(SUM(t.duration), 0), " +
            "COALESCE(AVG(t.confidence), 0), " +
            "COALESCE(SUM(t.fileSize), 0) " +
            "FROM IndependentTranscription t")
    Object[] getGeneralStatistics();

    /**
     * Obtiene las etiquetas más utilizadas
     */
    @Query(value = "SELECT tag, COUNT(*) as count FROM (" +
            "SELECT TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(tags, ',', numbers.n), ',', -1)) as tag " +
            "FROM independent_transcriptions " +
            "CROSS JOIN (SELECT 1 n UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 " +
            "UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION SELECT 10) numbers " +
            "WHERE tags IS NOT NULL AND tags != '' " +
            "AND CHAR_LENGTH(tags) - CHAR_LENGTH(REPLACE(tags, ',', '')) >= numbers.n - 1" +
            ") as tag_list " +
            "WHERE tag != '' " +
            "GROUP BY tag " +
            "ORDER BY count DESC " +
            "LIMIT :limit", nativeQuery = true)
    List<Object[]> findPopularTags(@Param("limit") int limit);

    /**
     * Busca transcripciones por ID de archivo en Google Drive
     */
    Optional<IndependentTranscription> findByTranscriptionFileId(String transcriptionFileId);

    /**
     * Busca transcripciones por ID de archivo de audio en Google Drive
     */
    Optional<IndependentTranscription> findByAudioFileId(String audioFileId);

    /**
     * Busca transcripciones pendientes de procesamiento
     */
    List<IndependentTranscription> findByStatusOrderByCreatedAtAsc(TranscriptionStatus status);

    /**
     * Busca transcripciones creadas en las últimas horas
     */
    @Query("SELECT t FROM IndependentTranscription t WHERE t.createdAt >= :since")
    List<IndependentTranscription> findRecentTranscriptions(@Param("since") LocalDateTime since);

    /**
     * Busca transcripciones por modelo de Whisper utilizado
     */
    Page<IndependentTranscription> findByWhisperModel(String whisperModel, Pageable pageable);

    /**
     * Busca transcripciones con tiempo de procesamiento mayor a un valor
     */
    Page<IndependentTranscription> findByProcessingTimeGreaterThan(Long processingTime, Pageable pageable);

    /**
     * Busca transcripciones por carpeta de Google Drive
     */
    Page<IndependentTranscription> findByDriveFolderId(String driveFolderId, Pageable pageable);

    /**
     * Busca transcripciones por IP del cliente
     */
    Page<IndependentTranscription> findByClientIp(String clientIp, Pageable pageable);

    /**
     * Elimina transcripciones antiguas (para limpieza automática)
     */
    @Query("DELETE FROM IndependentTranscription t WHERE t.createdAt < :cutoffDate")
    void deleteOldTranscriptions(@Param("cutoffDate") LocalDateTime cutoffDate);

    /**
     * Busca transcripciones que necesitan reintento (fallidas hace más de X tiempo)
     */
    @Query("SELECT t FROM IndependentTranscription t WHERE t.status = 'FAILED' AND t.updatedAt < :retryAfter")
    List<IndependentTranscription> findFailedTranscriptionsForRetry(@Param("retryAfter") LocalDateTime retryAfter);

    /**
     * Cuenta transcripciones por usuario en un período
     */
    @Query("SELECT COUNT(t) FROM IndependentTranscription t WHERE t.createdBy = :createdBy AND t.createdAt >= :since")
    Long countByCreatedByAndCreatedAtAfter(@Param("createdBy") String createdBy, @Param("since") LocalDateTime since);

    /**
     * Busca transcripciones duplicadas por nombre de archivo
     */
    @Query("SELECT t FROM IndependentTranscription t WHERE t.originalFileName = :originalFileName AND t.fileSize = :fileSize")
    List<IndependentTranscription> findPotentialDuplicates(@Param("originalFileName") String originalFileName, @Param("fileSize") Long fileSize);
}

package com.midas.crm.service;

import com.midas.crm.entity.DTO.EstadisticaSedeDTO;
import com.midas.crm.entity.DTO.EstadisticaSedePaginadaResponse;
import com.midas.crm.entity.DTO.estadisticas.EstadisticaTranscripcionCoordinadorDTO;
import com.midas.crm.entity.DTO.estadisticas.EstadisticaTranscripcionAsesorDTO;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * Servicio para manejar las estadísticas por sede
 */
public interface EstadisticasSedeService {

    /**
     * Obtiene estadísticas agrupadas por sede, supervisor y vendedor para una fecha
     * específica
     *
     * @param sedeId ID de la sede (opcional, null para todas las sedes)
     * @param fecha  Fecha para filtrar
     * @return Lista de estadísticas
     */
    List<EstadisticaSedeDTO> obtenerEstadisticasPorSede(Long sedeId, LocalDate fecha);

    /**
     * Obtiene estadísticas resumidas por sede para una fecha específica
     *
     * @param fecha Fecha para filtrar
     * @return Lista de estadísticas resumidas
     */
    List<EstadisticaSedeDTO> obtenerResumenPorSede(LocalDate fecha);

    /**
     * Obtiene estadísticas por rango de fechas
     *
     * @param fechaInicio Fecha de inicio
     * @param fechaFin    Fecha de fin
     * @param sedeId      ID de la sede (opcional)
     * @return Lista de estadísticas
     */
    List<EstadisticaSedeDTO> obtenerEstadisticasPorRango(LocalDate fechaInicio, LocalDate fechaFin, Long sedeId);

    // ===== MÉTODOS PAGINADOS =====

    /**
     * Obtiene estadísticas agrupadas por sede, supervisor y vendedor para una fecha
     * específica con paginación
     *
     * @param sedeId       ID de la sede (opcional, null para todas las sedes)
     * @param supervisorId ID del supervisor (opcional, null para todos los
     *                     supervisores)
     * @param fecha        Fecha para filtrar
     * @param pageable     Información de paginación
     * @return Respuesta paginada con estadísticas
     */
    EstadisticaSedePaginadaResponse obtenerEstadisticasPorSedePaginado(Long sedeId, Long supervisorId,
                                                                       LocalDate fecha,
                                                                       Pageable pageable);

    /**
     * Obtiene estadísticas agrupadas por sede, supervisor y vendedor para una fecha
     * específica con paginación y filtro de búsqueda por nombre de vendedor
     *
     * @param sedeId           ID de la sede (opcional, null para todas las sedes)
     * @param supervisorId     ID del supervisor (opcional, null para todos los
     *                         supervisores)
     * @param fecha            Fecha para filtrar
     * @param busquedaVendedor Término de búsqueda para el nombre del vendedor
     *                         (opcional)
     * @param pageable         Información de paginación
     * @return Respuesta paginada con estadísticas
     */
    EstadisticaSedePaginadaResponse obtenerEstadisticasPorSedePaginadoConBusqueda(Long sedeId, Long supervisorId,
                                                                                  LocalDate fecha, String busquedaVendedor,
                                                                                  Pageable pageable);

    /**
     * Obtiene estadísticas por rango de fechas con paginación
     *
     * @param fechaInicio Fecha de inicio
     * @param fechaFin    Fecha de fin
     * @param sedeId      ID de la sede (opcional)
     * @param pageable    Información de paginación
     * @return Respuesta paginada con estadísticas
     */
    EstadisticaSedePaginadaResponse obtenerEstadisticasPorRangoPaginado(LocalDate fechaInicio, LocalDate fechaFin,
                                                                        Long sedeId, Pageable pageable);

    /**
     * Obtiene leads específicos de un asesor para una fecha determinada
     *
     * @param nombreAsesor Nombre completo del asesor
     * @param fecha        Fecha para filtrar
     * @param numeroMovil  Número móvil para filtrar (opcional)
     * @param pageable     Información de paginación
     * @return Respuesta paginada con los leads del asesor
     */
    Map<String, Object> obtenerLeadsPorAsesorYFecha(String nombreAsesor, LocalDate fecha, String numeroMovil,
                                                    Pageable pageable);

    /**
     * Obtiene leads específicos de un asesor para un rango de fechas
     *
     * @param nombreAsesor Nombre completo del asesor
     * @param fechaInicio  Fecha de inicio para filtrar
     * @param fechaFin     Fecha de fin para filtrar
     * @param numeroMovil  Número móvil para filtrar (opcional)
     * @param pageable     Información de paginación
     * @return Respuesta paginada con los leads del asesor
     */
    Map<String, Object> obtenerLeadsPorAsesorYRangoFechas(String nombreAsesor, LocalDate fechaInicio,
                                                          LocalDate fechaFin, String numeroMovil, Pageable pageable);

    /**
     * Obtiene supervisores/coordinadores por sede
     *
     * @param sedeId ID de la sede
     * @return Lista de supervisores de la sede
     */
    List<Map<String, Object>> obtenerSupervisoresPorSede(Long sedeId);

    /**
     * Exporta a Excel todos los leads filtrados por sede, supervisor y fecha
     * específica
     *
     * @param sedeId       ID de la sede (opcional)
     * @param supervisorId ID del supervisor (opcional)
     * @param fecha        Fecha para filtrar
     * @return Array de bytes con el archivo Excel generado
     */
    byte[] exportarLeadsPorRango(Long sedeId, Long supervisorId, LocalDate fecha);

    /**
     * Obtiene estadísticas acumuladas por rango de fechas con paginación
     *
     * @param sedeId       ID de la sede (opcional)
     * @param supervisorId ID del supervisor (opcional)
     * @param fechaInicio  Fecha de inicio del rango
     * @param fechaFin     Fecha de fin del rango
     * @param pageable     Información de paginación
     * @return Estadísticas acumuladas paginadas
     */
    EstadisticaSedePaginadaResponse obtenerEstadisticasPorRangoFechas(Long sedeId, Long supervisorId,
                                                                      LocalDate fechaInicio, LocalDate fechaFin, Pageable pageable);

    /**
     * Obtiene estadísticas acumuladas por rango de fechas con paginación y búsqueda
     * por vendedor
     *
     * @param sedeId           ID de la sede (opcional)
     * @param supervisorId     ID del supervisor (opcional)
     * @param fechaInicio      Fecha de inicio del rango
     * @param fechaFin         Fecha de fin del rango
     * @param busquedaVendedor Término de búsqueda para el nombre del vendedor
     *                         (opcional)
     * @param pageable         Información de paginación
     * @return Estadísticas acumuladas paginadas
     */
    EstadisticaSedePaginadaResponse obtenerEstadisticasPorRangoFechasConBusqueda(Long sedeId, Long supervisorId,
                                                                                 LocalDate fechaInicio, LocalDate fechaFin, String busquedaVendedor, Pageable pageable);

    /**
     * Exporta a Excel todos los leads filtrados por sede, supervisor y rango de
     * fechas
     *
     * @param sedeId       ID de la sede (opcional)
     * @param supervisorId ID del supervisor (opcional)
     * @param fechaInicio  Fecha de inicio del rango
     * @param fechaFin     Fecha de fin del rango
     * @return Array de bytes con el archivo Excel generado
     */
    byte[] exportarLeadsPorRangoFechas(Long sedeId, Long supervisorId, LocalDate fechaInicio, LocalDate fechaFin);

    /**
     * Exporta a Excel todos los leads de un coordinador filtrados por tipo de interés
     * Este método es específico para la funcionalidad de estadísticas de coordinador
     *
     * @param coordinador   Nombre del coordinador
     * @param tipoInteres   Tipo de interés: 'seguros', 'energia', 'lowi'
     * @param fecha         Fecha específica
     * @param fechaFin      Fecha de fin del rango (opcional, para rango de fechas)
     * @param sedeId        ID de la sede (opcional)
     * @param busquedaMovil Término de búsqueda para filtrar por número móvil (opcional)
     * @return Array de bytes con el archivo Excel generado
     */
    byte[] exportarLeadsCoordinador(String coordinador, String tipoInteres, LocalDate fecha,
                                    LocalDate fechaFin, Long sedeId, String busquedaMovil);

    /**
     * Exporta a Excel todos los leads filtrados incluyendo búsqueda por vendedor
     * Este método respeta TODOS los filtros aplicados en el frontend
     *
     * @param sedeId           ID de la sede (opcional)
     * @param supervisorId     ID del supervisor (opcional)
     * @param fecha            Fecha específica (opcional, para exportar por fecha)
     * @param fechaInicio      Fecha de inicio del rango (opcional, para exportar
     *                         por rango)
     * @param fechaFin         Fecha de fin del rango (opcional, para exportar por
     *                         rango)
     * @param busquedaVendedor Término de búsqueda para filtrar por vendedor
     *                         (opcional)
     * @return Array de bytes con el archivo Excel generado
     */
    byte[] exportarLeadsFiltrados(Long sedeId, Long supervisorId, LocalDate fecha,
                                  LocalDate fechaInicio, LocalDate fechaFin, String busquedaVendedor);

    /**
     * Obtiene el rendimiento de leads por asesor para un período específico
     *
     * @param periodo      Tipo de período: 'diario', 'semanal', 'mensual'
     * @param sedeId       ID de la sede (opcional)
     * @param supervisorId ID del supervisor (opcional)
     * @param fecha        Fecha de referencia
     * @return Lista de rendimiento de asesores
     */
    List<Map<String, Object>> obtenerRendimientoLeadsPorAsesor(String periodo, Long sedeId, Long supervisorId,
                                                               LocalDate fecha);

    // ===== MÉTODOS PARA ESTADÍSTICAS DE TRANSCRIPCIONES POR COORDINADOR =====

    /**
     * Obtiene estadísticas de transcripciones por coordinador para todas las fechas
     * Clasifica leads por porcentajes de eficiencia basados en nota_agente_comparador_ia
     *
     * @return Lista de estadísticas de transcripciones por coordinador
     */
    List<EstadisticaTranscripcionCoordinadorDTO> obtenerEstadisticasTranscripcionPorCoordinador();

    /**
     * Obtiene estadísticas de transcripciones por coordinador filtrado por sede
     *
     * @param sedeId ID de la sede para filtrar
     * @return Lista de estadísticas de transcripciones por coordinador
     */
    List<EstadisticaTranscripcionCoordinadorDTO> obtenerEstadisticasTranscripcionPorCoordinadorYSede(Long sedeId);

    /**
     * Obtiene estadísticas de transcripciones por coordinador filtrado por fecha específica
     *
     * @param fecha Fecha específica para filtrar
     * @return Lista de estadísticas de transcripciones por coordinador
     */
    List<EstadisticaTranscripcionCoordinadorDTO> obtenerEstadisticasTranscripcionPorCoordinadorYFecha(LocalDate fecha);

    /**
     * Obtiene estadísticas de transcripciones por coordinador filtrado por sede y fecha específica
     *
     * @param sedeId ID de la sede para filtrar
     * @param fecha  Fecha específica para filtrar
     * @return Lista de estadísticas de transcripciones por coordinador
     */
    List<EstadisticaTranscripcionCoordinadorDTO> obtenerEstadisticasTranscripcionPorCoordinadorSedeYFecha(Long sedeId, LocalDate fecha);

    /**
     * Obtiene estadísticas de transcripciones por coordinador filtrado por rango de fechas
     *
     * @param fechaInicio Fecha de inicio del rango
     * @param fechaFin    Fecha de fin del rango
     * @return Lista de estadísticas de transcripciones por coordinador
     */
    List<EstadisticaTranscripcionCoordinadorDTO> obtenerEstadisticasTranscripcionPorCoordinadorYRangoFechas(LocalDate fechaInicio, LocalDate fechaFin);

    /**
     * Obtiene estadísticas de transcripciones por coordinador filtrado por sede y rango de fechas
     *
     * @param sedeId      ID de la sede para filtrar
     * @param fechaInicio Fecha de inicio del rango
     * @param fechaFin    Fecha de fin del rango
     * @return Lista de estadísticas de transcripciones por coordinador
     */
    List<EstadisticaTranscripcionCoordinadorDTO> obtenerEstadisticasTranscripcionPorCoordinadorSedeYRangoFechas(Long sedeId, LocalDate fechaInicio, LocalDate fechaFin);

    // ===== NUEVOS MÉTODOS CON LÓGICA DE AUDIOS HUÉRFANOS =====

    /**
     * NUEVA LÓGICA: Obtiene estadísticas de transcripciones por coordinador CON AUDIOS HUÉRFANOS
     * Incluye audios de Google Drive que no tienen lead correspondiente
     *
     * LEADS CONTACTO = Total de audios de Google Drive del asesor (registrados + audios sin lead)
     * LEADS REGISTRADO = Solo audios que tienen lead en cliente_residencial
     * BIEN REGISTRADO = Leads con nota_agente_comparador_ia >= 50%
     * MAL REGISTRADO = Leads con nota_agente_comparador_ia < 50%
     * NO REGISTRADO = Audios en audio_sin_lead de números de agente del asesor
     *
     * @return Lista de estadísticas de transcripciones por coordinador con audios huérfanos
     */
    List<EstadisticaTranscripcionCoordinadorDTO> obtenerEstadisticasTranscripcionConAudiosHuerfanos();

    /**
     * NUEVA LÓGICA: Obtiene estadísticas de transcripciones por coordinador CON AUDIOS HUÉRFANOS filtrado por sede
     *
     * @param sedeId ID de la sede para filtrar
     * @return Lista de estadísticas de transcripciones por coordinador con audios huérfanos
     */
    List<EstadisticaTranscripcionCoordinadorDTO> obtenerEstadisticasTranscripcionConAudiosHuerfanosPorSede(Long sedeId);

    /**
     * NUEVA LÓGICA: Obtiene estadísticas de transcripciones por coordinador CON AUDIOS HUÉRFANOS filtrado por fecha específica
     *
     * @param fecha Fecha específica para filtrar
     * @return Lista de estadísticas de transcripciones por coordinador con audios huérfanos
     */
    List<EstadisticaTranscripcionCoordinadorDTO> obtenerEstadisticasTranscripcionConAudiosHuerfanosPorFecha(LocalDate fecha);

    /**
     * NUEVA LÓGICA: Obtiene estadísticas de transcripciones por coordinador CON AUDIOS HUÉRFANOS filtrado por sede y fecha específica
     *
     * @param sedeId ID de la sede para filtrar
     * @param fecha  Fecha específica para filtrar
     * @return Lista de estadísticas de transcripciones por coordinador con audios huérfanos
     */
    List<EstadisticaTranscripcionCoordinadorDTO> obtenerEstadisticasTranscripcionConAudiosHuerfanosPorSedeYFecha(Long sedeId, LocalDate fecha);

    /**
     * NUEVA LÓGICA: Obtiene estadísticas de transcripciones por coordinador CON AUDIOS HUÉRFANOS filtrado por rango de fechas
     * CARGA POR DEFECTO: Este método se usa para cargar datos del mes anterior por defecto
     *
     * @param fechaInicio Fecha de inicio del rango
     * @param fechaFin    Fecha de fin del rango
     * @return Lista de estadísticas de transcripciones por coordinador con audios huérfanos
     */
    List<EstadisticaTranscripcionCoordinadorDTO> obtenerEstadisticasTranscripcionConAudiosHuerfanosPorRangoFechas(LocalDate fechaInicio, LocalDate fechaFin);

    /**
     * NUEVA LÓGICA: Obtiene estadísticas de transcripciones por coordinador CON AUDIOS HUÉRFANOS filtrado por sede y rango de fechas
     *
     * @param sedeId      ID de la sede para filtrar
     * @param fechaInicio Fecha de inicio del rango
     * @param fechaFin    Fecha de fin del rango
     * @return Lista de estadísticas de transcripciones por coordinador con audios huérfanos
     */
    List<EstadisticaTranscripcionCoordinadorDTO> obtenerEstadisticasTranscripcionConAudiosHuerfanosPorSedeYRangoFechas(Long sedeId, LocalDate fechaInicio, LocalDate fechaFin);

    // ===== MÉTODOS PARA ESTADÍSTICAS DE TRANSCRIPCIONES POR ASESOR =====

    /**
     * Obtiene estadísticas de transcripciones por asesor de un coordinador específico
     *
     * @param coordinadorId ID del coordinador
     * @param sedeId        ID de la sede para filtrar (opcional)
     * @param fecha         Fecha específica para filtrar (opcional)
     * @param fechaInicio   Fecha de inicio del rango (opcional)
     * @param fechaFin      Fecha de fin del rango (opcional)
     * @return Lista de estadísticas de transcripciones por asesor
     */
    List<EstadisticaTranscripcionAsesorDTO> obtenerEstadisticasTranscripcionPorAsesor(
            Long coordinadorId, Long sedeId, LocalDate fecha, LocalDate fechaInicio, LocalDate fechaFin
    );

    // ===== MÉTODOS PARA LEADS FILTRADOS POR COORDINADOR Y TIPO DE INTERÉS =====

    /**
     * Obtiene leads específicos de un coordinador filtrados por tipo de interés para una fecha determinada
     *
     * @param nombreCoordinador Nombre completo del coordinador
     * @param tipoInteres       Tipo de interés: 'seguros', 'energia', 'lowi'
     * @param fecha             Fecha para filtrar
     * @param sedeId            ID de la sede (opcional)
     * @param numeroMovil       Número móvil para filtrar (opcional)
     * @param pageable          Configuración de paginación
     * @return Map con leads paginados y metadatos
     */
    Map<String, Object> obtenerLeadsPorCoordinadorYTipoInteres(
            String nombreCoordinador, String tipoInteres, LocalDate fecha,
            Long sedeId, String numeroMovil, Pageable pageable
    );

    /**
     * Obtiene leads específicos de un coordinador filtrados por tipo de interés para un rango de fechas
     *
     * @param nombreCoordinador Nombre completo del coordinador
     * @param tipoInteres       Tipo de interés: 'seguros', 'energia', 'lowi'
     * @param fechaInicio       Fecha de inicio para filtrar
     * @param fechaFin          Fecha de fin para filtrar
     * @param sedeId            ID de la sede (opcional)
     * @param numeroMovil       Número móvil para filtrar (opcional)
     * @param pageable          Configuración de paginación
     * @return Map con leads paginados y metadatos
     */
    Map<String, Object> obtenerLeadsPorCoordinadorYTipoInteresRango(
            String nombreCoordinador, String tipoInteres, LocalDate fechaInicio, LocalDate fechaFin,
            Long sedeId, String numeroMovil, Pageable pageable
    );

    // ===== NUEVOS MÉTODOS PARA ESTADÍSTICAS DE TRANSCRIPCIONES POR ASESOR CON AUDIOS HUÉRFANOS =====

    String obtenerNombreAsesorPorAgenteYFecha(String agente, LocalDate fecha);

    /**
     * NUEVA LÓGICA: Obtiene estadísticas de transcripciones por asesor CON AUDIOS HUÉRFANOS
     *
     * Incluye audios de Google Drive que no tienen lead correspondiente en la métrica LEADS CONTACTO.
     * Esta nueva implementación proporciona una visión más completa del rendimiento real de cada asesor.
     *
     * MÉTRICAS INCLUIDAS:
     * - LEADS CONTACTO: Total de audios de Google Drive del asesor (registrados + audios sin lead)
     * - LEADS REGISTRADO: Solo audios que tienen lead correspondiente en cliente_residencial
     * - BIEN REGISTRADO: Leads con nota_agente_comparador_ia >= 50%
     * - MAL REGISTRADO: Leads con nota_agente_comparador_ia < 50%
     * - NO REGISTRADO: Audios en audio_sin_lead de números de agente del asesor
     * - EFICIENCIA: Porcentaje basado en leads registrados (no en total de contactos)
     *
     * COMPLEJIDAD TÉCNICA: Un asesor puede usar múltiples números de agente en diferentes fechas,
     * incluso dentro del mismo mes. Las consultas SQL manejan esta complejidad agregando
     * correctamente todos los audios huérfanos correspondientes.
     *
     * @return Lista de estadísticas de transcripciones por asesor con audios huérfanos
     */
    List<EstadisticaTranscripcionAsesorDTO> obtenerEstadisticasTranscripcionAsesorConAudiosHuerfanos();

    /**
     * NUEVA LÓGICA: Obtiene estadísticas de transcripciones por asesor CON AUDIOS HUÉRFANOS filtrado por sede
     *
     * @param sedeId ID de la sede para filtrar
     * @return Lista de estadísticas de transcripciones por asesor con audios huérfanos
     */
    List<EstadisticaTranscripcionAsesorDTO> obtenerEstadisticasTranscripcionAsesorConAudiosHuerfanosPorSede(Long sedeId);

    /**
     * NUEVA LÓGICA: Obtiene estadísticas de transcripciones por asesor CON AUDIOS HUÉRFANOS filtrado por fecha específica
     *
     * @param fecha Fecha específica para filtrar
     * @return Lista de estadísticas de transcripciones por asesor con audios huérfanos
     */
    List<EstadisticaTranscripcionAsesorDTO> obtenerEstadisticasTranscripcionAsesorConAudiosHuerfanosPorFecha(LocalDate fecha);

    /**
     * NUEVA LÓGICA: Obtiene estadísticas de transcripciones por asesor CON AUDIOS HUÉRFANOS filtrado por sede y fecha específica
     *
     * @param sedeId ID de la sede para filtrar
     * @param fecha Fecha específica para filtrar
     * @return Lista de estadísticas de transcripciones por asesor con audios huérfanos
     */
    List<EstadisticaTranscripcionAsesorDTO> obtenerEstadisticasTranscripcionAsesorConAudiosHuerfanosPorSedeYFecha(Long sedeId, LocalDate fecha);

    /**
     * NUEVA LÓGICA: Obtiene estadísticas de transcripciones por asesor CON AUDIOS HUÉRFANOS filtrado por rango de fechas
     *
     * @param fechaInicio Fecha de inicio del rango
     * @param fechaFin Fecha de fin del rango
     * @return Lista de estadísticas de transcripciones por asesor con audios huérfanos
     */
    List<EstadisticaTranscripcionAsesorDTO> obtenerEstadisticasTranscripcionAsesorConAudiosHuerfanosPorRangoFechas(LocalDate fechaInicio, LocalDate fechaFin);

    /**
     * NUEVA LÓGICA: Obtiene estadísticas de transcripciones por asesor CON AUDIOS HUÉRFANOS filtrado por sede y rango de fechas
     *
     * @param sedeId ID de la sede para filtrar
     * @param fechaInicio Fecha de inicio del rango
     * @param fechaFin Fecha de fin del rango
     * @return Lista de estadísticas de transcripciones por asesor con audios huérfanos
     */
    List<EstadisticaTranscripcionAsesorDTO> obtenerEstadisticasTranscripcionAsesorConAudiosHuerfanosPorSedeYRangoFechas(Long sedeId, LocalDate fechaInicio, LocalDate fechaFin);

    // ===== MÉTODOS PARA OBTENER DETALLES DE MÉTRICAS CON AUDIOS HUÉRFANOS =====

    /**
     * COORDINADORES: Obtiene leads bien registrados de un coordinador específico
     * @param nombreCoordinador Nombre completo del coordinador
     * @param sedeId ID de la sede (opcional)
     * @param fecha Fecha específica (opcional)
     * @param fechaInicio Fecha de inicio del rango (opcional)
     * @param fechaFin Fecha de fin del rango (opcional)
     * @param pageable Configuración de paginación
     * @return Map con leads bien registrados paginados
     */
    Map<String, Object> obtenerLeadsBienRegistradosPorCoordinador(
            String nombreCoordinador, Long sedeId, LocalDate fecha,
            LocalDate fechaInicio, LocalDate fechaFin, Pageable pageable
    );

    /**
     * COORDINADORES: Obtiene leads mal registrados de un coordinador específico
     */
    Map<String, Object> obtenerLeadsMalRegistradosPorCoordinador(
            String nombreCoordinador, Long sedeId, LocalDate fecha,
            LocalDate fechaInicio, LocalDate fechaFin, Pageable pageable
    );

    /**
     * COORDINADORES: Obtiene todos los leads registrados de un coordinador específico
     */
    Map<String, Object> obtenerLeadsRegistradosPorCoordinador(
            String nombreCoordinador, Long sedeId, LocalDate fecha,
            LocalDate fechaInicio, LocalDate fechaFin, Pageable pageable
    );

    /**
     * COORDINADORES: Obtiene audios huérfanos de un coordinador específico
     */
    Map<String, Object> obtenerAudiosHuerfanosPorCoordinador(
            String nombreCoordinador, Long sedeId, LocalDate fecha,
            LocalDate fechaInicio, LocalDate fechaFin, Pageable pageable
    );

    /**
     * ASESORES: Obtiene leads bien registrados de un asesor específico
     */
    Map<String, Object> obtenerLeadsBienRegistradosPorAsesor(
            String nombreAsesor, Long sedeId, LocalDate fecha,
            LocalDate fechaInicio, LocalDate fechaFin, Pageable pageable
    );

    /**
     * ASESORES: Obtiene leads mal registrados de un asesor específico
     */
    Map<String, Object> obtenerLeadsMalRegistradosPorAsesor(
            String nombreAsesor, Long sedeId, LocalDate fecha,
            LocalDate fechaInicio, LocalDate fechaFin, Pageable pageable
    );

    /**
     * ASESORES: Obtiene todos los leads registrados de un asesor específico
     */
    Map<String, Object> obtenerLeadsRegistradosPorAsesor(
            String nombreAsesor, Long sedeId, LocalDate fecha,
            LocalDate fechaInicio, LocalDate fechaFin, Pageable pageable
    );

    /**
     * ASESORES: Obtiene audios huérfanos de un asesor específico
     */
    Map<String, Object> obtenerAudiosHuerfanosPorAsesor(
            String nombreAsesor, Long sedeId, LocalDate fecha,
            LocalDate fechaInicio, LocalDate fechaFin, Pageable pageable
    );

    List<String> obtenerNumerosAgentePorCoordinador(
            Long coordinadorId,
            Long sedeId,
            LocalDate fecha,
            LocalDate fechaInicio,
            LocalDate fechaFin
    );

    Map<String, Object> obtenerDetalleLeadsContacto(Long coordinadorId, Long sedeId, LocalDate fecha, LocalDate fechaInicio, LocalDate fechaFin, Pageable pageable);
}

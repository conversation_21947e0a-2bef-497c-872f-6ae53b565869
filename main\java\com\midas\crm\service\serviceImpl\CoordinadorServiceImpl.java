package com.midas.crm.service.serviceImpl;

import com.midas.crm.entity.DTO.asesor.AsesorDisponibleDTO;
import com.midas.crm.entity.DTO.cliente.ClienteConUsuarioDTO;
import com.midas.crm.entity.DTO.asesor.AsesorDTO;
import com.midas.crm.entity.DTO.asesor.AsignacionAsesorDTO;
import com.midas.crm.entity.DTO.coordinador.CoordinadorDTO;
import com.midas.crm.entity.DTO.coordinador.CoordinadorListDTO;
import com.midas.crm.entity.Role;
import com.midas.crm.entity.User;
import com.midas.crm.mapper.AsesorMapper;
import com.midas.crm.mapper.CoordinadorMapper;
import com.midas.crm.repository.UserRepository;
import com.midas.crm.repository.CoordinadorClienteRepository;
import com.midas.crm.service.CoordinadorService;
import com.midas.crm.utils.GenericResponse;
import jakarta.annotation.Nullable;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class CoordinadorServiceImpl implements CoordinadorService {

    private final UserRepository userRepository;
    private final CoordinadorClienteRepository coordinadorClienteRepository;

    /**
     * Asigna una lista de asesores a un coordinador existente y retorna DTO actualizado.
     */
    @Override
    @Transactional
    public CoordinadorDTO asignarAsesoresACoordinador(AsignacionAsesorDTO dto) {
        User coord = userRepository.findById(dto.getCoordinadorId())
                .orElseThrow(() -> new IllegalArgumentException("Coordinador no encontrado"));
        if (coord.getRole() != Role.COORDINADOR) {
            throw new IllegalStateException("Usuario no es coordinador");
        }
        List<User> asesores = userRepository.findAllById(dto.getAsesorIds());
        if (asesores.size() != dto.getAsesorIds().size()) {
            throw new IllegalArgumentException("Algunos asesores no fueron encontrados");
        }
        asesores.forEach(a -> {
            if (a.getRole() != Role.ASESOR) {
                throw new IllegalStateException("Usuario " + a.getId() + " no es asesor");
            }
            a.setCoordinador(coord);
        });
        userRepository.saveAll(asesores);
        // recargar y mapear a DTO
        User updated = userRepository.findById(coord.getId()).orElse(coord);
        return CoordinadorMapper.toDTO(updated);
    }

// ✅ MÉTODO OPCIONAL PARA AGREGAR AL CoordinadorServiceImpl

    /**
     * ✅ NUEVO: Obtiene asesores de un coordinador de forma paginada con filtro opcional
     * Este método es opcional - el frontend ya funciona sin él
     */
    @Transactional(readOnly = true)
    @Override
    public GenericResponse<Map<String, Object>> obtenerAsesoresPorCoordinadorPaginado(
            Long coordinadorId, int page, int size, String term) {

        // Validar que el coordinador existe
        User coordinador = userRepository.findById(coordinadorId)
                .orElseThrow(() -> new RuntimeException("Coordinador no encontrado"));

        if (coordinador.getRole() != Role.COORDINADOR) {
            throw new RuntimeException("El usuario no es un coordinador");
        }

        Pageable pageable = PageRequest.of(page, size);

        // Obtener todos los asesores del coordinador
        List<User> todosLosAsesores = userRepository.findByCoordinadorIdAndRole(coordinadorId, Role.ASESOR);

        // Aplicar filtro si se proporciona
        List<User> asesoresFiltrados = todosLosAsesores;
        if (term != null && !term.trim().isEmpty()) {
            String termLower = term.trim().toLowerCase();
            asesoresFiltrados = todosLosAsesores.stream()
                    .filter(asesor ->
                            (asesor.getNombre() + " " + asesor.getApellido()).toLowerCase().contains(termLower) ||
                                    (asesor.getDni() != null && asesor.getDni().toLowerCase().contains(termLower)) ||
                                    (asesor.getUsername() != null && asesor.getUsername().toLowerCase().contains(termLower)) ||
                                    (asesor.getEmail() != null && asesor.getEmail().toLowerCase().contains(termLower))
                    )
                    .collect(Collectors.toList());
        }

        // Aplicar paginación manual
        int totalElements = asesoresFiltrados.size();
        int fromIndex = page * size;
        int toIndex = Math.min(fromIndex + size, totalElements);

        List<User> asesoresPaginados = (fromIndex < totalElements)
                ? asesoresFiltrados.subList(fromIndex, toIndex)
                : new ArrayList<>();

        // Convertir a DTO y ordenar
        List<AsesorDTO> asesoresDTO = asesoresPaginados.stream()
                .map(AsesorMapper::toDTO)
                .sorted(java.util.Comparator.comparing(AsesorDTO::getNombre)
                        .thenComparing(AsesorDTO::getApellido))
                .collect(Collectors.toList());

        // Calcular metadatos de paginación
        int totalPages = (int) Math.ceil((double) totalElements / size);
        boolean hasNext = (page + 1) < totalPages;
        boolean hasPrevious = page > 0;

        Map<String, Object> data = Map.of(
                "asesores", asesoresDTO,
                "totalItems", totalElements,
                "totalPages", totalPages,
                "currentPage", page,
                "size", size,
                "hasNext", hasNext,
                "hasPrevious", hasPrevious
        );

        return new GenericResponse<>(1, "Asesores del coordinador obtenidos correctamente", data);
    }

    @Transactional(readOnly = true)
    @Override
    public Page<CoordinadorDTO> obtenerTodosLosCoordinadoresPaginado(
            int page,
            int size,
            @Nullable String term   /* ←  nuevo parámetro opcional de búsqueda */
    ) {

        /* 1.- Paginación */
        Pageable pageable = PageRequest.of(page, size);

    /* 2.- Traemos los usuarios-coordinador desde la BD.
           - Si “term” viene vacío → listado normal ordenado.
           - Si “term” trae algo  → búsqueda global en BD (nombre, apellido, DNI, username…). */
        Page<User> source =
                (term == null || term.isBlank())
                        ? userRepository.findAllByRoleOrderedByNombreApellido(Role.COORDINADOR, pageable)
                        : userRepository.searchCoordinadores(Role.COORDINADOR, term.trim(), pageable);

        /* 3.- Mapeamos ENTIDAD → DTO SIN forzar colecciones lazy  */
        List<CoordinadorDTO> dtoList = source.getContent().stream()
                .map(coordinador -> {
                    CoordinadorDTO dto = new CoordinadorDTO();

                    dto.setId(coordinador.getId());
                    dto.setUsername(coordinador.getUsername());
                    dto.setNombre(coordinador.getNombre());
                    dto.setApellido(coordinador.getApellido());
                    dto.setDni(coordinador.getDni());
                    dto.setEmail(coordinador.getEmail());
                    dto.setTelefono(coordinador.getTelefono());

                    /* Sede: primero el backup “sedeNombre”, si no viene usa la relación Sede */
                    String sedeNombre = coordinador.getSedeNombre();
                    if (sedeNombre == null && coordinador.getSede() != null) {
                        sedeNombre = coordinador.getSede().getNombre();
                    }
                    dto.setSede(sedeNombre);

                    /* Traemos y ordenamos los asesores del coordinador (si los necesitas en el DTO) */
                    List<AsesorDTO> asesoresDTO = userRepository.findByCoordinadorId(coordinador.getId()).stream()
                            .filter(u -> u.getRole() == Role.ASESOR)
                            .map(AsesorMapper::toDTO)
                            .sorted(
                                    java.util.Comparator.comparing(AsesorDTO::getNombre)
                                            .thenComparing(AsesorDTO::getApellido)
                            )
                            .toList();

                    dto.setAsesores(asesoresDTO);

                    return dto;
                })
                .toList();

        /* 4.- Devolvemos página con DTOs y metadatos originales */
        return new PageImpl<>(dtoList, pageable, source.getTotalElements());
    }


    @Override
    @Transactional(readOnly = true)
    public CoordinadorDTO obtenerCoordinadorPorId(Long coordinadorId) {
        User coordinador = userRepository.findById(coordinadorId)
                .orElseThrow(() -> new RuntimeException("Coordinador no encontrado"));

        if (coordinador.getRole() != Role.COORDINADOR) {
            throw new RuntimeException("El usuario no es un coordinador");
        }

        // Crear un DTO directamente sin intentar inicializar la colección de asesores
        CoordinadorDTO dto = new CoordinadorDTO();
        dto.setId(coordinador.getId());
        dto.setUsername(coordinador.getUsername());
        dto.setNombre(coordinador.getNombre());
        dto.setApellido(coordinador.getApellido());
        dto.setDni(coordinador.getDni());
        dto.setEmail(coordinador.getEmail());
        dto.setTelefono(coordinador.getTelefono());

        // Obtener el nombre de la sede, primero desde sedeNombre y si es null, desde la
        // relación sede
        String nombreSede = coordinador.getSedeNombre();
        if (nombreSede == null && coordinador.getSede() != null) {
            nombreSede = coordinador.getSede().getNombre();
        }
        dto.setSede(nombreSede);

        // Obtener los asesores de forma segura y ordenarlos alfabéticamente
        List<AsesorDTO> asesoresDTO = userRepository.findByCoordinadorId(coordinadorId).stream()
                .filter(user -> user.getRole() == Role.ASESOR)
                .map(AsesorMapper::toDTO)
                // Ordenar alfabéticamente por nombre y apellido
                .sorted(java.util.Comparator.comparing(AsesorDTO::getNombre)
                        .thenComparing(AsesorDTO::getApellido))
                .collect(Collectors.toList());

        dto.setAsesores(asesoresDTO);

        return dto;
    }

    @Override
    @Transactional(readOnly = true)
    public GenericResponse<Map<String, Object>> obtenerAsesoresSinCoordinador(
            int page, int size, String term) {

        Pageable pageable = PageRequest.of(page, size);

        Page<AsesorDisponibleDTO> pageResult =
                (term == null || term.isBlank())
                        ? userRepository.findByRoleAndCoordinadorIsNull(Role.ASESOR, pageable)
                        : userRepository.findDisponiblesByTerm(Role.ASESOR, term.trim(), pageable); // úsala si añadiste la versión con term

        Map<String,Object> data = Map.of(
                "asesores",    pageResult.getContent(),
                "totalItems",  pageResult.getTotalElements(),
                "size",        pageResult.getSize(),
                "totalPages",  pageResult.getTotalPages(),
                "hasPrevious", pageResult.hasPrevious(),
                "hasNext",     pageResult.hasNext(),
                "currentPage", pageResult.getNumber()
        );

        return new GenericResponse<>(1, "Asesores disponibles", data);
    }




    @Override
    @Transactional
    public boolean eliminarAsesorDeCoordinador(Long coordinadorId, Long asesorId) {
        // Verificar que el coordinador existe
        if (!userRepository.existsById(coordinadorId)) {
            throw new RuntimeException("Coordinador no encontrado");
        }

        User asesor = userRepository.findById(asesorId)
                .orElseThrow(() -> new RuntimeException("Asesor no encontrado"));

        if (asesor.getCoordinador() != null && asesor.getCoordinador().getId().equals(coordinadorId)) {
            asesor.setCoordinador(null);
            userRepository.save(asesor);
            return true;
        }

        return false;
    }

    /**
     * Método para obtener, de forma paginada, los clientes residenciales de los
     * asesores asignados a un coordinador.
     * Se filtra por fecha (formato "yyyy-MM-dd"); si no se envía, se usa la fecha
     * actual.
     *
     * La respuesta se estructura en un GenericResponse con:
     * - rpta: 1
     * - msg: "Clientes con usuario obtenidos correctamente"
     * - data: { totalItems, totalPages, currentPage, clientes: [...] }
     *
     * @param coordinadorId ID del coordinador
     * @param fecha         Fecha en formato "yyyy-MM-dd" (opcional)
     * @param page          Número de página
     * @param size          Tamaño de página
     * @return GenericResponse con la información paginada de clientes
     */
    @Override
    public GenericResponse<Map<String, Object>> obtenerClientesPorCoordinador(
            Long coordinadorId,
            String dni,
            String nombre,
            String numeroMovil,
            String fecha,
            int page,
            int size) {
        // Validar que el coordinador existe y es de rol COORDINADOR
        User coordinador = userRepository.findById(coordinadorId)
                .orElseThrow(() -> new RuntimeException("Coordinador no encontrado con ID: " + coordinadorId));

        if (coordinador.getRole() != Role.COORDINADOR) {
            throw new RuntimeException("El usuario con ID: " + coordinadorId + " no tiene rol de coordinador");
        }

        // Parsear la fecha si está presente
        LocalDate filtroFecha = null;
        if (fecha != null && !fecha.isEmpty()) {
            try {
                filtroFecha = LocalDate.parse(fecha, DateTimeFormatter.ISO_DATE);
            } catch (DateTimeParseException e) {
                throw new RuntimeException("Formato de fecha inválido. Use: yyyy-MM-dd");
            }
        }

        // Preparar paginación
        Pageable pageable = PageRequest.of(page, size);

        // Aplicar todos los filtros disponibles en el repositorio
        Page<ClienteConUsuarioDTO> pageClientes = coordinadorClienteRepository.findClientesByCoordinadorWithFilters(
                coordinadorId,
                dni,
                nombre,
                numeroMovil,
                filtroFecha,
                pageable);

        // Preparar respuesta con metadatos de paginación
        Map<String, Object> responseData = new HashMap<>();
        responseData.put("clientes", pageClientes.getContent());
        responseData.put("totalItems", pageClientes.getTotalElements());
        responseData.put("totalPages", pageClientes.getTotalPages());
        responseData.put("currentPage", pageClientes.getNumber());
        responseData.put("size", pageClientes.getSize());
        responseData.put("hasNext", pageClientes.hasNext());
        responseData.put("hasPrevious", pageClientes.hasPrevious());

        return new GenericResponse<>(1, "Clientes obtenidos correctamente", responseData);
    }


    /**
     * Lista coordinadores de forma paginada y reducida: sólo id, nombre completo, dni, sede y cantidad de asesores.
     */
    @Override
    @Transactional(readOnly = true)
    public GenericResponse<Map<String, Object>> listarCoordinadoresReducido(
            int page, int size, @Nullable String term) {
        Pageable pg = PageRequest.of(page, size);
        Page<CoordinadorListDTO> p = userRepository.findCoordinadorListByRoleAndTerm(
                Role.COORDINADOR,
                (term == null || term.isBlank()) ? null : term.trim(),
                pg
        );
        return new GenericResponse<>(1, "Coordinadores obtenidos correctamente", Map.of(
                "coordinadores", p.getContent(),
                "totalItems",     p.getTotalElements(),
                "totalPages",     p.getTotalPages(),
                "currentPage",    p.getNumber(),
                "size",           p.getSize(),
                "hasNext",        p.hasNext(),
                "hasPrevious",    p.hasPrevious()
        ));
    }

    /**
     * Obtiene el detalle completo de asesores asignados a un coordinador.
     */
    @Override
    @Transactional(readOnly = true)
    public List<AsesorDTO> obtenerAsesoresPorCoordinador(Long id) {
        return userRepository.findByCoordinadorIdAndRole(id, Role.ASESOR)
                .stream()
                .map(AsesorMapper::toDTO)
                .collect(Collectors.toList());
    }

}
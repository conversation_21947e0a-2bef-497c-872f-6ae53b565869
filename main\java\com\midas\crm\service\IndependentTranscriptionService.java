package com.midas.crm.service;


import com.midas.crm.entity.DTO.IndependentTranscriptionDTO;
import com.midas.crm.entity.IndependentTranscription;
import com.midas.crm.entity.IndependentTranscription.TranscriptionStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Interfaz del servicio para transcripciones independientes
 */
public interface IndependentTranscriptionService {

    /**
     * Crea una nueva transcripción independiente
     */
    IndependentTranscription createTranscription(MultipartFile audioFile, String fileName,
                                                 String whisperModel, String targetLanguage,
                                                 List<String> tags, String notes, String createdBy);

    /**
     * Crea una nueva transcripción independiente con datos ya procesados del frontend
     */
    IndependentTranscription createTranscriptionWithData(MultipartFile audioFile, String fileName,
                                                         String whisperModel, String targetLanguage,
                                                         List<String> tags, String notes,
                                                         String transcriptionText, Double confidence,
                                                         String language, Long processingTime,
                                                         Integer duration, String status,
                                                         String driveUrl, String callId, String createdBy);

    /**
     * Obtiene una transcripción por ID
     */
    Optional<IndependentTranscription> getTranscriptionById(Long id);

    /**
     * Obtiene todas las transcripciones con paginación
     */
    Page<IndependentTranscriptionDTO> getAllTranscriptions(Pageable pageable);

    /**
     * Busca transcripciones con filtros
     */
    Page<IndependentTranscriptionDTO> searchTranscriptions(
            List<TranscriptionStatus> statuses,
            String fileName,
            String language,
            String createdBy,
            LocalDateTime startDate,
            LocalDateTime endDate,
            Integer minDuration,
            Integer maxDuration,
            Double minConfidence,
            Double maxConfidence,
            Pageable pageable);

    /**
     * Busca transcripciones por contenido de texto
     */
    Page<IndependentTranscriptionDTO> searchByContent(String query, Pageable pageable);

    /**
     * Actualiza una transcripción
     */
    IndependentTranscription updateTranscription(Long id, String fileName, List<String> tags, String notes);

    /**
     * Elimina una transcripción
     */
    void deleteTranscription(Long id);

    /**
     * Procesa una transcripción (llamada al servicio de IA)
     */
    void processTranscription(Long id);

    /**
     * Reintenta una transcripción fallida
     */
    IndependentTranscription retryTranscription(Long id);

    /**
     * Cancela una transcripción en proceso
     */
    IndependentTranscription cancelTranscription(Long id);

    /**
     * Obtiene el estado de una transcripción
     */
    TranscriptionStatus getTranscriptionStatus(Long id);

    /**
     * Actualiza el estado de una transcripción
     */
    void updateTranscriptionStatus(Long id, TranscriptionStatus status);

    /**
     * Guarda el resultado de la transcripción
     */
    void saveTranscriptionResult(Long id, String transcriptionText, Double confidence,
                                 String language, Long processingTime);

    /**
     * Obtiene estadísticas generales
     */
    Map<String, Object> getStatistics();

    /**
     * Obtiene las etiquetas más populares
     */
    List<Map<String, Object>> getPopularTags(int limit);

    /**
     * Exporta transcripciones a Word
     */
    byte[] exportToWord(List<Long> transcriptionIds, boolean includeMetadata,
                        boolean includeTimestamps, String documentTitle, String documentAuthor);

    /**
     * Obtiene la URL de descarga de un archivo
     */
    String getDownloadUrl(Long id, String format);

    /**
     * Descarga un archivo de transcripción
     */
    byte[] downloadTranscription(Long id, String format);

    /**
     * Sube archivo de audio a Google Drive
     */
    String uploadAudioToGoogleDrive(MultipartFile file, String fileName);

    /**
     * Sube archivo de transcripción a Google Drive
     */
    String uploadTranscriptionToGoogleDrive(String transcriptionText, String fileName, LocalDateTime date);

    /**
     * Crea o obtiene carpeta organizada por fecha en Google Drive
     */
    String getOrCreateDateFolder(LocalDateTime date);

    /**
     * Valida un archivo de audio
     */
    void validateAudioFile(MultipartFile file);

    /**
     * Obtiene transcripciones pendientes de procesamiento
     */
    List<IndependentTranscription> getPendingTranscriptions();

    /**
     * Obtiene transcripciones recientes
     */
    List<IndependentTranscriptionDTO> getRecentTranscriptions(int hours);

    /**
     * Cuenta transcripciones por usuario en un período
     */
    Long countTranscriptionsByUser(String createdBy, LocalDateTime since);

    /**
     * Busca transcripciones duplicadas
     */
    List<IndependentTranscription> findPotentialDuplicates(String originalFileName, Long fileSize);

    /**
     * Limpia transcripciones antiguas
     */
    void cleanupOldTranscriptions(int daysOld);

    /**
     * Convierte entidad a DTO
     */
    IndependentTranscriptionDTO convertToDTO(IndependentTranscription transcription);

    /**
     * Convierte lista de entidades a DTOs
     */
    List<IndependentTranscriptionDTO> convertToDTOs(List<IndependentTranscription> transcriptions);
}

package com.midas.crm.service;

import com.midas.crm.entity.Asistencia;
import com.midas.crm.repository.AsistenciaRepository;
import com.midas.crm.service.serviceImpl.AsistenciaServiceImpl; // Importa tu servicio
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional; // Usa el de Spring

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

@Service
@Slf4j // Añade esta anotación para logging
public class TareaProgramadaService {

    private final AsistenciaRepository asistenciaRepository;
    private final AsistenciaServiceImpl asistenciaService; // Inyecta el servicio principal

    public TareaProgramadaService(AsistenciaRepository asistenciaRepository, AsistenciaServiceImpl asistenciaService) {
        this.asistenciaRepository = asistenciaRepository;
        this.asistenciaService = asistenciaService;
    }

    @Scheduled(cron = "0 59 23 * * ?") // Se ejecuta todos los días a las 23:59:00
    public void cerrarAsistenciasAbiertasDelDia() {
        log.info("▶️ Iniciando tarea programada: Búsqueda de asistencias sin cerrar...");

        LocalDate hoy = LocalDate.now();
        LocalDateTime inicioHoy = hoy.atStartOfDay();
        LocalDateTime finHoy = hoy.atTime(LocalTime.MAX);

        List<Asistencia> asistenciasAbiertas = asistenciaRepository
                .findAsistenciasSinSalidaPorFecha(inicioHoy, finHoy);

        if (asistenciasAbiertas.isEmpty()) {
            log.info("✅ Tarea finalizada. No se encontraron asistencias abiertas.");
            return;
        }

        log.warn("Se encontraron {} asistencias abiertas para procesar.", asistenciasAbiertas.size());

        for (Asistencia asistencia : asistenciasAbiertas) {
            // Delega la lógica de negocio al servicio correspondiente
            asistenciaService.cerrarAsistenciaAbiertaPorSistema(asistencia);
        }

        log.info("✅ Tarea finalizada. Todas las asistencias abiertas han sido procesadas.");
    }
}
package com.midas.crm.service.serviceImpl;

import com.midas.crm.entity.DTO.NotificationDTO;
import com.midas.crm.entity.DTO.NotificationReadUserDTO;
import com.midas.crm.entity.Notification;
import com.midas.crm.entity.NotificationRead;
import com.midas.crm.entity.Role;
import com.midas.crm.entity.User;
import com.midas.crm.repository.NotificationReadRepository;
import com.midas.crm.repository.NotificationRepository;
import com.midas.crm.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * Servicio para gestionar las notificaciones sin utilizar caching de Spring.
 * Se eliminan todas las anotaciones @Cacheable, @CacheEvict y @Caching, así como
 * las importaciones asociadas. Esto garantiza que cada operación consulte
 * directamente la base de datos y refleje los cambios inmediatamente.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class NotificationService {

    /* ---------- dependencias ---------- */
    private final NotificationRepository notificationRepository;
    private final NotificationReadRepository notificationReadRepository;
    private final UserRepository userRepository;
    private final SimpMessagingTemplate messagingTemplate;

    /**
     * Crea una nueva notificación.
     *
     * @param notificationDTO DTO con los datos de la notificación
     * @return DTO de la notificación creada
     */
    @Transactional
    public NotificationDTO createNotification(NotificationDTO notificationDTO) {
        Notification notification = new Notification();

        // Configurar destinatario si se proporciona
        if (notificationDTO.getRecipientId() != null) {
            userRepository.findById(notificationDTO.getRecipientId())
                    .ifPresent(notification::setRecipient);
        }

        // Configurar remitente si se proporciona
        if (notificationDTO.getSenderId() != null) {
            userRepository.findById(notificationDTO.getSenderId())
                    .ifPresent(notification::setSender);
        }

        // Configurar datos básicos
        notification.setSenderName(notificationDTO.getSenderName());
        notification.setTitle(notificationDTO.getTitle());
        notification.setMessage(notificationDTO.getMessage());
        notification.setAvatar(notificationDTO.getAvatar());
        notification.setType(notificationDTO.getType());
        notification.setCategory(notificationDTO.getCategory());
        notification.setCreatedAt(LocalDateTime.now());
        notification.setRead(false);

        // Guardar la notificación
        Notification savedNotification = notificationRepository.save(notification);

        // Convertir a DTO
        NotificationDTO savedDTO = mapToDTO(savedNotification);

        // Enviar notificación en tiempo real al destinatario específico
        if (savedNotification.getRecipient() != null) {
            Long recipientId = savedNotification.getRecipient().getId();
            log.info("=== ENVIANDO NOTIFICACIÓN POR WEBSOCKET ===");
            log.info("Destinatario ID: {}", recipientId);
            log.info("Título: {}", savedDTO.getTitle());
            log.info("Mensaje: {}", savedDTO.getMessage());
            log.info("Tópico: /user/{}/queue/notifications", recipientId);

            messagingTemplate.convertAndSendToUser(
                    recipientId.toString(),
                    "/queue/notifications",
                    savedDTO);

            long unreadCount = getUnreadCount(recipientId);
            log.info("Enviando contador actualizado: {}", unreadCount);
            log.info("Tópico contador: /user/{}/queue/notifications.count", recipientId);

            messagingTemplate.convertAndSendToUser(
                    recipientId.toString(),
                    "/queue/notifications.count",
                    unreadCount);

            log.info("✅ Notificación y contador enviados al usuario {} - Contador: {}",
                    recipientId, unreadCount);
            log.info("=== FIN ENVÍO WEBSOCKET ===");
        }

        // Si es una notificación de tipo BROADCAST, enviarla a todos los usuarios
        if (notification.getType() == Notification.NotificationType.BROADCAST) {
            messagingTemplate.convertAndSend("/topic/notifications", savedDTO);
        }

        // Si es una notificación basada en rol, enviarla al tópico correspondiente
        if (notification.getType() == Notification.NotificationType.ROLE_BASED && notification.getData() != null) {
            messagingTemplate.convertAndSend("/topic/notifications/role/" + notification.getData(), savedDTO);
        }

        return savedDTO;
    }

    /**
     * Obtiene las notificaciones para un usuario INCLUYENDO NOTIFICACIONES GRUPALES.
     */
    @Transactional(readOnly = true)
    public Page<NotificationDTO> getNotificationsForUser(Long userId, int page, int size) {
        try {
            log.debug("Obteniendo notificaciones para usuario {} - página: {}, tamaño: {}", userId, page, size);

            if (userId == null || userId <= 0) {
                log.warn("ID de usuario inválido: {}", userId);
                return Page.empty();
            }

            final int finalPage = Math.max(page, 0);
            final int finalSize = Math.min(Math.max(size, 1), 100);

            Optional<User> userOpt = userRepository.findById(userId);
            if (userOpt.isEmpty()) {
                log.warn("Usuario no encontrado: {}", userId);
                return Page.empty();
            }

            User user = userOpt.get();
            final Long finalSedeId = user.getSede() != null ? user.getSede().getId() : null;
            final String finalRole = user.getRole() != null ? user.getRole().name() : "USER";
            final Long finalUserId = userId;

            Pageable pageable = PageRequest.of(finalPage, finalSize);

            Page<Notification> notifications = notificationRepository.findNotificationsForUserIncludingGroups(
                    finalUserId, pageable);

            log.debug("Notificaciones encontradas: {} de {} total",
                    notifications.getNumberOfElements(), notifications.getTotalElements());

            return notifications.map(notification -> {
                try {
                    NotificationDTO dto = mapToDTO(notification);

                    if (notification.getRecipientId() == null) {
                        dto.setRecipientId(finalUserId);
                    }

                    if (notification.getRecipientId() == null
                            || notification.getType() == Notification.NotificationType.BROADCAST) {
                        boolean isRead = notificationReadRepository.existsByUserIdAndNotificationId(finalUserId,
                                notification.getId());
                        dto.setRead(isRead);
                    }

                    return dto;
                } catch (Exception e) {
                    log.error("Error al mapear notificación {}: {}", notification.getId(), e.getMessage());
                    NotificationDTO errorDto = new NotificationDTO();
                    errorDto.setId(notification.getId());
                    errorDto.setMessage("Error al cargar notificación");
                    errorDto.setRecipientId(finalUserId);
                    return errorDto;
                }
            });

        } catch (Exception e) {
            log.error("Error crítico al obtener notificaciones para usuario {}: {}", userId, e.getMessage(), e);
            return Page.empty();
        }
    }

    /**
     * Marca una notificación como leída.
     */
    @Transactional
    public boolean markAsRead(Long notificationId, Long userId) {
        Optional<Notification> notificationOpt = notificationRepository.findById(notificationId);
        if (notificationOpt.isEmpty()) {
            return false;
        }

        Notification notification = notificationOpt.get();

        if (notification.getRecipientId() != null && notification.getRecipientId().equals(userId)) {
            notification.setRead(true);
            notificationRepository.save(notification);
            return true;
        }

        if (notification.getType() == Notification.NotificationType.BROADCAST ||
                notification.getType() == Notification.NotificationType.ROLE_BASED) {

            if (notificationReadRepository.existsByUserIdAndNotificationId(userId, notificationId)) {
                return true;
            }

            NotificationRead notificationRead = new NotificationRead();
            notificationRead.setUserId(userId);
            notificationRead.setNotificationId(notificationId);
            notificationReadRepository.save(notificationRead);
            return true;
        }

        return false;
    }

    /**
     * Obtiene el número de notificaciones no leídas para un usuario, incluyendo notificaciones grupales.
     */
    @Transactional(readOnly = true)
    public long getUnreadCount(Long userId) {
        try {
            User user = userRepository.findById(userId).orElse(null);
            if (user == null) {
                log.warn("Usuario no encontrado para conteo: {}", userId);
                return 0L;
            }

            Long sedeId = user.getSede() != null ? user.getSede().getId() : null;
            String role = user.getRole().name();

            return notificationRepository.countUnreadNotificationsIncludingGroups(userId);

        } catch (Exception e) {
            log.error("Error al contar no leídas para usuario {}: {}", userId, e.getMessage(), e);
            return 0L;
        }
    }

    /**
     * Obtiene las últimas notificaciones para un usuario, incluyendo notificaciones grupales.
     */
    @Transactional(readOnly = true)
    public List<NotificationDTO> getLatestNotifications(Long userId, int limit) {
        try {
            Optional<User> userOpt = userRepository.findById(userId);
            if (userOpt.isEmpty()) {
                log.warn("Usuario no encontrado para notificaciones: {}", userId);
                return Collections.emptyList();
            }

            User user = userOpt.get();
            final Long finalSedeId = user.getSede() != null ? user.getSede().getId() : null;
            final String finalRole = user.getRole().name();
            final Long finalUserId = userId;

            Pageable pageable = PageRequest.of(0, limit);

            List<Notification> notifications = notificationRepository.findLatestNotificationsIncludingGroups(
                    finalUserId, pageable);

            return notifications.stream()
                    .map(notification -> {
                        NotificationDTO dto = mapToDTO(notification);
                        if (notification.getRecipientId() == null) {
                            dto.setRecipientId(finalUserId);
                        }
                        if (notification.getRecipientId() == null ||
                                notification.getType() == Notification.NotificationType.BROADCAST) {
                            boolean isRead = notificationReadRepository.existsByUserIdAndNotificationId(finalUserId,
                                    notification.getId());
                            dto.setRead(isRead);
                        }
                        return dto;
                    })
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Error al obtener notificaciones: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /* --------------------------------------------------------------------- */
    /*                        UTILIDADES PRIVADAS                             */
    /* --------------------------------------------------------------------- */

    private NotificationDTO mapToDTO(Notification notification) {
        NotificationDTO dto = new NotificationDTO();
        dto.setId(notification.getId());
        dto.setRecipientId(notification.getRecipientId());
        dto.setSenderId(notification.getSenderId());
        dto.setSenderName(notification.getSenderName());
        dto.setTitle(notification.getTitle());
        dto.setMessage(notification.getMessage());
        dto.setRead(notification.isRead());
        dto.setAvatar(notification.getAvatar());
        dto.setType(notification.getType());
        dto.setCategory(notification.getCategory());
        dto.setCreatedAt(notification.getCreatedAt());
        dto.setTime(formatTimeAgo(notification.getCreatedAt()));
        return dto;
    }

    private String formatTimeAgo(LocalDateTime dateTime) {
        LocalDateTime now = LocalDateTime.now();
        long minutes = java.time.Duration.between(dateTime, now).toMinutes();

        if (minutes < 1) {
            return "Ahora mismo";
        } else if (minutes < 60) {
            return "Hace " + minutes + " minuto" + (minutes > 1 ? "s" : "");
        } else if (minutes < 1440) {
            long hours = minutes / 60;
            return "Hace " + hours + " hora" + (hours > 1 ? "s" : "");
        } else if (minutes < 10080) {
            long days = minutes / 1440;
            return "Hace " + days + " día" + (days > 1 ? "s" : "");
        } else {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
            return dateTime.format(formatter);
        }
    }

    /* --------------------------------------------------------------------- */
    /*               MÉTODOS RELACIONADOS A GRUPOS (ROLE/SEDE)                */
    /* --------------------------------------------------------------------- */

    @Transactional
    public int sendNotificationByRole(String role, String title, String message, String senderName) {
        try {
            Role roleEnum = Role.valueOf(role.toUpperCase());
            List<User> users = userRepository.findByRoleAndEstado(roleEnum, "A");

            if (users.isEmpty()) {
                log.warn("No se encontraron usuarios activos con rol: {}", role);
                return 0;
            }

            // Crear notificaciones individuales para cada usuario
            List<Notification> individualNotifications = users.stream().map(user -> {
                Notification notification = new Notification();
                notification.setRecipient(user);
                notification.setTitle(title);
                notification.setMessage(message);
                notification.setSenderName(senderName);
                notification.setType(Notification.NotificationType.ROLE_BASED);
                notification.setCategory(Notification.NotificationCategory.GENERAL);
                notification.setRead(false);
                notification.setData("ROLE:" + role);
                return notification;
            }).collect(Collectors.toList());

            // Guardar todas las notificaciones individuales
            List<Notification> savedNotifications = notificationRepository.saveAll(individualNotifications);

            // Enviar notificaciones por WebSocket
            sendIndividualWebSocketNotifications(savedNotifications);

            log.info("Enviadas {} notificaciones por rol: {}", savedNotifications.size(), role);
            return savedNotifications.size();

        } catch (IllegalArgumentException e) {
            log.error("Rol inválido: {}", role);
            throw new RuntimeException("Rol inválido: " + role);
        } catch (Exception e) {
            log.error("Error al enviar notificaciones por rol {}: {}", role, e.getMessage(), e);
            throw new RuntimeException("Error al enviar notificaciones por rol: " + e.getMessage());
        }
    }

    @Transactional
    public int sendNotificationBySede(Long sedeId, String title, String message, String senderName) {
        List<User> users = userRepository.findBySedeIdAndEstado(sedeId, "A");
        if (users.isEmpty()) {
            log.warn("No se encontraron usuarios activos en la sede: {}", sedeId);
            return 0;
        }

        // Crear notificaciones individuales para cada usuario
        List<Notification> individualNotifications = users.stream().map(user -> {
            Notification notification = new Notification();
            notification.setRecipient(user);
            notification.setTitle(title);
            notification.setMessage(message);
            notification.setSenderName(senderName);
            notification.setType(Notification.NotificationType.ROLE_BASED);
            notification.setCategory(Notification.NotificationCategory.GENERAL);
            notification.setRead(false);
            notification.setData("SEDE:" + sedeId);
            return notification;
        }).collect(Collectors.toList());

        // Guardar todas las notificaciones individuales
        List<Notification> savedNotifications = notificationRepository.saveAll(individualNotifications);

        // Enviar notificaciones por WebSocket
        sendIndividualWebSocketNotifications(savedNotifications);

        log.info("Enviadas {} notificaciones por sede: {}", savedNotifications.size(), sedeId);
        return savedNotifications.size();
    }

    @Transactional
    public int sendNotificationBySedeAndRole(Long sedeId, String role, String title, String message,
                                             String senderName) {
        try {
            Role roleEnum = Role.valueOf(role.toUpperCase());
            List<User> users = userRepository.findBySedeIdAndRoleAndEstado(sedeId, roleEnum, "A");

            if (users.isEmpty()) {
                log.warn("No se encontraron usuarios activos con rol {} en la sede: {}", role, sedeId);
                return 0;
            }

            // Crear notificaciones individuales para cada usuario
            List<Notification> individualNotifications = users.stream().map(user -> {
                Notification notification = new Notification();
                notification.setRecipient(user);
                notification.setTitle(title);
                notification.setMessage(message);
                notification.setSenderName(senderName);
                notification.setType(Notification.NotificationType.ROLE_BASED);
                notification.setCategory(Notification.NotificationCategory.GENERAL);
                notification.setRead(false);
                notification.setData("SEDE:" + sedeId + ":ROLE:" + role);
                return notification;
            }).collect(Collectors.toList());

            // Guardar todas las notificaciones individuales
            List<Notification> savedNotifications = notificationRepository.saveAll(individualNotifications);

            // Enviar notificaciones por WebSocket
            sendIndividualWebSocketNotifications(savedNotifications);

            log.info("Enviadas {} notificaciones por sede {} y rol: {}", savedNotifications.size(), sedeId, role);
            return savedNotifications.size();

        } catch (IllegalArgumentException e) {
            log.error("Rol inválido: {}", role);
            throw new RuntimeException("Rol inválido: " + role);
        } catch (Exception e) {
            log.error("Error al enviar notificaciones por sede {} y rol {}: {}", sedeId, role, e.getMessage(), e);
            throw new RuntimeException("Error al enviar notificaciones por sede y rol: " + e.getMessage());
        }
    }

    /* --------------------------------------------------------------------- */
    /*                        MÉTODOS AUXILIARES                              */
    /* --------------------------------------------------------------------- */

    private Notification createGroupNotificationForSede(Long sedeId, String title, String message,
                                                        String senderName, int userCount) {
        Notification notification = new Notification();
        notification.setRecipientId(null);
        notification.setSenderName(senderName);
        notification.setTitle(title);
        notification.setMessage(message);
        notification.setType(Notification.NotificationType.ROLE_BASED);
        notification.setCategory(Notification.NotificationCategory.GENERAL);
        notification.setCreatedAt(LocalDateTime.now());
        notification.setRead(false);
        notification.setData("SEDE:" + sedeId + ":USERS:" + userCount);
        return notification;
    }

    private Notification createGroupNotificationForRole(String role, String title, String message,
                                                        String senderName, int userCount) {
        Notification notification = new Notification();
        notification.setRecipientId(null);
        notification.setSenderName(senderName);
        notification.setTitle(title);
        notification.setMessage(message);
        notification.setType(Notification.NotificationType.ROLE_BASED);
        notification.setCategory(Notification.NotificationCategory.GENERAL);
        notification.setCreatedAt(LocalDateTime.now());
        notification.setRead(false);
        notification.setData("ROLE:" + role + ":USERS:" + userCount);
        return notification;
    }

    private void sendGroupWebSocketNotification(Notification groupNotification, List<User> users) {
        try {
            NotificationDTO dto = mapToDTO(groupNotification);
            users.parallelStream().forEach(user -> {
                try {
                    NotificationDTO personalizedDTO = new NotificationDTO();
                    personalizedDTO.setId(groupNotification.getId());
                    personalizedDTO.setTitle(dto.getTitle());
                    personalizedDTO.setMessage(dto.getMessage());
                    personalizedDTO.setSenderName(dto.getSenderName());
                    personalizedDTO.setType(dto.getType());
                    personalizedDTO.setCategory(dto.getCategory());
                    personalizedDTO.setCreatedAt(dto.getCreatedAt());
                    personalizedDTO.setRead(false);
                    personalizedDTO.setRecipientId(user.getId());

                    // Enviar la notificación al usuario
                    messagingTemplate.convertAndSendToUser(
                            user.getId().toString(),
                            "/queue/notifications",
                            personalizedDTO);

                    // Enviar el contador actualizado de notificaciones no leídas
                    long unreadCount = getUnreadCount(user.getId());
                    messagingTemplate.convertAndSendToUser(
                            user.getId().toString(),
                            "/queue/notifications.count",
                            unreadCount);

                    log.debug("✅ Notificación y contador enviados al usuario {} - Contador: {}",
                            user.getId(), unreadCount);
                } catch (Exception e) {
                    log.error("Error WebSocket usuario {}: {}", user.getId(), e.getMessage());
                }
            });
            log.info("WebSocket grupal enviado a {} usuarios", users.size());
        } catch (Exception e) {
            log.error("Error en envío WebSocket grupal: {}", e.getMessage(), e);
        }
    }

    /**
     * Envía notificaciones individuales por WebSocket
     */
    private void sendIndividualWebSocketNotifications(List<Notification> notifications) {
        try {
            notifications.parallelStream().forEach(notification -> {
                try {
                    if (notification.getRecipient() != null) {
                        Long userId = notification.getRecipient().getId();
                        NotificationDTO dto = mapToDTO(notification);

                        // Enviar la notificación al usuario
                        messagingTemplate.convertAndSendToUser(
                                userId.toString(),
                                "/queue/notifications",
                                dto);

                        // Enviar el contador actualizado de notificaciones no leídas
                        long unreadCount = getUnreadCount(userId);
                        messagingTemplate.convertAndSendToUser(
                                userId.toString(),
                                "/queue/notifications.count",
                                unreadCount);

                        log.debug("✅ Notificación individual y contador enviados al usuario {} - Contador: {}",
                                userId, unreadCount);
                    }
                } catch (Exception e) {
                    log.error("Error WebSocket usuario: {}", e.getMessage());
                }
            });
            log.info("WebSocket individual enviado a {} usuarios", notifications.size());
        } catch (Exception e) {
            log.error("Error en envío WebSocket individual: {}", e.getMessage(), e);
        }
    }

    /* --------------------------------------------------------------------- */
    /*                        ACTUALIZAR Y ELIMINAR                         */
    /* --------------------------------------------------------------------- */

    /**
     * Actualiza una notificación existente
     */
    @Transactional
    public NotificationDTO updateNotification(Long notificationId, NotificationDTO notificationDTO) {
        Notification notification = notificationRepository.findById(notificationId)
                .orElseThrow(() -> new RuntimeException("Notificación no encontrada con ID: " + notificationId));

        // Actualizar campos permitidos
        if (notificationDTO.getTitle() != null) {
            notification.setTitle(notificationDTO.getTitle());
        }
        if (notificationDTO.getMessage() != null) {
            notification.setMessage(notificationDTO.getMessage());
        }
        if (notificationDTO.getSenderName() != null) {
            notification.setSenderName(notificationDTO.getSenderName());
        }
        if (notificationDTO.getType() != null) {
            notification.setType(notificationDTO.getType());
        }
        if (notificationDTO.getCategory() != null) {
            notification.setCategory(notificationDTO.getCategory());
        }

        Notification updatedNotification = notificationRepository.save(notification);

        log.info("Notificación {} actualizada exitosamente", notificationId);
        return mapToDTO(updatedNotification);
    }

    /**
     * Elimina una notificación
     */
    @Transactional
    public boolean deleteNotification(Long notificationId) {
        if (!notificationRepository.existsById(notificationId)) {
            log.warn("Intento de eliminar notificación inexistente: {}", notificationId);
            return false;
        }

        notificationRepository.deleteById(notificationId);
        log.info("Notificación {} eliminada exitosamente", notificationId);
        return true;
    }

    /**
     * Elimina múltiples notificaciones
     */
    @Transactional
    public int deleteNotifications(List<Long> notificationIds) {
        if (notificationIds == null || notificationIds.isEmpty()) {
            return 0;
        }

        List<Notification> notifications = notificationRepository.findAllById(notificationIds);
        int deletedCount = notifications.size();

        notificationRepository.deleteAll(notifications);

        log.info("Eliminadas {} notificaciones", deletedCount);
        return deletedCount;
    }

    /* --------------------------------------------------------------------- */
    /*                                EVENTOS                                */
    /* --------------------------------------------------------------------- */

    @Async
    void publishAsync(Object event) {
        log.info("Evento publicado: {}", event.getClass().getSimpleName());
    }

    public long getReadCount(Long notificationId) {
        try {
            return notificationReadRepository.countUsersWhoReadNotification(notificationId);
        } catch (Exception e) {
            log.error("Error al obtener el conteo de lecturas para la notificación {}: {}", notificationId,
                    e.getMessage(), e);
            return 0;
        }
    }

    public List<NotificationReadUserDTO> getUsersWhoReadNotification(Long notificationId) {
        try {
            List<Object[]> results = notificationReadRepository.findUsersWhoReadNotification(notificationId);

            return results.stream()
                    .map(result -> {
                        try {
                            Long userId = (Long) result[0];
                            String userName = (String) result[1];
                            String userEmail = (String) result[2];
                            String nombre = (String) result[3];
                            String apellido = (String) result[4];
                            String sedeNombre = (String) result[5];

                            // Manejar sedeId que puede ser null o 0
                            Long sedeId = null;
                            if (result[6] != null) {
                                if (result[6] instanceof Long) {
                                    sedeId = (Long) result[6];
                                } else if (result[6] instanceof Integer) {
                                    sedeId = ((Integer) result[6]).longValue();
                                }
                            }

                            LocalDateTime readAt = (LocalDateTime) result[7];

                            return new NotificationReadUserDTO(
                                    userId, userName, userEmail, nombre, apellido,
                                    sedeNombre, sedeId, readAt);
                        } catch (Exception e) {
                            log.error("Error al mapear resultado para notificación {}: {}", notificationId,
                                    e.getMessage(), e);
                            // Retornar un DTO básico en caso de error
                            return new NotificationReadUserDTO(
                                    (Long) result[0],
                                    (String) result[1],
                                    (String) result[2]);
                        }
                    })
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Error al obtener usuarios que leyeron la notificación {}: {}", notificationId, e.getMessage(),
                    e);
            return new ArrayList<>();
        }
    }
}
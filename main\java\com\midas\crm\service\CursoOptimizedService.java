package com.midas.crm.service;

import com.midas.crm.entity.DTO.curso.CursoListDTO;
import com.midas.crm.entity.DTO.curso.ModuloMinimalDTO;

import java.util.List;

/**
 * Servicio optimizado para cursos que reduce la cantidad de datos transferidos
 */
public interface CursoOptimizedService {

    /**
     * Obtiene cursos optimizados por lista de IDs
     * Solo incluye los campos necesarios para la vista de lista
     */
    List<CursoListDTO> getCursosOptimizedByIds(List<Long> ids);

    /**
     * Obtiene módulos optimizados por curso ID
     * Solo incluye los campos necesarios para la vista de módulos
     */
    List<ModuloMinimalDTO> getModulosOptimizedByCursoId(Long cursoId);

    /**
     * Obtiene un curso optimizado por ID
     * Solo incluye los campos necesarios para la vista de detalle
     */
    CursoListDTO getCursoOptimizedById(Long cursoId);
}

import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { environment } from '@src/environments/environment';
import { GenericResponse } from '@app/models/backend/generic-response';
import {
  EnviarEmailRequest,
  EmailMensajeResumen,
  EmailMensajeDetalle,
  EstadoConexionGmail,
  BandejaEntradaResponse
} from '../models/gmail.models';

@Injectable({
  providedIn: 'root'
})
export class GmailService {
  private readonly baseUrl = `${environment.url}api/gmail`;

  constructor(private http: HttpClient) {}

  /**
   * Verifica si el usuario tiene su cuenta de Gmail conectada
   * Usa el nuevo endpoint del backend que verifica directamente el estado
   */
  verificarConexion(): Observable<GenericResponse<EstadoConexionGmail>> {
    return this.http.get<GenericResponse<boolean>>(`${this.baseUrl}/estado-conexion`).pipe(
      map(response => {
        if (response.rpta === 1) {
          return {
            rpta: 1,
            msg: response.msg,
            data: {
              conectado: response.data,
              email: response.data ? 'Cuenta conectada' : undefined,
              mensaje: response.data ? 'Tu cuenta de Gmail está conectada correctamente' : 'No tienes una cuenta de Gmail conectada'
            }
          };
        } else {
          return {
            rpta: 1,
            msg: 'No conectado',
            data: {
              conectado: false,
              mensaje: 'No tienes una cuenta de Gmail conectada'
            }
          };
        }
      }),
      catchError(error => {
        console.error('Error al verificar conexión Gmail:', error);
        return of({
          rpta: 1,
          msg: 'No conectado',
          data: {
            conectado: false,
            mensaje: 'No tienes una cuenta de Gmail conectada'
          }
        });
      })
    );
  }

  /**
   * Inicia el proceso de autorización OAuth2 para Gmail
   * Usa el nuevo endpoint del backend que devuelve la URL de autorización
   */
  iniciarAutorizacionOAuth(): Observable<void> {
    console.log('Iniciando OAuth - obteniendo URL de autorización del backend');

    return this.http.get<GenericResponse<string>>(`${this.baseUrl}/url-autorizacion`).pipe(
      map(response => {
        if (response.rpta === 1 && response.data) {
          console.log('URL de autorización obtenida:', response.data);
          // Redirigir directamente a la URL de Google OAuth
          window.location.href = response.data;
        } else {
          throw new Error(response.msg || 'No se pudo obtener la URL de autorización');
        }
      }),
      catchError(error => {
        console.error('Error al obtener URL de autorización:', error);
        throw error;
      })
    );
  }

  /**
   * Método helper para obtener la URL base
   */
  getBaseUrl(): string {
    return environment.url;
  }



  /**
   * Obtiene la bandeja de entrada del usuario
   */
  obtenerBandejaEntrada(maxResults: number = 20): Observable<GenericResponse<EmailMensajeResumen[]>> {
    return this.http.get<GenericResponse<EmailMensajeResumen[]>>(
      `${this.baseUrl}/bandeja-entrada?limit=${maxResults}`
    );
  }

  /**
   * Obtiene el detalle de un mensaje específico
   */
  obtenerDetalleMensaje(idMensaje: string): Observable<GenericResponse<EmailMensajeDetalle>> {
    return this.http.get<GenericResponse<EmailMensajeDetalle>>(`${this.baseUrl}/mensajes/${idMensaje}`);
  }

  /**
   * Envía un correo electrónico
   */
  enviarEmail(request: EnviarEmailRequest): Observable<GenericResponse<void>> {
    return this.http.post<GenericResponse<void>>(`${this.baseUrl}/enviar`, request);
  }

  /**
   * Descarga un adjunto
   */
  descargarAdjunto(idMensaje: string, idAdjunto: string): Observable<Blob> {
    return this.http.get(`${this.baseUrl}/mensajes/${idMensaje}/adjuntos/${idAdjunto}`, {
      responseType: 'blob'
    });
  }

  /**
   * Desconecta la cuenta de Gmail del usuario
   */
  desconectarCuenta(): Observable<GenericResponse<void>> {
    return this.http.delete<GenericResponse<void>>(`${this.baseUrl}/desconectar`);
  }
}

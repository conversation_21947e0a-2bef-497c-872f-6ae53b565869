import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, of, throwError } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { environment } from '@src/environments/environment';
import { GenericResponse } from '@app/models/backend/generic-response';
import {
  EnviarEmailRequest,
  EmailMensajeResumen,
  EmailMensajeDetalle,
  EstadoConexionGmail,
  BandejaEntradaResponse
} from '../models/gmail.models';

@Injectable({
  providedIn: 'root'
})
export class GmailService {
  private readonly baseUrl = `${environment.url}api/gmail`;

  constructor(private http: HttpClient) {}

  /**
   * Verifica si el usuario tiene su cuenta de Gmail conectada
   * Intenta obtener la bandeja de entrada para verificar la conexión
   */
  verificarConexion(): Observable<GenericResponse<EstadoConexionGmail>> {
    return this.obtenerBandejaEntrada(1).pipe(
      map(response => {
        if (response.rpta === 1) {
          return {
            rpta: 1,
            msg: 'Conexión verificada',
            data: {
              conectado: true,
              email: 'Cuenta conectada',
              mensaje: 'Tu cuenta de Gmail está conectada correctamente'
            }
          };
        } else {
          return {
            rpta: 1,
            msg: 'No conectado',
            data: {
              conectado: false,
              mensaje: 'No tienes una cuenta de Gmail conectada'
            }
          };
        }
      }),
      catchError(error => {
        return of({
          rpta: 1,
          msg: 'No conectado',
          data: {
            conectado: false,
            mensaje: 'No tienes una cuenta de Gmail conectada'
          }
        });
      })
    );
  }

  /**
   * Inicia el proceso de autorización OAuth2 para Gmail
   * Hace una petición HTTP al backend que manejará la redirección
   */
  iniciarAutorizacionOAuth(): Observable<any> {
    const headers = this.createAuthHeaders();

    // Hacer una petición GET al endpoint OAuth que debería redirigir
    return this.http.get(`${environment.url}oauth2/authorize/user`, {
      headers,
      observe: 'response',
      responseType: 'text'
    }).pipe(
      map(response => {
        // Si el backend devuelve una URL de redirección
        if (response.headers.get('Location')) {
          window.location.href = response.headers.get('Location')!;
        }
        return response;
      }),
      catchError(error => {
        // Si es una redirección (302, 301), extraer la URL
        if (error.status === 302 || error.status === 301) {
          const location = error.headers?.get('Location') || error.url;
          if (location) {
            window.location.href = location;
          }
        }
        return throwError(() => error);
      })
    );
  }

  /**
   * Crea headers con token de autorización
   */
  private createAuthHeaders(): HttpHeaders {
    const token = localStorage.getItem('token');
    let headers = new HttpHeaders();

    if (token) {
      headers = headers.set('Authorization', `Bearer ${token}`);
    }

    return headers;
  }

  /**
   * Obtiene la bandeja de entrada del usuario
   */
  obtenerBandejaEntrada(maxResults: number = 20): Observable<GenericResponse<EmailMensajeResumen[]>> {
    return this.http.get<GenericResponse<EmailMensajeResumen[]>>(
      `${this.baseUrl}/bandeja-entrada?limit=${maxResults}`
    );
  }

  /**
   * Obtiene el detalle de un mensaje específico
   */
  obtenerDetalleMensaje(idMensaje: string): Observable<GenericResponse<EmailMensajeDetalle>> {
    return this.http.get<GenericResponse<EmailMensajeDetalle>>(`${this.baseUrl}/mensajes/${idMensaje}`);
  }

  /**
   * Envía un correo electrónico
   */
  enviarEmail(request: EnviarEmailRequest): Observable<GenericResponse<void>> {
    return this.http.post<GenericResponse<void>>(`${this.baseUrl}/enviar`, request);
  }

  /**
   * Descarga un adjunto
   */
  descargarAdjunto(idMensaje: string, idAdjunto: string): Observable<Blob> {
    return this.http.get(`${this.baseUrl}/mensajes/${idMensaje}/adjuntos/${idAdjunto}`, {
      responseType: 'blob'
    });
  }

  /**
   * Desconecta la cuenta de Gmail del usuario
   */
  desconectarCuenta(): Observable<GenericResponse<void>> {
    return this.http.delete<GenericResponse<void>>(`${this.baseUrl}/desconectar`);
  }
}

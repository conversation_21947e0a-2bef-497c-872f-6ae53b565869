import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of, throwError } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { environment } from '@src/environments/environment';
import { GenericResponse } from '@app/models/backend/generic-response';
import {
  EnviarEmailRequest,
  EmailMensajeResumen,
  EmailMensajeDetalle,
  EstadoConexionGmail,
  BandejaEntradaResponse
} from '../models/gmail.models';

@Injectable({
  providedIn: 'root'
})
export class GmailService {
  private readonly baseUrl = `${environment.url}api/gmail`;

  constructor(private http: HttpClient) {}

  /**
   * Verifica si el usuario tiene su cuenta de Gmail conectada
   * Intenta obtener la bandeja de entrada para verificar la conexión
   */
  verificarConexion(): Observable<GenericResponse<EstadoConexionGmail>> {
    return this.obtenerBandejaEntrada(1).pipe(
      map(response => {
        if (response.rpta === 1) {
          return {
            rpta: 1,
            msg: 'Conexión verificada',
            data: {
              conectado: true,
              email: 'Cuenta conectada',
              mensaje: 'Tu cuenta de Gmail está conectada correctamente'
            }
          };
        } else {
          return {
            rpta: 1,
            msg: 'No conectado',
            data: {
              conectado: false,
              mensaje: 'No tienes una cuenta de Gmail conectada'
            }
          };
        }
      }),
      catchError(error => {
        return of({
          rpta: 1,
          msg: 'No conectado',
          data: {
            conectado: false,
            mensaje: 'No tienes una cuenta de Gmail conectada'
          }
        });
      })
    );
  }

  /**
   * Inicia el proceso de autorización OAuth2 para Gmail
   * Usa un enfoque de formulario oculto para enviar el token JWT
   */
  iniciarAutorizacionOAuth(): void {
    const token = localStorage.getItem('token');
    if (!token) {
      console.error('No hay token de autorización disponible');
      return;
    }

    console.log('Iniciando OAuth con formulario y token JWT');

    // Crear un formulario oculto que envíe el token como header
    const form = document.createElement('form');
    form.method = 'GET';
    form.action = `${environment.url}oauth2/authorize/user`;
    form.style.display = 'none';

    // Crear un input oculto para el token (aunque esto no funcionará como header)
    // En su lugar, vamos a usar fetch con credentials para que las cookies se envíen
    document.body.appendChild(form);

    // En lugar de enviar el formulario, hacer una petición fetch que permita al navegador seguir la redirección
    fetch(form.action, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      credentials: 'include',
      redirect: 'manual' // No seguir redirecciones automáticamente
    }).then(response => {
      console.log('Respuesta del servidor:', response);

      if (response.type === 'opaqueredirect' || response.status === 302 || response.status === 301) {
        // Obtener la URL de redirección del header Location
        const location = response.headers.get('Location');
        console.log('URL de redirección detectada:', location);

        if (location) {
          // Redirigir manualmente a la URL de Google
          window.location.href = location;
        } else {
          // Si no hay header Location, intentar con la URL del response
          console.log('No se encontró header Location, usando response.url');
          window.location.href = response.url || `${environment.url}oauth2/authorize/user`;
        }
      } else {
        console.error('No se recibió redirección del servidor');
      }
    }).catch(error => {
      console.log('Error en fetch OAuth:', error);

      // Si hay error CORS, es probable que la redirección haya funcionado
      if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
        console.log('Error CORS - redirigiendo directamente al endpoint');
        // Como último recurso, redirigir directamente al endpoint
        // El navegador enviará las cookies de sesión automáticamente
        window.location.href = `${environment.url}oauth2/authorize/user`;
      } else {
        console.error('Error inesperado:', error);
      }
    }).finally(() => {
      // Limpiar el formulario
      document.body.removeChild(form);
    });
  }

  /**
   * Método helper para obtener la URL base
   */
  getBaseUrl(): string {
    return environment.url;
  }



  /**
   * Obtiene la bandeja de entrada del usuario
   */
  obtenerBandejaEntrada(maxResults: number = 20): Observable<GenericResponse<EmailMensajeResumen[]>> {
    return this.http.get<GenericResponse<EmailMensajeResumen[]>>(
      `${this.baseUrl}/bandeja-entrada?limit=${maxResults}`
    );
  }

  /**
   * Obtiene el detalle de un mensaje específico
   */
  obtenerDetalleMensaje(idMensaje: string): Observable<GenericResponse<EmailMensajeDetalle>> {
    return this.http.get<GenericResponse<EmailMensajeDetalle>>(`${this.baseUrl}/mensajes/${idMensaje}`);
  }

  /**
   * Envía un correo electrónico
   */
  enviarEmail(request: EnviarEmailRequest): Observable<GenericResponse<void>> {
    return this.http.post<GenericResponse<void>>(`${this.baseUrl}/enviar`, request);
  }

  /**
   * Descarga un adjunto
   */
  descargarAdjunto(idMensaje: string, idAdjunto: string): Observable<Blob> {
    return this.http.get(`${this.baseUrl}/mensajes/${idMensaje}/adjuntos/${idAdjunto}`, {
      responseType: 'blob'
    });
  }

  /**
   * Desconecta la cuenta de Gmail del usuario
   */
  desconectarCuenta(): Observable<GenericResponse<void>> {
    return this.http.delete<GenericResponse<void>>(`${this.baseUrl}/desconectar`);
  }
}

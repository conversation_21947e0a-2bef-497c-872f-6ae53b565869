import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of, throwError } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { environment } from '@src/environments/environment';
import { GenericResponse } from '@app/models/backend/generic-response';
import {
  EnviarEmailRequest,
  EmailMensajeResumen,
  EmailMensajeDetalle,
  EstadoConexionGmail,
  BandejaEntradaResponse
} from '../models/gmail.models';

@Injectable({
  providedIn: 'root'
})
export class GmailService {
  private readonly baseUrl = `${environment.url}api/gmail`;

  constructor(private http: HttpClient) {}

  /**
   * Verifica si el usuario tiene su cuenta de Gmail conectada
   * Intenta obtener la bandeja de entrada para verificar la conexión
   */
  verificarConexion(): Observable<GenericResponse<EstadoConexionGmail>> {
    return this.obtenerBandejaEntrada(1).pipe(
      map(response => {
        if (response.rpta === 1) {
          return {
            rpta: 1,
            msg: 'Conexión verificada',
            data: {
              conectado: true,
              email: 'Cuenta conectada',
              mensaje: 'Tu cuenta de Gmail está conectada correctamente'
            }
          };
        } else {
          return {
            rpta: 1,
            msg: 'No conectado',
            data: {
              conectado: false,
              mensaje: 'No tienes una cuenta de Gmail conectada'
            }
          };
        }
      }),
      catchError(error => {
        return of({
          rpta: 1,
          msg: 'No conectado',
          data: {
            conectado: false,
            mensaje: 'No tienes una cuenta de Gmail conectada'
          }
        });
      })
    );
  }

  /**
   * Inicia el proceso de autorización OAuth2 para Gmail
   * Hace una petición HTTP que obtendrá la URL de autorización
   */
  iniciarAutorizacionOAuth(): Observable<string> {
    return this.http.get(`${environment.url}oauth2/authorize/user`, {
      observe: 'response',
      responseType: 'text'
    }).pipe(
      map(response => {
        // Si llegamos aquí, algo no está bien porque debería haber una redirección
        console.log('Respuesta inesperada:', response);
        throw new Error('No se recibió redirección del servidor');
      }),
      catchError(error => {
        console.log('Error capturado en OAuth:', error);

        // Si es un error de red (status 0), puede ser que la redirección haya funcionado
        // pero Angular no puede seguirla debido a CORS
        if (error.status === 0) {
          // Extraer la URL de redirección del error si está disponible
          const redirectUrl = error.url || `${environment.url}oauth2/authorize/user`;
          console.log('Redirección detectada a:', redirectUrl);
          return of(redirectUrl);
        }

        // Para otros errores, propagarlos
        return throwError(() => error);
      })
    );
  }

  /**
   * Método helper para obtener la URL base
   */
  getBaseUrl(): string {
    return environment.url;
  }



  /**
   * Obtiene la bandeja de entrada del usuario
   */
  obtenerBandejaEntrada(maxResults: number = 20): Observable<GenericResponse<EmailMensajeResumen[]>> {
    return this.http.get<GenericResponse<EmailMensajeResumen[]>>(
      `${this.baseUrl}/bandeja-entrada?limit=${maxResults}`
    );
  }

  /**
   * Obtiene el detalle de un mensaje específico
   */
  obtenerDetalleMensaje(idMensaje: string): Observable<GenericResponse<EmailMensajeDetalle>> {
    return this.http.get<GenericResponse<EmailMensajeDetalle>>(`${this.baseUrl}/mensajes/${idMensaje}`);
  }

  /**
   * Envía un correo electrónico
   */
  enviarEmail(request: EnviarEmailRequest): Observable<GenericResponse<void>> {
    return this.http.post<GenericResponse<void>>(`${this.baseUrl}/enviar`, request);
  }

  /**
   * Descarga un adjunto
   */
  descargarAdjunto(idMensaje: string, idAdjunto: string): Observable<Blob> {
    return this.http.get(`${this.baseUrl}/mensajes/${idMensaje}/adjuntos/${idAdjunto}`, {
      responseType: 'blob'
    });
  }

  /**
   * Desconecta la cuenta de Gmail del usuario
   */
  desconectarCuenta(): Observable<GenericResponse<void>> {
    return this.http.delete<GenericResponse<void>>(`${this.baseUrl}/desconectar`);
  }
}

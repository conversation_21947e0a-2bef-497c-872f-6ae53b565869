import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of, throwError } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { environment } from '@src/environments/environment';
import { GenericResponse } from '@app/models/backend/generic-response';
import {
  EnviarEmailRequest,
  EmailMensajeResumen,
  EmailMensajeDetalle,
  EstadoConexionGmail,
  BandejaEntradaResponse
} from '../models/gmail.models';

@Injectable({
  providedIn: 'root'
})
export class GmailService {
  private readonly baseUrl = `${environment.url}api/gmail`;

  constructor(private http: HttpClient) {}

  /**
   * Verifica si el usuario tiene su cuenta de Gmail conectada
   * Intenta obtener la bandeja de entrada para verificar la conexión
   */
  verificarConexion(): Observable<GenericResponse<EstadoConexionGmail>> {
    return this.obtenerBandejaEntrada(1).pipe(
      map(response => {
        if (response.rpta === 1) {
          return {
            rpta: 1,
            msg: 'Conexión verificada',
            data: {
              conectado: true,
              email: 'Cuenta conectada',
              mensaje: 'Tu cuenta de Gmail está conectada correctamente'
            }
          };
        } else {
          return {
            rpta: 1,
            msg: 'No conectado',
            data: {
              conectado: false,
              mensaje: 'No tienes una cuenta de Gmail conectada'
            }
          };
        }
      }),
      catchError(error => {
        return of({
          rpta: 1,
          msg: 'No conectado',
          data: {
            conectado: false,
            mensaje: 'No tienes una cuenta de Gmail conectada'
          }
        });
      })
    );
  }

  /**
   * Inicia el proceso de autorización OAuth2 para Gmail
   * Abre una nueva ventana para manejar el flujo OAuth
   */
  iniciarAutorizacionOAuth(): void {
    const token = localStorage.getItem('token');
    if (!token) {
      console.error('No hay token de autorización disponible');
      return;
    }

    // Abrir una nueva ventana para el flujo OAuth
    const popup = window.open('', 'oauth-popup', 'width=600,height=700,scrollbars=yes,resizable=yes');

    if (!popup) {
      console.error('No se pudo abrir la ventana popup');
      return;
    }

    // Crear el contenido HTML de la ventana popup que hará la petición con el token
    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Conectando con Gmail...</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background: #f5f5f5;
          }
          .loading { text-align: center; }
          .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
            margin: 0 auto 20px;
          }
          @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        </style>
      </head>
      <body>
        <div class="loading">
          <div class="spinner"></div>
          <h3>Conectando con Gmail...</h3>
          <p>Redirigiendo a Google para autorizar el acceso...</p>
        </div>
        <script>
          // Hacer la petición con el token
          fetch('${environment.url}oauth2/authorize/user', {
            method: 'GET',
            headers: {
              'Authorization': 'Bearer ${token}',
              'Content-Type': 'application/json'
            },
            credentials: 'include'
          }).then(response => {
            // Si la respuesta es una redirección, seguirla
            if (response.redirected) {
              window.location.href = response.url;
            } else if (response.status === 302 || response.status === 301) {
              // Manejar redirecciones manuales
              const location = response.headers.get('Location');
              if (location) {
                window.location.href = location;
              }
            } else {
              // Si no hay redirección, algo salió mal
              console.error('No se recibió redirección del servidor');
              window.close();
            }
          }).catch(error => {
            console.error('Error en la petición OAuth:', error);
            // Si hay un error CORS, es probable que la redirección haya funcionado
            if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
              console.log('Error CORS detectado, probablemente la redirección funcionó');
              // Intentar redirigir manualmente
              window.location.href = '${environment.url}oauth2/authorize/user';
            } else {
              alert('Error al conectar con Gmail: ' + error.message);
              window.close();
            }
          });
        </script>
      </body>
      </html>
    `;

    // Escribir el contenido en la ventana popup
    popup.document.write(htmlContent);
    popup.document.close();
  }

  /**
   * Método helper para obtener la URL base
   */
  getBaseUrl(): string {
    return environment.url;
  }



  /**
   * Obtiene la bandeja de entrada del usuario
   */
  obtenerBandejaEntrada(maxResults: number = 20): Observable<GenericResponse<EmailMensajeResumen[]>> {
    return this.http.get<GenericResponse<EmailMensajeResumen[]>>(
      `${this.baseUrl}/bandeja-entrada?limit=${maxResults}`
    );
  }

  /**
   * Obtiene el detalle de un mensaje específico
   */
  obtenerDetalleMensaje(idMensaje: string): Observable<GenericResponse<EmailMensajeDetalle>> {
    return this.http.get<GenericResponse<EmailMensajeDetalle>>(`${this.baseUrl}/mensajes/${idMensaje}`);
  }

  /**
   * Envía un correo electrónico
   */
  enviarEmail(request: EnviarEmailRequest): Observable<GenericResponse<void>> {
    return this.http.post<GenericResponse<void>>(`${this.baseUrl}/enviar`, request);
  }

  /**
   * Descarga un adjunto
   */
  descargarAdjunto(idMensaje: string, idAdjunto: string): Observable<Blob> {
    return this.http.get(`${this.baseUrl}/mensajes/${idMensaje}/adjuntos/${idAdjunto}`, {
      responseType: 'blob'
    });
  }

  /**
   * Desconecta la cuenta de Gmail del usuario
   */
  desconectarCuenta(): Observable<GenericResponse<void>> {
    return this.http.delete<GenericResponse<void>>(`${this.baseUrl}/desconectar`);
  }
}

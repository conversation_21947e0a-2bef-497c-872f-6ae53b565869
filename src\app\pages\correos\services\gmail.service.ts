import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { environment } from '@src/environments/environment';
import { GenericResponse } from '@app/models/backend/generic-response';
import {
  EnviarEmailRequest,
  EmailMensajeResumen,
  EmailMensajeDetalle,
  EstadoConexionGmail,
  BandejaEntradaResponse
} from '../models/gmail.models';

@Injectable({
  providedIn: 'root'
})
export class GmailService {
  private readonly baseUrl = `${environment.url}api/gmail`;

  constructor(private http: HttpClient) {}

  /**
   * Verifica si el usuario tiene su cuenta de Gmail conectada
   * Intenta obtener la bandeja de entrada para verificar la conexión
   */
  verificarConexion(): Observable<GenericResponse<EstadoConexionGmail>> {
    return this.obtenerBandejaEntrada(1).pipe(
      map(response => {
        if (response.rpta === 1) {
          return {
            rpta: 1,
            msg: 'Conexión verificada',
            data: {
              conectado: true,
              email: 'Cuenta conectada',
              mensaje: 'Tu cuenta de Gmail está conectada correctamente'
            }
          };
        } else {
          return {
            rpta: 1,
            msg: 'No conectado',
            data: {
              conectado: false,
              mensaje: 'No tienes una cuenta de Gmail conectada'
            }
          };
        }
      }),
      catchError(error => {
        return of({
          rpta: 1,
          msg: 'No conectado',
          data: {
            conectado: false,
            mensaje: 'No tienes una cuenta de Gmail conectada'
          }
        });
      })
    );
  }

  /**
   * Inicia el proceso de autorización OAuth2 para Gmail
   * Usa una ventana popup con JavaScript para manejar la autenticación
   */
  iniciarAutorizacionOAuth(): void {
    const token = localStorage.getItem('token');
    if (!token) {
      console.error('No hay token de autorización disponible');
      return;
    }

    console.log('Iniciando OAuth con ventana popup y token JWT');

    // Crear una ventana popup
    const popup = window.open('', 'oauth-popup', 'width=600,height=700,scrollbars=yes,resizable=yes');

    if (!popup) {
      console.error('No se pudo abrir la ventana popup - verifique que los popups estén habilitados');
      return;
    }

    // Crear el contenido HTML de la ventana popup
    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Conectando con Gmail...</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background: #f5f5f5;
          }
          .loading { text-align: center; }
          .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
            margin: 0 auto 20px;
          }
          @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        </style>
      </head>
      <body>
        <div class="loading">
          <div class="spinner"></div>
          <h3>Conectando con Gmail...</h3>
          <p>Redirigiendo a Google para autorizar el acceso...</p>
        </div>
        <script>
          console.log('Popup cargado, iniciando petición OAuth');

          // Hacer la petición con XMLHttpRequest para tener más control
          const xhr = new XMLHttpRequest();
          xhr.open('GET', '${environment.url}oauth2/authorize/user', true);
          xhr.setRequestHeader('Authorization', 'Bearer ${token}');
          xhr.setRequestHeader('Content-Type', 'application/json');

          xhr.onreadystatechange = function() {
            console.log('XHR State:', xhr.readyState, 'Status:', xhr.status);

            if (xhr.readyState === 4) {
              if (xhr.status === 302 || xhr.status === 301) {
                // Redirección exitosa
                const location = xhr.getResponseHeader('Location');
                console.log('Redirección detectada a:', location);
                if (location) {
                  window.location.href = location;
                }
              } else if (xhr.status === 200) {
                // Respuesta exitosa sin redirección
                console.log('Respuesta 200 recibida');
                const responseText = xhr.responseText;
                if (responseText.includes('google.com')) {
                  // Buscar URL de Google en la respuesta
                  const match = responseText.match(/https:\\/\\/accounts\\.google\\.com\\/o\\/oauth2\\/auth[^"\\s]*/);
                  if (match) {
                    window.location.href = match[0];
                  }
                }
              } else if (xhr.status === 403) {
                alert('Error 403: No tienes permisos para conectar Gmail. El token JWT no se está enviando correctamente.');
                window.close();
              } else {
                console.error('Error en petición OAuth:', xhr.status, xhr.statusText);
                alert('Error al conectar con Gmail: ' + xhr.status + ' ' + xhr.statusText);
                window.close();
              }
            }
          };

          xhr.onerror = function() {
            console.error('Error de red en petición OAuth');
            // Si hay error de red, intentar redirección directa
            window.location.href = '${environment.url}oauth2/authorize/user';
          };

          xhr.send();
        </script>
      </body>
      </html>
    `;

    // Escribir el contenido en la ventana popup
    popup.document.write(htmlContent);
    popup.document.close();
  }

  /**
   * Método helper para obtener la URL base
   */
  getBaseUrl(): string {
    return environment.url;
  }



  /**
   * Obtiene la bandeja de entrada del usuario
   */
  obtenerBandejaEntrada(maxResults: number = 20): Observable<GenericResponse<EmailMensajeResumen[]>> {
    return this.http.get<GenericResponse<EmailMensajeResumen[]>>(
      `${this.baseUrl}/bandeja-entrada?limit=${maxResults}`
    );
  }

  /**
   * Obtiene el detalle de un mensaje específico
   */
  obtenerDetalleMensaje(idMensaje: string): Observable<GenericResponse<EmailMensajeDetalle>> {
    return this.http.get<GenericResponse<EmailMensajeDetalle>>(`${this.baseUrl}/mensajes/${idMensaje}`);
  }

  /**
   * Envía un correo electrónico
   */
  enviarEmail(request: EnviarEmailRequest): Observable<GenericResponse<void>> {
    return this.http.post<GenericResponse<void>>(`${this.baseUrl}/enviar`, request);
  }

  /**
   * Descarga un adjunto
   */
  descargarAdjunto(idMensaje: string, idAdjunto: string): Observable<Blob> {
    return this.http.get(`${this.baseUrl}/mensajes/${idMensaje}/adjuntos/${idAdjunto}`, {
      responseType: 'blob'
    });
  }

  /**
   * Desconecta la cuenta de Gmail del usuario
   */
  desconectarCuenta(): Observable<GenericResponse<void>> {
    return this.http.delete<GenericResponse<void>>(`${this.baseUrl}/desconectar`);
  }
}

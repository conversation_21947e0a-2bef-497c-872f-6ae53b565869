package com.midas.crm.repository;

import com.midas.crm.entity.AudioSinLead;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * Repositorio para manejar audios sin leads correspondientes
 * CORREGIDO: Sintaxis completa de métodos
 */
@Repository
public interface AudioSinLeadRepository extends JpaRepository<AudioSinLead, Long> {

    // ===== MÉTODOS EXISTENTES (mantener todos los que ya tienes) =====

    Optional<AudioSinLead> findByNombreArchivo(String nombreArchivo);
    Optional<AudioSinLead> findByGoogleDriveFileId(String googleDriveFileId);
    List<AudioSinLead> findByMovilExtraido(String movilExtraido);
    List<AudioSinLead> findByAgenteExtraido(String agenteExtraido);
    List<AudioSinLead> findByEstado(String estado);
    List<AudioSinLead> findByFechaProcesamientoBetween(LocalDateTime inicio, LocalDateTime fin);
    Page<AudioSinLead> findByFechaProcesamientoBetween(LocalDateTime inicio, LocalDateTime fin, Pageable pageable);
    List<AudioSinLead> findByRequiereProcesamientoTrue();
    List<AudioSinLead> findByPrioridad(String prioridad);
    List<AudioSinLead> findByMovilExtraidoAndAgenteExtraido(String movilExtraido, String agenteExtraido);
    List<AudioSinLead> findByFechaExtraidaBetween(LocalDateTime inicio, LocalDateTime fin);
    boolean existsByNombreArchivo(String nombreArchivo);
    boolean existsByGoogleDriveFileId(String googleDriveFileId);
    long countByEstado(String estado);
    long countByRequiereProcesamientoTrue();

    @Query("SELECT DATE(a.fechaProcesamiento) as fecha, COUNT(a) as total, " +
            "COUNT(CASE WHEN a.estado = 'ENCONTRADO' THEN 1 END) as encontrados, " +
            "COUNT(CASE WHEN a.estado = 'PROCESADO' THEN 1 END) as procesados, " +
            "COUNT(CASE WHEN a.estado = 'IGNORADO' THEN 1 END) as ignorados " +
            "FROM AudioSinLead a " +
            "WHERE a.fechaProcesamiento BETWEEN :inicio AND :fin " +
            "GROUP BY DATE(a.fechaProcesamiento) " +
            "ORDER BY DATE(a.fechaProcesamiento) DESC")
    List<Object[]> getEstadisticasPorFecha(@Param("inicio") LocalDateTime inicio, @Param("fin") LocalDateTime fin);

    @Query("SELECT a.agenteExtraido as agente, COUNT(a) as total, " +
            "COUNT(CASE WHEN a.estado = 'ENCONTRADO' THEN 1 END) as encontrados, " +
            "COUNT(CASE WHEN a.estado = 'PROCESADO' THEN 1 END) as procesados " +
            "FROM AudioSinLead a " +
            "WHERE a.fechaProcesamiento BETWEEN :inicio AND :fin " +
            "AND a.agenteExtraido IS NOT NULL " +
            "GROUP BY a.agenteExtraido " +
            "ORDER BY COUNT(a) DESC")
    List<Object[]> getEstadisticasPorAgente(@Param("inicio") LocalDateTime inicio, @Param("fin") LocalDateTime fin);

    @Query("SELECT a FROM AudioSinLead a WHERE a.movilExtraido = :movil " +
            "AND a.agenteExtraido = :agente " +
            "AND DATE(a.fechaExtraida) = DATE(:fecha)")
    List<AudioSinLead> findDuplicados(@Param("movil") String movil,
                                      @Param("agente") String agente,
                                      @Param("fecha") LocalDateTime fecha);

    // **FIXED**: Removed time constraint from query and method signature
    @Query("SELECT a FROM AudioSinLead a " +
            "WHERE a.requiereProcesamiento = true " +
            "AND (:prioridad IS NULL OR a.prioridad = :prioridad) " +
            "ORDER BY a.fechaEncontrado DESC")
    Page<AudioSinLead> findRecientesYRequieren(@Param("prioridad") String prioridad, Pageable pageable);

    // **FIXED**: Removed time constraint from query and method signature
    @Query("SELECT a FROM AudioSinLead a " +
            "WHERE a.requiereProcesamiento = true " +
            "AND (:prioridad IS NULL OR a.prioridad = :prioridad) " +
            "ORDER BY a.fechaEncontrado DESC")
    List<AudioSinLead> findRecientesYRequieren(@Param("prioridad") String prioridad);

    @Query("SELECT a FROM AudioSinLead a WHERE a.fechaEncontrado >= :fecha ORDER BY a.fechaEncontrado DESC")
    List<AudioSinLead> findRecientes(@Param("fecha") LocalDateTime fecha);

    List<AudioSinLead> findByCarpetaOrigen(String carpetaOrigen);

    @Query("DELETE FROM AudioSinLead a WHERE a.fechaEncontrado < :fechaLimite")
    void deleteAntiguos(@Param("fechaLimite") LocalDateTime fechaLimite);

    @Query("SELECT a FROM AudioSinLead a WHERE a.movilExtraido IS NOT NULL " +
            "AND a.movilExtraido != '' AND a.agenteExtraido IS NOT NULL " +
            "AND a.agenteExtraido != '' ORDER BY a.fechaEncontrado DESC")
    List<AudioSinLead> findConInformacionValida();

    @Query("SELECT a FROM AudioSinLead a WHERE a.movilExtraido IS NULL " +
            "OR a.movilExtraido = '' OR a.agenteExtraido IS NULL " +
            "OR a.agenteExtraido = '' ORDER BY a.fechaEncontrado DESC")
    List<AudioSinLead> findConInformacionIncompleta();

    @Query("UPDATE AudioSinLead a SET a.estado = :nuevoEstado, a.fechaActualizacion = :fecha " +
            "WHERE a.id IN :ids")
    void updateEstadoMultiple(@Param("ids") List<Long> ids,
                              @Param("nuevoEstado") String nuevoEstado,
                              @Param("fecha") LocalDateTime fecha);

    @Query("SELECT a FROM AudioSinLead a WHERE a.nombreArchivo LIKE %:patron% ORDER BY a.fechaEncontrado DESC")
    List<AudioSinLead> findByNombreArchivoContaining(@Param("patron") String patron);

    // ===== NUEVOS MÉTODOS CORREGIDOS =====

    @Query(value =
            "SELECT " +
                    "  COUNT(DISTINCT asl.id)                           AS total_audios_sin_lead, " +
                    "  COUNT(DISTINCT asl.agente_extraido)              AS agentes_con_audios_huerfanos, " +
                    "  SUM(CASE WHEN asl.estado = 'ENCONTRADO' THEN 1 ELSE 0 END) AS encontrados, " +
                    "  SUM(CASE WHEN asl.estado = 'PROCESADO' THEN 1 ELSE 0 END)  AS procesados, " +
                    "  SUM(CASE WHEN asl.estado = 'IGNORADO' THEN 1 ELSE 0 END)   AS ignorados, " +
                    "  SUM(CASE WHEN asl.requiere_procesamiento = true THEN 1 ELSE 0 END) AS requieren_procesamiento " +
                    "FROM audio_sin_lead asl " +
                    "WHERE asl.agente_extraido COLLATE utf8mb4_0900_ai_ci IN ( " +
                    "    SELECT DISTINCT c.numero_agente COLLATE utf8mb4_0900_ai_ci " +
                    "      FROM cliente_residencial c " +
                    "      JOIN usuarios u ON c.usuario_id = u.codi_usuario " +
                    "     WHERE u.coordinador_id = :coordinadorId " +
                    "       AND c.numero_agente IS NOT NULL " +
                    "       AND c.numero_agente <> '' " +
                    "       AND u.role COLLATE utf8mb4_0900_ai_ci = 'ASESOR' " +
                    ") " +
                    "  AND (:fechaInicio IS NULL OR :fechaFin IS NULL " +
                    "       OR DATE(asl.fecha_extraida) BETWEEN :fechaInicio AND :fechaFin)",
            nativeQuery = true
    )
    Object[] getEstadisticasAudiosSinLeadPorCoordinador(
            @Param("coordinadorId") Long coordinadorId,
            @Param("fechaInicio") LocalDate fechaInicio,
            @Param("fechaFin")   LocalDate fechaFin
    );


    /**
     * Obtiene audios sin lead por agente específico
     */
    @Query("SELECT a FROM AudioSinLead a WHERE a.agenteExtraido = :numeroAgente " +
            "AND (:fechaInicio IS NULL OR :fechaFin IS NULL OR DATE(a.fechaExtraida) BETWEEN :fechaInicio AND :fechaFin) " +
            "ORDER BY a.fechaEncontrado DESC")
    List<AudioSinLead> findByAgenteYRangoFechas(@Param("numeroAgente") String numeroAgente,
                                                @Param("fechaInicio") LocalDate fechaInicio,
                                                @Param("fechaFin") LocalDate fechaFin);

    /**
     * Cuenta audios sin lead por agente y rango de fechas
     */
    @Query("SELECT COUNT(a) FROM AudioSinLead a WHERE a.agenteExtraido = :numeroAgente " +
            "AND (:fechaInicio IS NULL OR :fechaFin IS NULL OR DATE(a.fechaExtraida) BETWEEN :fechaInicio AND :fechaFin)")
    Long countByAgenteYRangoFechas(@Param("numeroAgente") String numeroAgente,
                                   @Param("fechaInicio") LocalDate fechaInicio,
                                   @Param("fechaFin") LocalDate fechaFin);
    /**
     * Obtiene estadísticas detalladas por agente para un coordinador
     * CORREGIDO: Solucionado problema de collation
     */
    @Query(value = "SELECT " +
            "asl.agente_extraido as numero_agente, " +
            "COUNT(DISTINCT asl.id) as total_audios_sin_lead, " +
            "SUM(CASE WHEN asl.estado = 'ENCONTRADO' THEN 1 ELSE 0 END) as encontrados, " +
            "SUM(CASE WHEN asl.estado = 'PROCESADO' THEN 1 ELSE 0 END) as procesados, " +
            "SUM(CASE WHEN asl.estado = 'IGNORADO' THEN 1 ELSE 0 END) as ignorados, " +
            "MIN(DATE(asl.fecha_extraida)) as fecha_primer_audio, " +
            "MAX(DATE(asl.fecha_extraida)) as fecha_ultimo_audio " +
            "FROM audio_sin_lead asl " +
            "WHERE asl.agente_extraido COLLATE utf8mb4_unicode_ci IN ( " +
            "    SELECT DISTINCT c.numero_agente COLLATE utf8mb4_unicode_ci " +
            "    FROM cliente_residencial c " +
            "    INNER JOIN usuarios u ON c.usuario_id = u.codi_usuario " +
            "    WHERE u.coordinador_id = :coordinadorId " +
            "    AND c.numero_agente IS NOT NULL " +
            "    AND c.numero_agente != '' " +
            ") " +
            "AND (:fechaInicio IS NULL OR :fechaFin IS NULL OR DATE(asl.fecha_extraida) BETWEEN :fechaInicio AND :fechaFin) " +
            "GROUP BY asl.agente_extraido " +
            "ORDER BY total_audios_sin_lead DESC",
            nativeQuery = true)
    List<Object[]> getEstadisticasDetalladasPorAgente(@Param("coordinadorId") Long coordinadorId,
                                                      @Param("fechaInicio") LocalDate fechaInicio,
                                                      @Param("fechaFin") LocalDate fechaFin);

    /**
     * Busca todos los AudioSinLead cuyo agenteExtraido pertenezca a
     * un cliente residencial de asesores de un coordinador y sede dados,
     * y cuyo procesamiento esté en el rango de fechas indicado.
     */
    @Query("""
      SELECT a
        FROM AudioSinLead a
       WHERE a.agenteExtraido IN (
         SELECT c.numeroAgente
           FROM ClienteResidencial c
           JOIN c.usuario u
          WHERE u.coordinador.nombre = :nombreCoordinador
            AND u.sede.id           = :sedeId
            AND c.numeroAgente IS NOT NULL
            AND c.numeroAgente <> ''
       )
         AND a.fechaProcesamiento BETWEEN :desde AND :hasta
    """)
    List<AudioSinLead> buscarPorCoordinadorYSedeYRango(
            @Param("nombreCoordinador") String nombreCoordinador,
            @Param("sedeId")           Long sedeId,
            @Param("desde")            LocalDateTime desde,
            @Param("hasta")            LocalDateTime hasta
    );

    /**
     * Busca todos los AudioSinLead cuyo agenteExtraido pertenezca
     * a clientes residenciales de asesores de un coordinador (por id)
     * y opcionalmente de una sede dada, y cuyo procesamiento esté
     * entre :desde y :hasta.
     */
    @Query(
            "SELECT a " +
                    "  FROM AudioSinLead a " +
                    " WHERE a.agenteExtraido IN ( " +
                    "     SELECT cr.numeroAgente " +
                    "       FROM ClienteResidencial cr " +
                    "      WHERE cr.usuario.coordinador.id = :coordId " +
                    "        AND (:sedeId IS NULL OR cr.usuario.sede.id = :sedeId) " +
                    "        AND cr.numeroAgente IS NOT NULL " +
                    "        AND cr.numeroAgente <> '' " +
                    " ) " +
                    "   AND a.fechaProcesamiento BETWEEN :desde AND :hasta"
    )
    List<AudioSinLead> findByCoordinadorYSedeYRangoProcesamiento(
            @Param("coordId") Long coordId,
            @Param("sedeId")  Long sedeId,      // pásalo null para no filtrar por sede
            @Param("desde")   LocalDateTime desde,
            @Param("hasta")   LocalDateTime hasta
    );

    /**
     * Devuelve los agentes extraídos (distinct) de los audios sin lead, filtrando
     * por un conjunto de números de agente y un rango de fecha.
     */
    @Query("""
      SELECT DISTINCT a.agenteExtraido
        FROM AudioSinLead a
       WHERE a.agenteExtraido IN :agentes
         AND a.fechaExtraida BETWEEN :desde AND :hasta
    """)
    List<String> findDistinctAgenteExtraidoByAgentesAndFechaRange(
            @Param("agentes") List<String> agentes,
            @Param("desde")   LocalDateTime desde,
            @Param("hasta")   LocalDateTime hasta
    );

    List<AudioSinLead> findByAgenteExtraidoInAndFechaExtraidaBetween(List<String> agentes, LocalDateTime desde, LocalDateTime hasta);

    /**
     * Busca audios sin lead con filtros múltiples aplicados
     * Incluye búsqueda por fecha en el nombre del archivo (formatohasFocusAreaMMdd)
     */
    @Query("SELECT a FROM AudioSinLead a WHERE " +
            "(:estado IS NULL OR a.estado = :estado) AND " +
            "(:prioridad IS NULL OR a.prioridad = :prioridad) AND " +
            "(:movil IS NULL OR a.movilExtraido = :movil) AND " +
            "(:agente IS NULL OR a.agenteExtraido = :agente) AND " +
            "(:carpetaOrigen IS NULL OR a.carpetaOrigen = :carpetaOrigen) AND " +
            "(:requiereProcesamiento IS NULL OR a.requiereProcesamiento = :requiereProcesamiento) AND " +
            "(:busquedaLeadRealizada IS NULL OR a.busquedaLeadRealizada = :busquedaLeadRealizada) AND " +
            "(:fechaInicio IS NULL OR :fechaFin IS NULL OR " +
            "  (a.fechaProcesamiento BETWEEN :fechaInicio AND :fechaFin OR " +
            "   (:fechaInicioStr IS NOT NULL AND :fechaFinStr IS NOT NULL AND " +
            "    SUBSTRING(a.nombreArchivo, 1, 8) BETWEEN :fechaInicioStr AND :fechaFinStr))) " +
            "ORDER BY a.fechaEncontrado DESC")
    Page<AudioSinLead> findWithFilters(
            @Param("estado") String estado,
            @Param("prioridad") String prioridad,
            @Param("movil") String movil,
            @Param("agente") String agente,
            @Param("carpetaOrigen") String carpetaOrigen,
            @Param("requiereProcesamiento") Boolean requiereProcesamiento,
            @Param("busquedaLeadRealizada") Boolean busquedaLeadRealizada,
            @Param("fechaInicio") LocalDateTime fechaInicio,
            @Param("fechaFin") LocalDateTime fechaFin,
            @Param("fechaInicioStr") String fechaInicioStr,
            @Param("fechaFinStr") String fechaFinStr,
            Pageable pageable);

    /**
     * Busca audios sin lead con filtros múltiples aplicados (sin paginación)
     * Incluye búsqueda por fecha en el nombre del archivo (formatoResourcesErrorsMMdd)
     */
    @Query("SELECT a FROM AudioSinLead a WHERE " +
            "(:estado IS NULL OR a.estado = :estado) AND " +
            "(:prioridad IS NULL OR a.prioridad = :prioridad) AND " +
            "(:movil IS NULL OR a.movilExtraido = :movil) AND " +
            "(:agente IS NULL OR a.agenteExtraido = :agente) AND " +
            "(:carpetaOrigen IS NULL OR a.carpetaOrigen = :carpetaOrigen) AND " +
            "(:requiereProcesamiento IS NULL OR a.requiereProcesamiento = :requiereProcesamiento) AND " +
            "(:busquedaLeadRealizada IS NULL OR a.busquedaLeadRealizada = :busquedaLeadRealizada) AND " +
            "(:fechaInicio IS NULL OR :fechaFin IS NULL OR " +
            "  (a.fechaProcesamiento BETWEEN :fechaInicio AND :fechaFin OR " +
            "   (:fechaInicioStr IS NOT NULL AND :fechaFinStr IS NOT NULL AND " +
            "    SUBSTRING(a.nombreArchivo, 1, 8) BETWEEN :fechaInicioStr AND :fechaFinStr))) " +
            "ORDER BY a.fechaEncontrado DESC")
    List<AudioSinLead> findWithFilters(
            @Param("estado") String estado,
            @Param("prioridad") String prioridad,
            @Param("movil") String movil,
            @Param("agente") String agente,
            @Param("carpetaOrigen") String carpetaOrigen,
            @Param("requiereProcesamiento") Boolean requiereProcesamiento,
            @Param("busquedaLeadRealizada") Boolean busquedaLeadRealizada,
            @Param("fechaInicio") LocalDateTime fechaInicio,
            @Param("fechaFin") LocalDateTime fechaFin,
            @Param("fechaInicioStr") String fechaInicioStr,
            @Param("fechaFinStr") String fechaFinStr);

}
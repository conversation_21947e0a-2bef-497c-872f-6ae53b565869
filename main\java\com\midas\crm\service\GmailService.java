package com.midas.crm.service;

import com.midas.crm.entity.DTO.gmail.EmailDtos;
import jakarta.mail.MessagingException;
import java.io.IOException;
import java.util.List;

public interface GmailService {



    void enviarEmail(Long crmUserId, EmailDtos.EnviarEmailRequest request) throws MessagingException, IOException;

    List<EmailDtos.EmailMensajeResumen> getBandejaDeEntrada(Long crmUserId, int maxResults) throws IOException;

    EmailDtos.EmailMensajeDetalle getDetalleMensaje(Long crmUserId, String idMensaje) throws IOException;

    byte[] getAdjunto(Long crmUserId, String idMensaje, String idAdjunto) throws IOException;
}
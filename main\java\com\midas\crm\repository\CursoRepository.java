package com.midas.crm.repository;

import com.midas.crm.entity.Curso;
import com.midas.crm.entity.DTO.curso.CursoListDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CursoRepository extends JpaRepository<Curso, Long> {
    boolean existsByNombre(String nombre);

    @Query("SELECT new com.midas.crm.entity.DTO.curso.CursoListDTO(" +
            "c.id, c.nombre, c.descripcion, c.fechaInicio, c.fechaFin, c.estado, c.videoUrl, " +
            "u.nombre, u.apellido, null, " +
            "CAST(COUNT(DISTINCT m.id) AS INTEGER), " +
            "CAST(COUNT(DISTINCT s.id) AS INTEGER), " +
            "CAST(COUNT(DISTINCT l.id) AS INTEGER)) " +
            "FROM Curso c " +
            "LEFT JOIN c.usuario u " +
            "LEFT JOIN Modulo m ON m.curso.id = c.id " +
            "LEFT JOIN Seccion s ON s.modulo.id = m.id " +
            "LEFT JOIN Leccion l ON l.seccion.id = s.id " +
            "WHERE c.id IN :ids " +
            "GROUP BY c.id, c.nombre, c.descripcion, c.fechaInicio, c.fechaFin, c.estado, c.videoUrl, " +
            "u.nombre, u.apellido " +
            "ORDER BY c.id")
    List<CursoListDTO> findOptimizedByIds(@Param("ids") List<Long> ids);
}

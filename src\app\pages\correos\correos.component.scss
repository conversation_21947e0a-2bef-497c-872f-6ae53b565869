// Estilos específicos para el componente de correos
.correos-container {
  min-height: calc(100vh - 64px); // Ajustar según la altura del header
}

// Animaciones para los estados de carga
.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// Estilos para los botones
.btn-primary {
  @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

.btn-secondary {
  @apply bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

// Estilos para las tarjetas
.card {
  @apply bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700;
}

// Responsive design
@media (max-width: 768px) {
  .correos-container {
    padding: 1rem;
  }
}

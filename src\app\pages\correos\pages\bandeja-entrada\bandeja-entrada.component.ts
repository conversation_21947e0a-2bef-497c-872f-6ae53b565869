import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { GmailService } from '../../services/gmail.service';
import { EmailMensajeResumen } from '../../models/gmail.models';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-bandeja-entrada',
  templateUrl: './bandeja-entrada.component.html',
  styleUrls: ['./bandeja-entrada.component.scss']
})
export class BandejaEntradaComponent implements OnInit {
  mensajes: EmailMensajeResumen[] = [];
  cargando = true;
  error: string | null = null;
  maxResults = 20;

  constructor(
    private gmailService: GmailService,
    public router: Router
  ) {}

  ngOnInit(): void {
    this.cargarBandejaEntrada();
  }

  private cargarBandejaEntrada(): void {
    this.cargando = true;
    this.error = null;

    this.gmailService.obtenerBandejaEntrada(this.maxResults).subscribe({
      next: (response) => {
        if (response.rpta === 1) {
          this.mensajes = response.data || [];
        } else {
          this.error = response.msg || 'Error al cargar la bandeja de entrada';
        }
        this.cargando = false;
      },
      error: (error) => {
        console.error('Error al cargar bandeja de entrada:', error);
        this.error = 'Error de conexión. Intente nuevamente.';
        this.cargando = false;
      }
    });
  }

  verDetalleMensaje(mensaje: EmailMensajeResumen): void {
    this.router.navigate(['/correos/mensaje', mensaje.id]);
  }

  recargar(): void {
    this.cargarBandejaEntrada();
  }

  cambiarCantidadMensajes(cantidad: number): void {
    this.maxResults = cantidad;
    this.cargarBandejaEntrada();
  }

  onCambiarCantidad(event: Event): void {
    const target = event.target as HTMLSelectElement;
    if (target && target.value) {
      this.cambiarCantidadMensajes(+target.value);
    }
  }

  // Función para formatear la fecha de manera más legible
  formatearFecha(fecha: string): string {
    try {
      const fechaObj = new Date(fecha);
      const ahora = new Date();
      const diferencia = ahora.getTime() - fechaObj.getTime();
      const dias = Math.floor(diferencia / (1000 * 60 * 60 * 24));

      if (dias === 0) {
        return fechaObj.toLocaleTimeString('es-ES', { 
          hour: '2-digit', 
          minute: '2-digit' 
        });
      } else if (dias === 1) {
        return 'Ayer';
      } else if (dias < 7) {
        return `${dias} días`;
      } else {
        return fechaObj.toLocaleDateString('es-ES', {
          day: '2-digit',
          month: '2-digit',
          year: '2-digit'
        });
      }
    } catch (error) {
      return fecha;
    }
  }

  // Función para extraer solo el nombre del remitente (sin email)
  extraerNombreRemitente(de: string): string {
    const match = de.match(/^(.+?)\s*<.*>$/);
    return match ? match[1].trim() : de;
  }

  // Función para extraer solo el email del remitente
  extraerEmailRemitente(de: string): string {
    const match = de.match(/<(.+)>/);
    return match ? match[1] : de;
  }

  // Función para truncar el asunto si es muy largo
  truncarAsunto(asunto: string, maxLength: number = 50): string {
    return asunto.length > maxLength ? asunto.substring(0, maxLength) + '...' : asunto;
  }

  // Función para truncar el snippet
  truncarSnippet(snippet: string, maxLength: number = 100): string {
    return snippet.length > maxLength ? snippet.substring(0, maxLength) + '...' : snippet;
  }

  // TrackBy function para mejorar el rendimiento
  trackByMessageId(index: number, mensaje: EmailMensajeResumen): string {
    return mensaje.id;
  }
}

package com.midas.crm.service.serviceImpl;

import com.midas.crm.entity.AudioSinLead;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import com.midas.crm.config.RabbitMQConfig;
import com.midas.crm.entity.ClienteResidencial;
import com.midas.crm.entity.DTO.queue.TranscriptionQueueMessage;
import com.midas.crm.repository.ClienteResidencialRepository;
import com.midas.crm.service.AudioConversionService;
import com.midas.crm.service.AudioSinLeadService;
import com.midas.crm.service.ClienteResidencialService;
import com.midas.crm.service.TranscriptionAnalysisService;
import com.midas.crm.service.TranscriptionQueueService;
import com.midas.crm.service.TranscriptionDatabaseServiceSimple;
import com.midas.crm.utils.GoogleDriveOrganizationHelper;
import com.midas.crm.utils.GoogleDriveOrganizationHelper.FolderType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.domain.Sort;
import org.springframework.http.*;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.client.ResourceAccessException;
import java.io.*;
import com.rabbitmq.client.Channel;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.TextStyle;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Stream;

/**
 * Implementación del servicio de colas de transcripción
 */
@Service
@Slf4j
public class TranscriptionQueueServiceImpl implements TranscriptionQueueService {
    private final ClienteResidencialRepository clienteResidencialRepository;
    private final RabbitTemplate rabbitTemplate;
    private final AudioConversionService audioConversionService;
    private final GoogleDriveServiceImpl googleDriveService;
    private final RestTemplate transcriptionRestTemplate;
    private final RestTemplate quickRestTemplate;
    private final ClienteResidencialService clienteResidencialService;
    private final TranscriptionAnalysisService transcriptionAnalysisService;
    private final AudioSinLeadService audioSinLeadService;
    private final RestTemplate restTemplate;
    private final TranscriptionDatabaseServiceSimple transcriptionDatabaseService;
    private final GoogleDriveOrganizationHelper driveOrganizationHelper;

    public TranscriptionQueueServiceImpl(
            ClienteResidencialRepository clienteResidencialRepository,
            RabbitTemplate rabbitTemplate,
            AudioConversionService audioConversionService,
            GoogleDriveServiceImpl googleDriveService,
            @Qualifier("transcriptionRestTemplate") RestTemplate transcriptionRestTemplate,
            @Qualifier("quickRestTemplate")        RestTemplate quickRestTemplate,
            @Qualifier("standardRestTemplate")     RestTemplate restTemplate,
            ClienteResidencialService clienteResidencialService,
            TranscriptionAnalysisService transcriptionAnalysisService,
            AudioSinLeadService audioSinLeadService,
            TranscriptionDatabaseServiceSimple transcriptionDatabaseService,
            GoogleDriveOrganizationHelper driveOrganizationHelper) {

        this.clienteResidencialRepository = clienteResidencialRepository;
        this.rabbitTemplate              = rabbitTemplate;
        this.audioConversionService      = audioConversionService;
        this.googleDriveService          = googleDriveService;
        this.transcriptionRestTemplate   = transcriptionRestTemplate;
        this.quickRestTemplate           = quickRestTemplate;
        this.restTemplate                = restTemplate;
        this.clienteResidencialService   = clienteResidencialService;
        this.transcriptionAnalysisService= transcriptionAnalysisService;
        this.audioSinLeadService         = audioSinLeadService;
        this.transcriptionDatabaseService= transcriptionDatabaseService;
        this.driveOrganizationHelper = driveOrganizationHelper;
    }

    // Control de estado del procesamiento
    private final AtomicBoolean processingActive = new AtomicBoolean(true);

    // URLs de las APIs externas
    @Value("${transcription.api.url:https://gni8cguopmely9-8000.proxy.runpod.net/api}")
    private String transcriptionApiUrl;

    @Value("${comparison.api.url:https://apisozarusac.com/ventas/api}")
    private String comparisonApiUrl;

    @Value("${transcription.timeout.seconds:1000}")
    private int transcriptionTimeoutSeconds;

    @Value("${transcription.poll.initial-wait-seconds:90}")
    private int initialWaitSeconds;

    @Value("${transcription.poll.interval-seconds:30}")
    private int pollIntervalSeconds;

    @Value("${transcription.poll.max-attempts:12}")
    private int pollMaxAttempts;

    // 📏 Configuración de timeouts por tamaño de archivo
    @Value("${transcription.small-file.max-size-mb:10}")
    private int smallFileMaxSizeMb;

    @Value("${transcription.small-file.initial-wait-seconds:120}")
    private int smallFileInitialWaitSeconds;

    @Value("${transcription.small-file.max-attempts:12}")
    private int smallFileMaxAttempts;

    @Value("${transcription.medium-file.max-size-mb:50}")
    private int mediumFileMaxSizeMb;

    @Value("${transcription.medium-file.initial-wait-seconds:180}")
    private int mediumFileInitialWaitSeconds;

    @Value("${transcription.medium-file.max-attempts:16}")
    private int mediumFileMaxAttempts;

    @Value("${transcription.large-file.initial-wait-seconds:300}")
    private int largeFileInitialWaitSeconds;

    @Value("${transcription.large-file.max-attempts:24}")
    private int largeFileMaxAttempts;

    /**
     * 📊 Clase interna para configuración de timeouts
     */
    private static class TimeoutConfig {
        final int initialWaitSeconds;
        final int intervalSeconds;
        final int maxAttempts;
        final String category;

        TimeoutConfig(int initialWait, int interval, int maxAttempts, String category) {
            this.initialWaitSeconds = initialWait;
            this.intervalSeconds = interval;
            this.maxAttempts = maxAttempts;
            this.category = category;
        }
    }

    /**
     * 🧠 Calcula timeouts inteligentes basados en el tamaño del archivo
     * @param fileSizeBytes Tamaño del archivo en bytes
     * @return Configuración de timeouts personalizada
     */
    private TimeoutConfig calculateIntelligentTimeouts(long fileSizeBytes) {
        double fileSizeMb = fileSizeBytes / (1024.0 * 1024.0);

        log.info("📏 Archivo de {:.2f} MB - Calculando timeouts inteligentes...", fileSizeMb);

        if (fileSizeMb <= smallFileMaxSizeMb) {
            log.info("🟢 Archivo PEQUEÑO - Timeouts reducidos");
            return new TimeoutConfig(
                    smallFileInitialWaitSeconds,
                    pollIntervalSeconds,
                    smallFileMaxAttempts,
                    "small"
            );
        } else if (fileSizeMb <= mediumFileMaxSizeMb) {
            log.info("🟡 Archivo MEDIANO - Timeouts estándar");
            return new TimeoutConfig(
                    mediumFileInitialWaitSeconds,
                    pollIntervalSeconds,
                    mediumFileMaxAttempts,
                    "medium"
            );
        } else {
            log.info("🔴 Archivo GRANDE - Timeouts extendidos");
            return new TimeoutConfig(
                    largeFileInitialWaitSeconds,
                    pollIntervalSeconds,
                    largeFileMaxAttempts,
                    "large"
            );
        }
    }

    /**
     * 📏 Obtiene el tamaño del archivo desde la URL
     */
    private long getFileSizeFromUrl(String fileUrl) {
        try {
            HttpHeaders headers = transcriptionRestTemplate.headForHeaders(fileUrl);
            String contentLength = headers.getFirst("Content-Length");

            if (contentLength != null) {
                long size = Long.parseLong(contentLength);
                log.debug("📏 Tamaño obtenido via HEAD: {} bytes", size);
                return size;
            }

            log.warn("⚠️ No se pudo obtener Content-Length, usando estimación");
            return 25 * 1024 * 1024; // Asumir 25 MB por defecto

        } catch (Exception e) {
            log.warn("⚠️ Error obteniendo tamaño de archivo: {}", e.getMessage());
            return 25 * 1024 * 1024; // Asumir 25 MB por defecto
        }
    }

    @Override
    public Map<String, Object> processPendingLeads(int batchSize, String numeroAgente, boolean allDates, LocalDate selectedDate) {
        log.info("▶ processPendingLeads → batchSize={}, agente='{}', allDates={}, selectedDate={}",
                batchSize, numeroAgente, allDates, selectedDate);

        Map<String, Object> result = new HashMap<>();
        int sent = 0, errors = 0;
        List<String> skipped = new ArrayList<>();
        int pageIndex = 0;
        int pageSize = 10;

        try {
            if (selectedDate != null && (numeroAgente == null || numeroAgente.trim().isEmpty())) {
                log.info("🎯 Procesando TODOS los audios de la fecha: {}", selectedDate);
                return processAllAudiosFromDate(selectedDate, batchSize, result, skipped);
            }

            while (sent < batchSize) {
                Pageable pageRequest = PageRequest.of(pageIndex, pageSize, Sort.by("fechaCreacion").ascending());
                List<ClienteResidencial> pageLeads = fetchPage(pageRequest, numeroAgente, allDates, selectedDate);
                if (pageLeads.isEmpty()) break;

                for (ClienteResidencial lead : pageLeads) {
                    if (sent >= batchSize) break;

                    String movil = lead.getMovilContacto();
                    String agenteNorm = normalizeAgentNumber(lead.getNumeroAgente());

                    if (!StringUtils.hasText(movil) || !StringUtils.hasText(agenteNorm)) {
                        skipped.add("Lead " + lead.getId() + " sin móvil o agente");
                        continue;
                    }

                    String audioUrl = null;
                    try {
                        String fechaCreacionStr = selectedDate != null
                                ? selectedDate.toString()
                                : (lead.getFechaCreacion() != null ? lead.getFechaCreacion().toLocalDate().toString() : null);
                        audioUrl = googleDriveService.findAudioByMovilAgentAndDate(movil, agenteNorm, fechaCreacionStr, null);
                    } catch (IOException e) {
                        log.warn("❌ Error al buscar audio Movil={} Agente={} para lead {}: {}", movil, agenteNorm, lead.getId(), e.getMessage());
                    }

                    if (audioUrl == null) {
                        skipped.add("Lead " + lead.getId() + " sin audio");
                        continue;
                    }

                    lead.setNombreArchivoMp3(extractMp3FileName(audioUrl));
                    boolean ok = sendLeadToTranscriptionQueue(lead);
                    if (ok) sent++;
                    else errors++;
                }

                pageIndex++;
            }

            result.put("sentToQueue", sent);
            result.put("skippedNoAudio", skipped);
            result.put("errorsEnqueuing", errors);
            result.put("timestamp", LocalDateTime.now());

            log.info("✔ processPendingLeads completado. Encolados={}, Saltados={}, Errores={}", sent, skipped.size(), errors);
        } catch (Exception e) {
            log.error("❌ Error general en processPendingLeads", e);
            result.put("error", e.getMessage());
        }

        return result;
    }

    @Override
    public Map<String, Object> processTranscriptionOnly(int batchSize, String numeroAgente, boolean allDates, LocalDate selectedDate) {
        log.info("▶ processTranscriptionOnly → batchSize={}, agente='{}', allDates={}, selectedDate={}", batchSize, numeroAgente, allDates, selectedDate);

        Map<String, Object> result = new HashMap<>();
        int sent = 0, errors = 0;
        List<String> skipped = new ArrayList<>();
        int pageIndex = 0;
        int pageSize = 10;

        try {
            if (selectedDate != null && (numeroAgente == null || numeroAgente.trim().isEmpty())) {
                log.info("🎯 Procesando TODOS los audios de la fecha para SOLO transcripción: {}", selectedDate);
                return processAllAudiosFromDateTranscriptionOnly(selectedDate, batchSize, result, skipped);
            }

            while (sent < batchSize) {
                Pageable pageRequest = PageRequest.of(pageIndex, pageSize, Sort.by("fechaCreacion").ascending());
                List<ClienteResidencial> pageLeads = fetchPage(pageRequest, numeroAgente, allDates, selectedDate);
                if (pageLeads.isEmpty()) break;

                for (ClienteResidencial lead : pageLeads) {
                    if (sent >= batchSize) break;

                    String movil = lead.getMovilContacto();
                    String agenteNorm = normalizeAgentNumber(lead.getNumeroAgente());

                    if (!StringUtils.hasText(movil) || !StringUtils.hasText(agenteNorm)) {
                        skipped.add("Lead " + lead.getId() + " sin móvil o agente");
                        continue;
                    }

                    String audioUrl = null;
                    try {
                        String fechaCreacionStr = selectedDate != null
                                ? selectedDate.toString()
                                : (lead.getFechaCreacion() != null ? lead.getFechaCreacion().toLocalDate().toString() : null);
                        audioUrl = googleDriveService.findAudioByMovilAgentAndDate(movil, agenteNorm, fechaCreacionStr, null);
                    } catch (IOException e) {
                        log.warn("❌ Error al buscar audio Movil={} Agente={} para lead {}: {}", movil, agenteNorm, lead.getId(), e.getMessage());
                    }

                    if (audioUrl == null) {
                        skipped.add("Lead " + lead.getId() + " sin audio");
                        continue;
                    }

                    lead.setNombreArchivoMp3(extractMp3FileName(audioUrl));
                    boolean ok = sendLeadToTranscriptionQueueOnly(lead);
                    if (ok) sent++;
                    else errors++;
                }

                pageIndex++;
            }

            result.put("sentToQueue", sent);
            result.put("skippedNoAudio", skipped);
            result.put("errorsEnqueuing", errors);
            result.put("timestamp", LocalDateTime.now());
            result.put("processType", "TRANSCRIPTION_ONLY");

            log.info("✔ processTranscriptionOnly completado. Encolados={}, Saltados={}, Errores={}", sent, skipped.size(), errors);
        } catch (Exception e) {
            log.error("❌ Error general en processTranscriptionOnly", e);
            result.put("error", e.getMessage());
        }

        return result;
    }

    @Override
    public Map<String, Object> processComparisonOnly(int batchSize, String numeroAgente, boolean allDates, LocalDate selectedDate) {
        log.info("▶ processComparisonOnly → batchSize={}, agente='{}', allDates={}, selectedDate={}", batchSize, numeroAgente, allDates, selectedDate);

        Map<String, Object> result = new HashMap<>();
        int sent = 0, errors = 0;
        List<String> skipped = new ArrayList<>();
        int pageIndex = 0;
        int pageSize = 10;

        try {
            while (sent < batchSize) {
                Pageable pageRequest = PageRequest.of(pageIndex, pageSize, Sort.by("fechaCreacion").ascending());
                List<ClienteResidencial> pageLeads = fetchPageWithTranscription(pageRequest, numeroAgente, allDates, selectedDate);
                if (pageLeads.isEmpty()) break;

                for (ClienteResidencial lead : pageLeads) {
                    if (sent >= batchSize) break;

                    if (!hasTranscription(lead)) {
                        skipped.add("Lead " + lead.getId() + " sin transcripción");
                        continue;
                    }

                    boolean ok = sendLeadToComparisonQueueOnly(lead);
                    if (ok) sent++;
                    else errors++;
                }

                pageIndex++;
            }

            result.put("sentToQueue", sent);
            result.put("skippedNoTranscription", skipped);
            result.put("errorsEnqueuing", errors);
            result.put("timestamp", LocalDateTime.now());
            result.put("processType", "COMPARISON_ONLY");

            log.info("✔ processComparisonOnly completado. Encolados={}, Saltados={}, Errores={}", sent, skipped.size(), errors);
        } catch (Exception e) {
            log.error("❌ Error general en processComparisonOnly", e);
            result.put("error", e.getMessage());
        }

        return result;
    }

    /**
     * 🆕 NUEVO MÉTODO: Procesa archivos de audio de una fecha específica UNO POR UNO
     * Respeta el batchSize y NO crea leads automáticamente
     * Registra audios sin leads en tabla separada para análisis futuro
     */
    private Map<String, Object> processAllAudiosFromDate(LocalDate selectedDate, int batchSize, Map<String, Object> result, List<String> skipped) {
        int sent = 0, errors = 0;
        List<String> processed = new ArrayList<>();
        List<String> audioErrors = new ArrayList<>();
        List<String> audiosSinLead = new ArrayList<>();

        try {
            log.info("🎯 Obteniendo archivos de audio para fecha: {} (procesando máximo {})", selectedDate, batchSize);

            List<Map<String, Object>> allAudioFiles = googleDriveService.getAllAudioFilesByDate(selectedDate.toString());
            if (allAudioFiles.isEmpty()) {
                log.warn("📭 No se encontraron archivos de audio para la fecha: {}", selectedDate);
                result.put("sentToQueue", 0);
                result.put("skippedNoAudio", List.of("No hay archivos de audio para la fecha " + selectedDate));
                result.put("errorsEnqueuing", 0);
                result.put("timestamp", LocalDateTime.now());
                return result;
            }

            List<Map<String, Object>> audioFiles = filtrarArchivosNoProcesados(allAudioFiles, selectedDate);
            log.info("📁 Total archivos encontrados: {} | Sin procesar: {} | Procesando máximo: {}", allAudioFiles.size(), audioFiles.size(), batchSize);

            int procesados = 0;
            for (Map<String, Object> audioFile : audioFiles) {
                if (sent >= batchSize) {
                    log.info("🛑 Límite de batch alcanzado: {} de {} archivos procesados", sent, audioFiles.size());
                    break;
                }

                procesados++;
                try {
                    String fileName = (String) audioFile.get("name");
                    String audioUrl = (String) audioFile.get("webContentLink");
                    String extractedMovil = (String) audioFile.get("extractedMovil");
                    String extractedAgente = (String) audioFile.get("extractedAgente");

                    if (extractedMovil == null || extractedAgente == null) {
                        skipped.add("Archivo " + fileName + " - No se pudo extraer móvil o agente");
                        continue;
                    }

                    ClienteResidencial lead = findExistingLeadForAudio(extractedMovil, extractedAgente, selectedDate);
                    if (lead == null) {
                        registrarAudioSinLead(audioFile, selectedDate, "No se encontró lead existente");
                        audiosSinLead.add("Archivo " + fileName + " - Sin lead correspondiente (registrado para análisis)");
                        continue;
                    }

                    if (isAlreadyTranscribed(lead)) {
                        skipped.add("Lead " + lead.getId() + " ya tiene transcripción");
                        continue;
                    }

                    lead.setNombreArchivoMp3(extractMp3FileName(audioUrl));
                    boolean ok = sendLeadToTranscriptionQueue(lead);

                    if (ok) {
                        sent++;
                        processed.add("Lead " + lead.getId() + " (" + fileName + ")");
                        log.info("✅ Lead {} enviado a cola: {}", lead.getId(), fileName);
                    } else {
                        errors++;
                        audioErrors.add("Error enviando lead " + lead.getId() + " para archivo " + fileName);
                    }

                } catch (Exception e) {
                    errors++;
                    String fileName = (String) audioFile.get("name");
                    String error = "Error procesando archivo " + fileName + ": " + e.getMessage();
                    audioErrors.add(error);
                    log.error("❌ {}", error, e);
                }
            }

            result.put("sentToQueue", sent);
            result.put("processedAudios", processed);
            result.put("skippedNoAudio", skipped);
            result.put("audiosSinLead", audiosSinLead);
            result.put("errorsEnqueuing", errors);
            result.put("audioErrors", audioErrors);
            result.put("totalAudioFiles", allAudioFiles.size());
            result.put("audiosSinProcesar", audioFiles.size());
            result.put("procesadosEnEsteJob", procesados);
            result.put("timestamp", LocalDateTime.now());

            log.info("🏁 Procesamiento completado - Enviados: {}, Omitidos: {}, Sin Lead: {}, Errores: {}, Total: {}, Sin procesar: {}",
                    sent, skipped.size(), audiosSinLead.size(), errors, allAudioFiles.size(), audioFiles.size());

        } catch (Exception e) {
            log.error("❌ Error general al procesar audios de fecha {}: {}", selectedDate, e.getMessage(), e);
            result.put("error", "Error general: " + e.getMessage());
        }

        return result;
    }

    /**
     * 🔍 BUSCA LEAD EXISTENTE (NO CREA) - Búsqueda inteligente y flexible
     * Usa múltiples estrategias para encontrar leads que coincidan con archivos de audio
     */
    private ClienteResidencial findExistingLeadForAudio(String movil, String agente, LocalDate fecha) {
        try {
            log.debug("🔍 Buscando lead para móvil: {}, agente: {}, fecha: {}", movil, agente, fecha);

            String agenteNorm = normalizeAgentNumber(agente);
            log.debug("🔧 Agente normalizado: {} -> {}", agente, agenteNorm);

            LocalDateTime startOfDay = fecha.atStartOfDay();
            LocalDateTime endOfDay = fecha.atTime(LocalTime.MAX);

            List<ClienteResidencial> flexibleResults =
                    clienteResidencialRepository.findLeadsByAudioPattern(movil, agenteNorm, startOfDay, endOfDay);

            if (!flexibleResults.isEmpty()) {
                ClienteResidencial found = flexibleResults.get(0);
                log.info("✅ Lead encontrado con búsqueda flexible - ID: {}, móvil: {}, agente: {} -> {}",
                        found.getId(), found.getMovilContacto(), found.getNumeroAgente(), agenteNorm);
                return found;
            }

            List<ClienteResidencial> exactResults =
                    clienteResidencialRepository.findByMovilContactoAndNumeroAgenteAndFechaCreacionBetween(
                            movil, agenteNorm, startOfDay, endOfDay);

            if (!exactResults.isEmpty()) {
                ClienteResidencial found = exactResults.get(0);
                log.info("✅ Lead encontrado con búsqueda exacta - ID: {}, móvil: {}, agente: {}",
                        found.getId(), found.getMovilContacto(), found.getNumeroAgente());
                return found;
            }

            List<ClienteResidencial> allByMovil = clienteResidencialRepository.findByMovilContacto(movil);
            if (!allByMovil.isEmpty()) {
                log.debug("🔍 Encontrados {} leads con móvil {} (cualquier fecha/agente):", allByMovil.size(), movil);
                for (ClienteResidencial lead : allByMovil.subList(0, Math.min(3, allByMovil.size()))) {
                    log.debug(" 📋 Lead ID: {}, agente: {}, fecha: {}",
                            lead.getId(), lead.getNumeroAgente(), lead.getFechaCreacion());
                }
            }

            log.warn("❌ No se encontró lead para móvil: {}, agente: {} (norm: {}), fecha: {}",
                    movil, agente, agenteNorm, fecha);
            return null;

        } catch (Exception e) {
            log.error("❌ Error al buscar lead para móvil {} y agente {}: {}", movil, agente, e.getMessage(), e);
            return null;
        }
    }

    private static final Pattern FILE_INFO_PATTERN = Pattern.compile(
            "^(?<mobile>\\d{6,15}).*?(?<agent>(?:agen|agent)?\\d{1,3})", Pattern.CASE_INSENSITIVE);

    private class AudioMeta {
        final Map<String, Object> raw;
        final String fileName;
        final String mobile;
        final String agent;

        AudioMeta(Map<String, Object> raw) {
            this.raw = raw;
            this.fileName = (String) raw.getOrDefault("name", "");
            String[] extracted = extractMovilAndAgentFromFileName(this.fileName);
            this.mobile = extracted != null ? extracted[0] : null;
            this.agent  = extracted != null ? extracted[1] : null;
            raw.put("extractedMovil",  mobile);
            raw.put("extractedAgente", agent);
        }

        boolean hasLeadData() { return mobile != null && agent != null; }
    }

    /**
     * 🔄 Método de filtrado optimizado para archivos no procesados
     */
    private List<Map<String, Object>> filtrarArchivosNoProcesados(List<Map<String, Object>> allAudioFiles,
                                                                  LocalDate fecha) {
        log.info("🔍 Filtrado OPTIMIZADO ➜ {} audios / fecha {}", allAudioFiles.size(), fecha);

        if (allAudioFiles.isEmpty()) return List.of();

        long startTime = System.currentTimeMillis();

        Set<String> nombresRegistrados = audioSinLeadService.findByFechaProcesamientoBetween(
                        fecha.atStartOfDay(), fecha.atTime(LocalTime.MAX))
                .stream()
                .map(AudioSinLead::getNombreArchivo)
                .collect(Collectors.toSet());

        Map<String, ClienteResidencial> leadsIndex = createLeadsIndexOptimizado(fecha);
        log.info("📑 Índice OPTIMIZADO construido: {} elementos en {}ms",
                leadsIndex.size(), System.currentTimeMillis() - startTime);

        boolean useParallel = allAudioFiles.size() > 200;
        Stream<Map<String, Object>> stream = allAudioFiles.stream();
        if (useParallel) {
            stream = stream.parallel();
            log.info("⚡ Procesamiento PARALELO activado para {} archivos", allAudioFiles.size());
        }

        List<Map<String, Object>> audiosSinProcesar = stream
                .map(this::enrichAudioFileWithMetadata)
                .filter(audioFile -> shouldProcessAudioFile(audioFile, nombresRegistrados, leadsIndex))
                .collect(Collectors.toList());

        long totalTime = System.currentTimeMillis() - startTime;
        log.info("✅ Filtrado OPTIMIZADO completado en {}ms ➜ {} audios a procesar (de {} originales)",
                totalTime, audiosSinProcesar.size(), allAudioFiles.size());

        return audiosSinProcesar;
    }

    /**
     * 🗂️ Índice de leads optimizado para búsqueda rápida
     */
    private Map<String, ClienteResidencial> createLeadsIndexOptimizado(LocalDate fecha) {
        Map<String, ClienteResidencial> index = new HashMap<>();

        try {
            LocalDateTime startOfDay = fecha.atStartOfDay();
            LocalDateTime endOfDay = fecha.atTime(LocalTime.MAX);

            List<Object[]> leadsData = clienteResidencialRepository.findAllLeadsForAudioVerification(startOfDay, endOfDay);
            log.info("📋 Procesando {} leads para índice OPTIMIZADO", leadsData.size());

            int batchSize = 1000;
            int processedCount = 0;

            for (int i = 0; i < leadsData.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, leadsData.size());
                List<Object[]> batch = leadsData.subList(i, endIndex);

                for (Object[] data : batch) {
                    try {
                        Long id = ((Number) data[0]).longValue();
                        String movil = (String) data[1];
                        String agente = (String) data[2];
                        String agenteNorm = (String) data[7]; // agente_normalizado

                        ClienteResidencial lead = new ClienteResidencial();
                        lead.setId(id);
                        lead.setMovilContacto(movil);
                        lead.setNumeroAgente(agente);
                        lead.setTextoTranscription((String) data[4]);
                        lead.setUrlDriveTranscripcion((String) data[5]);

                        if (movil != null && agenteNorm != null) {
                            String keyPrincipal = movil + "_" + agenteNorm;
                            index.put(keyPrincipal, lead);

                            if (!agente.equals(agenteNorm)) {
                                index.put(movil + "_" + agente, lead);
                            }

                            if (agenteNorm.matches("\\d+")) {
                                int agenteNum = Integer.parseInt(agenteNorm);
                                index.put(movil + "_agen" + String.format("%03d", agenteNum), lead);
                                index.put(movil + "_agent" + String.format("%03d", agenteNum), lead);
                            }

                            processedCount++;
                        }
                    } catch (Exception e) {
                        log.warn("⚠️ Error procesando lead para índice: {}", e.getMessage());
                    }
                }

                if (i % 5000 == 0 && i > 0) {
                    log.debug("📊 Índice: procesados {} de {} leads", i, leadsData.size());
                }
            }

            log.info("✅ Índice OPTIMIZADO creado: {} leads procesados → {} claves generadas",
                    processedCount, index.size());

        } catch (Exception e) {
            log.error("❌ Error creando índice optimizado: {}", e.getMessage(), e);
        }

        return index;
    }

    /**
     * 🧠 Timeouts inteligentes mejorados - Más precisos por tamaño y tipo
     */
    private TimeoutConfig calculateIntelligentTimeoutsImproved(long fileSizeBytes) {
        double fileSizeMb = fileSizeBytes / (1024.0 * 1024.0);

        log.info("📏 Archivo de {:.2f} MB - Calculando timeouts inteligentes mejorados...", fileSizeMb);

        if (fileSizeMb <= 5) {
            log.info("🟢 Archivo MUY PEQUEÑO - Timeouts mínimos (transcripción rápida)");
            return new TimeoutConfig(
                    30,   // Solo 30 segundos de espera inicial
                    20,   // Intervalo de 20s
                    8,    // Máximo 8 intentos
                    "muy_pequeño"
            );
        } else if (fileSizeMb <= smallFileMaxSizeMb) {
            log.info("🟡 Archivo PEQUEÑO - Timeouts reducidos");
            return new TimeoutConfig(
                    60,   // 1 minuto inicial
                    25,   // Intervalo de 25s
                    10,   // Máximo 10 intentos
                    "pequeño"
            );
        } else if (fileSizeMb <= mediumFileMaxSizeMb) {
            log.info("🟠 Archivo MEDIANO - Timeouts estándar");
            return new TimeoutConfig(
                    90,   // 1.5 minutos inicial
                    30,   // Intervalo de 30s
                    12,   // Máximo 12 intentos
                    "mediano"
            );
        } else {
            log.info("🔴 Archivo GRANDE - Timeouts extendidos");
            return new TimeoutConfig(
                    180,  // 3 minutos inicial
                    45,   // Intervalo de 45s
                    16,   // Máximo 16 intentos
                    "grande"
            );
        }
    }

    /**
     * 🚀 Llamada a API de transcripción optimizada con verificación temprana
     */
    private Map<String, Object> callTranscriptionAPIOptimized(TranscriptionQueueMessage message) {
        try {
            log.info("🚀 TRANSCRIPCIÓN OPTIMIZADA - Lead ID: {}", message.getLeadId());

            String callId = generateEnhancedCallId(message);
            message.setCallId(callId);

            long fileSize = getFileSizeFromUrl(message.getArchivoMp3Url());
            TimeoutConfig timeouts = calculateIntelligentTimeoutsImproved(fileSize);

            log.info("⚙️ Config optimizada: {} - Inicial: {}s, Intervalo: {}s, Máx: {}",
                    timeouts.category, timeouts.initialWaitSeconds, timeouts.intervalSeconds, timeouts.maxAttempts);

            sendAudioForTranscriptionAsyncSafe(message, fileSize);
            log.info("✅ Audio enviado - iniciando polling optimizado");

            if (timeouts.category.equals("muy_pequeño")) {
                log.info("🚀 MODO RÁPIDO - Verificación temprana a los 15s");
                Thread.sleep(15000);

                Optional<Map<String, Object>> earlyResult = transcriptionDatabaseService.findForTranscriptionFlow(
                        callId, message.getAgentId(), message.getNumeroMovil(), message.getNombreArchivoMp3());

                if (earlyResult.isPresent()) {
                    log.info("✅ ¡TRANSCRIPCIÓN RÁPIDA! Encontrada en solo 15s");
                    return earlyResult.get();
                }
            }

            int remainingWait = timeouts.initialWaitSeconds - (timeouts.category.equals("muy_pequeño") ? 15 : 0);
            if (remainingWait > 0) {
                log.info("⏳ Esperando {} segundos adicionales...", remainingWait);
                Thread.sleep(remainingWait * 1000L);
            }

            log.info("🎯 Consultando BD directamente...");
            Optional<Map<String, Object>> dbResult = transcriptionDatabaseService.findForTranscriptionFlow(
                    callId, message.getAgentId(), message.getNumeroMovil(), message.getNombreArchivoMp3());

            if (dbResult.isPresent()) {
                log.info("✅ TRANSCRIPCIÓN ENCONTRADA EN BD");
                return dbResult.get();
            }

            log.info("📡 BD vacía - iniciando polling API optimizado...");
            String currentDate = LocalDate.now().toString();
            Map<String, Object> apiResult = pollForTranscriptionResultOptimized(
                    callId, message.getAgentId(), message.getNumeroMovil(), currentDate, timeouts);

            if (apiResult != null) {
                log.info("✅ TRANSCRIPCIÓN ENCONTRADA EN API");
                return apiResult;
            }

            throw new RuntimeException("No se pudo obtener transcripción después de " +
                    timeouts.maxAttempts + " intentos optimizados");

        } catch (Exception e) {
            log.error("❌ Error en transcripción optimizada para lead {}: {}", message.getLeadId(), e.getMessage());
            throw new RuntimeException("Error en transcripción optimizada: " + e.getMessage(), e);
        }
    }

    /**
     * 🔄 Polling optimizado con verificaciones múltiples
     */
    private Map<String, Object> pollForTranscriptionResultOptimized(
            String callId, String agentId, String callerPhone, String currentDate, TimeoutConfig timeouts) {

        String transcriptionsUrl = transcriptionApiUrl.replace("/api", "/api/transcriptions/");

        log.info("🔄 POLLING OPTIMIZADO - {} intentos máx, intervalo {}s",
                timeouts.maxAttempts, timeouts.intervalSeconds);

        for (int attempt = 1; attempt <= timeouts.maxAttempts; attempt++) {
            try {
                log.info("🔍 Intento optimizado {}/{} - Verificación triple", attempt, timeouts.maxAttempts);

                Optional<Map<String, Object>> dbResult = transcriptionDatabaseService.findForTranscriptionFlow(
                        callId, agentId, callerPhone, null);

                if (dbResult.isPresent()) {
                    log.info("✅ ENCONTRADO EN BD - Intento {}", attempt);
                    return dbResult.get();
                }

                Map<String, Object> callIdResult = searchByCallIdOptimized(transcriptionsUrl, callId);
                if (callIdResult != null) {
                    log.info("✅ ENCONTRADO POR CALL_ID - Intento {}", attempt);
                    return callIdResult;
                }

                Map<String, Object> agentResult = searchByAgentDateOptimized(transcriptionsUrl, agentId, currentDate);
                if (agentResult != null) {
                    log.info("✅ ENCONTRADO POR AGENT+FECHA - Intento {}", attempt);
                    return agentResult;
                }

                if (attempt < timeouts.maxAttempts) {
                    log.info("⏳ Esperando {}s antes del próximo intento...", timeouts.intervalSeconds);
                    Thread.sleep(timeouts.intervalSeconds * 1000L);
                }

            } catch (Exception e) {
                log.warn("⚠️ Error en intento optimizado {}: {}", attempt, e.getMessage());

                if (attempt < timeouts.maxAttempts) {
                    try {
                        Thread.sleep((timeouts.intervalSeconds + 10) * 1000L);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }

        log.warn("❌ Polling optimizado terminado sin resultados después de {} intentos", timeouts.maxAttempts);
        return null;
    }

    /**
     * 🔍 Búsqueda optimizada por call_id
     */
    private Map<String, Object> searchByCallIdOptimized(String baseUrl, String callId) {
        try {
            String url = baseUrl + "?call_id=" + callId;
            log.debug("🔍 Búsqueda optimizada por call_id: {}", callId);

            RestTemplate fastRestTemplate = createFastRestTemplate(5000, 10000);
            ResponseEntity<Map> response = fastRestTemplate.getForEntity(url, Map.class);

            return extractTranscriptionFromResponse(response.getBody(), "call_id optimizado");

        } catch (Exception e) {
            log.debug("⚠️ Error en búsqueda por call_id: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 🔍 Búsqueda optimizada por agent + fecha
     */
    private Map<String, Object> searchByAgentDateOptimized(String baseUrl, String agentId, String date) {
        try {
            String url = baseUrl + "?agent_id=" + agentId + "&date=" + date;
            log.debug("🔍 Búsqueda optimizada por agent+fecha: agent={}, fecha={}", agentId, date);

            RestTemplate fastRestTemplate = createFastRestTemplate(5000, 10000);
            ResponseEntity<Map> response = fastRestTemplate.getForEntity(url, Map.class);

            return extractTranscriptionFromResponse(response.getBody(), "agent+fecha optimizado");

        } catch (Exception e) {
            log.debug("⚠️ Error en búsqueda por agent+fecha: {}", e.getMessage());
            return null;
        }
    }

    /**
     * ⚡ Crea RestTemplate rápido para búsquedas
     */
    private RestTemplate createFastRestTemplate(int connectTimeout, int readTimeout) {
        RestTemplate fastTemplate = new RestTemplate();
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(connectTimeout);
        factory.setReadTimeout(readTimeout);
        fastTemplate.setRequestFactory(factory);
        return fastTemplate;
    }

    // Extrae una página de leads según los criterios
    private List<ClienteResidencial> fetchPage(Pageable pageRequest, String numeroAgente, boolean allDates, LocalDate selectedDate) {
        if (selectedDate != null) {
            LocalDateTime start = selectedDate.atStartOfDay();
            LocalDateTime end = selectedDate.atTime(LocalTime.MAX);

            return StringUtils.hasText(numeroAgente)
                    ? clienteResidencialRepository
                    .findPendingLeadsByDateRangeAndAgent(start, end, numeroAgente.trim(), pageRequest)
                    .getContent()
                    : clienteResidencialRepository
                    .findPendingLeadsByDateRange(start, end, pageRequest)
                    .getContent();

        } else if (!allDates) {
            LocalDateTime start = LocalDate.now().withDayOfMonth(1).atStartOfDay();
            LocalDateTime end = start.plusMonths(1).minusNanos(1);

            return StringUtils.hasText(numeroAgente)
                    ? clienteResidencialRepository
                    .findPendingLeadsByDateRangeAndAgent(start, end, numeroAgente.trim(), pageRequest)
                    .getContent()
                    : clienteResidencialRepository
                    .findPendingLeadsByDateRange(start, end, pageRequest)
                    .getContent();

        } else {
            return StringUtils.hasText(numeroAgente)
                    ? clienteResidencialRepository
                    .findPendingLeadsByNumeroAgente(numeroAgente.trim(), pageRequest)
                    .getContent()
                    : clienteResidencialRepository
                    .findPendingLeads(pageRequest)
                    .getContent();
        }
    }

    @Override
    public Map<String, Object> getQueueStatistics(boolean allDates, LocalDate selectedDate) {
        if (selectedDate != null) {
            log.info("📊 Estadísticas para fecha concreta: {}", selectedDate);
            LocalDateTime start = selectedDate.atStartOfDay();
            LocalDateTime end = selectedDate.atTime(LocalTime.MAX);

            long total = clienteResidencialRepository.countByFechaCreacionBetween(start, end);
            long withTrans = clienteResidencialRepository.countByFechaCreacionBetweenAndTranscripcionIsNotNull(start, end);
            long withoutTrans = total - withTrans;
            long withNotes = clienteResidencialRepository.countByFechaCreacionBetweenAndNotasIsNotNull(start, end);

            Map<String, Object> stats = new HashMap<>();
            stats.put("totalLeads", total);
            stats.put("leadsWithTranscription", withTrans);
            stats.put("leadsWithoutTranscription", withoutTrans);
            stats.put("leadsWithNotes", withNotes);
            stats.put("date", selectedDate.toString());
            return stats;

        } else {
            return getQueueStatistics(allDates);
        }
    }

    @Override
    public Map<String,Object> getQueueStatistics(boolean allDates) {
        return allDates
                ? getQueueStatisticsAll()
                : getQueueStatisticsCurrentMonth();
    }

    private Map<String, Object> getQueueStatisticsCurrentMonth() {
        try {
            log.info("📊 Obteniendo estadísticas de cola de transcripción para el mes actual");

            LocalDateTime startOfMonth = LocalDateTime.now()
                    .withDayOfMonth(1)
                    .withHour(0).withMinute(0).withSecond(0).withNano(0);
            LocalDateTime endOfMonth = startOfMonth.plusMonths(1).minusNanos(1);

            log.info("📅 Rango de fechas - Inicio: {}, Fin: {}", startOfMonth, endOfMonth);

            long totalLeads = clienteResidencialRepository.countByFechaCreacionBetween(startOfMonth, endOfMonth);
            long withTranscription = clienteResidencialRepository.countByFechaCreacionBetweenAndTranscripcionIsNotNull(startOfMonth, endOfMonth);
            long withoutTranscription = clienteResidencialRepository.countByFechaCreacionBetweenAndTranscripcionIsNull(startOfMonth, endOfMonth);
            long withNotes = clienteResidencialRepository.countByFechaCreacionBetweenAndNotasIsNotNull(startOfMonth, endOfMonth);

            List<Object[]> statsByAgentResults = clienteResidencialRepository.getStatsByAgentForCurrentMonth(startOfMonth, endOfMonth);
            Map<String, Map<String, Object>> statsByAgent = new HashMap<>();

            for (Object[] result : statsByAgentResults) {
                String numeroAgente = result[0] != null ? (String) result[0] : "";
                long total = ((Number) result[1]).longValue();
                long transcribed = ((Number) result[2]).longValue();
                long notes = ((Number) result[3]).longValue();
                long noTranscription = total - transcribed;

                Map<String, Object> agentStats = new HashMap<>();
                agentStats.put("total", total);
                agentStats.put("withTranscription", transcribed);
                agentStats.put("withoutTranscription", noTranscription);
                agentStats.put("withNotes", notes);

                statsByAgent.put(numeroAgente, agentStats);
            }

            Map<String, Object> stats = new HashMap<>();
            stats.put("totalLeads", totalLeads);
            stats.put("leadsWithTranscription", withTranscription);
            stats.put("leadsWithoutTranscription", withoutTranscription);
            stats.put("leadsWithNotes", withNotes);
            stats.put("statsByAgent", statsByAgent);
            stats.put("monthYear", LocalDateTime.now().format(DateTimeFormatter.ofPattern("MM/yyyy")));
            stats.put("dateRange", Map.of(
                    "start", startOfMonth.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                    "end", endOfMonth.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
            ));

            log.info("📈 Estadísticas del mes actual - Total: {}, Con transcripción: {}, Sin transcripción: {}, Con notas IA: {}",
                    totalLeads, withTranscription, withoutTranscription, withNotes);

            return stats;

        } catch (Exception e) {
            log.error("❌ Error al obtener estadísticas de cola del mes actual", e);
            throw new RuntimeException("Error al obtener estadísticas del mes actual: " + e.getMessage());
        }
    }

    private Map<String, Object> getQueueStatisticsAll() {
        try {
            log.info("📊 Obteniendo estadísticas de cola de transcripción");

            long totalLeads = clienteResidencialRepository.count();
            long leadsWithTranscription = clienteResidencialRepository.countWithTranscription();
            long leadsWithoutTranscription = totalLeads - leadsWithTranscription;
            long leadsWithNotes = clienteResidencialRepository.countWithNotes();

            List<Object[]> statsByAgentResults = clienteResidencialRepository.getStatsByAgent();
            Map<String, Map<String, Object>> statsByAgent = new HashMap<>();

            for (Object[] result : statsByAgentResults) {
                String numeroAgente = result[0] != null ? (String) result[0] : "";
                long total = ((Number) result[1]).longValue();
                long withTranscription = ((Number) result[2]).longValue();
                long withNotes = ((Number) result[3]).longValue();
                long withoutTranscription = total - withTranscription;

                Map<String, Object> agentStats = new HashMap<>();
                agentStats.put("total", total);
                agentStats.put("withTranscription", withTranscription);
                agentStats.put("withoutTranscription", withoutTranscription);
                agentStats.put("withNotes", withNotes);

                statsByAgent.put(numeroAgente, agentStats);
            }

            Map<String, Object> stats = new HashMap<>();
            stats.put("totalLeads", totalLeads);
            stats.put("leadsWithTranscription", leadsWithTranscription);
            stats.put("leadsWithoutTranscription", leadsWithoutTranscription);
            stats.put("leadsWithNotes", leadsWithNotes);
            stats.put("statsByAgent", statsByAgent);
            stats.put("scope", "all_time");

            return stats;

        } catch (Exception e) {
            log.error("❌ Error al obtener estadísticas de cola", e);
            throw new RuntimeException("Error al obtener estadísticas: " + e.getMessage());
        }
    }

    public List<TranscriptionQueueMessage> getPendingLeads(int limit, String numeroAgente, boolean currentMonthOnly) {
        try {
            String filtroMes = currentMonthOnly ? "del mes actual" : "de todos los meses";
            log.info("🗓️ Obteniendo leads pendientes {} - Límite: {}, Agente: {}", filtroMes, limit, numeroAgente);

            List<ClienteResidencial> pendingLeads = getPendingLeadsFromDatabase(limit, numeroAgente, currentMonthOnly);
            List<TranscriptionQueueMessage> messages = pendingLeads.stream()
                    .map(this::convertToQueueMessage)
                    .collect(Collectors.toList());

            log.info("✅ Se encontraron {} leads pendientes {}", messages.size(), filtroMes);
            return messages;

        } catch (Exception e) {
            log.error("❌ Error al obtener leads pendientes", e);
            return new ArrayList<>();
        }
    }

    @Override
    public Map<String, Object> retryFailedMessages(int maxRetries) {
        Map<String, Object> result = new HashMap<>();
        result.put("message", "Funcionalidad de reintento en desarrollo");
        result.put("maxRetries", maxRetries);
        result.put("timestamp", LocalDateTime.now());
        return result;
    }

    @Override
    public boolean sendLeadToQueue(Long leadId) {
        return clienteResidencialRepository.findById(leadId)
                .map(this::sendLeadToQueueInternal)
                .orElse(false);
    }

    private boolean sendLeadToQueueInternal(ClienteResidencial lead) {
        if (isAlreadyTranscribed(lead)) {
            log.debug("⏩ Lead {} ya procesado, se omite", lead.getId());
            return false;
        }

        TranscriptionQueueMessage msg = convertToQueueMessage(lead);
        rabbitTemplate.convertAndSend(
                RabbitMQConfig.TRANSCRIPTION_EXCHANGE,
                RabbitMQConfig.TRANSCRIPTION_ROUTING_KEY,
                msg
        );
        return true;
    }

    @Override
    public void pauseProcessing() {
        processingActive.set(false);
        log.info("Procesamiento de colas pausado");
    }

    @Override
    public void resumeProcessing() {
        processingActive.set(true);
        log.info("Procesamiento de colas reanudado");
    }

    @Override
    public String normalizeAgentNumber(String numeroAgente) {
        if (numeroAgente == null || numeroAgente.trim().isEmpty()) return null;

        String agente = numeroAgente.toLowerCase(Locale.ROOT).replaceAll("\\s+", "");
        agente = agente.replaceFirst("^(agente|agent|agen|te)([:\\-_])?", "");
        agente = agente.replaceAll("[^0-9]", "");
        agente = agente.replaceFirst("^0+", "");

        return agente.isEmpty() ? null : agente;
    }

    @Override
    public boolean isLeadEligibleForProcessing(Long leadId) {
        try {
            Optional<ClienteResidencial> leadOpt = clienteResidencialRepository.findById(leadId);
            if (leadOpt.isPresent()) {
                ClienteResidencial lead = leadOpt.get();

                if (lead.getMovilContacto() == null || lead.getMovilContacto().trim().isEmpty()) return false;
                if (lead.getTextoTranscription() != null && !lead.getTextoTranscription().trim().isEmpty()) return false;
                if (lead.getNumeroAgente() == null || lead.getNumeroAgente().trim().isEmpty()) return false;

                return true;
            }
            return false;

        } catch (Exception e) {
            log.error("Error al verificar elegibilidad del lead {}", leadId, e);
            return false;
        }
    }

    @Override
    public boolean isProcessingActive() {
        return processingActive.get();
    }

    // ========== MÉTODOS PRIVADOS ==========

    private List<ClienteResidencial> getPendingLeadsFromDatabase(int limit, String numeroAgente, boolean currentMonthOnly) {
        try {
            if (numeroAgente != null && !numeroAgente.trim().isEmpty()) {
                String normalizedAgent = normalizeAgentNumber(numeroAgente);
                if (currentMonthOnly) {
                    log.info("🗓️ Obteniendo leads del mes actual para agente: {} (normalizado: {})", numeroAgente, normalizedAgent);
                    return clienteResidencialRepository.findLeadsWithoutTranscriptionCurrentMonthByAgent(normalizedAgent, limit);
                } else {
                    log.info("📅 Obteniendo leads de todos los meses para agente: {} (normalizado: {})", numeroAgente, normalizedAgent);
                    return clienteResidencialRepository.findLeadsWithoutTranscriptionByAgent(normalizedAgent, limit);
                }
            } else {
                if (currentMonthOnly) {
                    log.info("🗓️ Obteniendo leads del mes actual (todos los agentes)");
                    return clienteResidencialRepository.findLeadsWithoutTranscriptionCurrentMonth(limit);
                } else {
                    log.info("📅 Obteniendo leads de todos los meses (todos los agentes)");
                    return clienteResidencialRepository.findLeadsWithoutTranscription(limit);
                }
            }
        } catch (Exception e) {
            log.error("Error al obtener leads pendientes de la base de datos", e);
            return new ArrayList<>();
        }
    }

    private List<ClienteResidencial> getPendingLeadsFromDatabase(int limit, String numeroAgente) {
        return getPendingLeadsFromDatabase(limit, numeroAgente, true);
    }

    private boolean sendLeadToTranscriptionQueue(ClienteResidencial lead) {
        if (isAlreadyTranscribed(lead)) {
            log.debug("⏩ Lead {} ya tiene transcripción/nota – NO se envía a Rabbit", lead.getId());
            return false;
        }

        rabbitTemplate.convertAndSend(
                RabbitMQConfig.TRANSCRIPTION_EXCHANGE,
                RabbitMQConfig.TRANSCRIPTION_ROUTING_KEY,
                convertToQueueMessage(lead)
        );
        return true;
    }

    private TranscriptionQueueMessage convertToQueueMessage(ClienteResidencial lead) {
        String numeroAgenteNormalizado = normalizeAgentNumber(lead.getNumeroAgente());
        String agentIdConsistente = normalizeAgentNumber(lead.getNumeroAgente());
        LocalDateTime fechaActual = LocalDateTime.now();

        log.info("🔧 CREANDO MENSAJE - Lead ID: {} - Agent original: {} -> normalizado: {}",
                lead.getId(), lead.getNumeroAgente(), agentIdConsistente);

        return TranscriptionQueueMessage.builder()
                .leadId(lead.getId())
                .numeroMovil(lead.getMovilContacto())
                .numeroAgente(lead.getNumeroAgente())
                .numeroAgenteNormalizado(agentIdConsistente)
                .nombreCliente(lead.getNombresApellidos())
                .dniCliente(lead.getNifNie())
                .fechaCreacion(lead.getFechaCreacion())
                .nombreAsesor(lead.getUsuario() != null ? lead.getUsuario().getNombres() : null)
                .archivoMp3Url(null)
                .nombreArchivoMp3(lead.getNombreArchivoMp3())
                .estadoProcesamiento("PENDING")
                .fechaEnvio(LocalDateTime.now())
                .intentos(0)
                .maxIntentos(3)
                .queueName(RabbitMQConfig.TRANSCRIPTION_QUEUE)
                .routingKey(RabbitMQConfig.TRANSCRIPTION_ROUTING_KEY)
                .whisperModel("medium")
                .device("gpu")
                .targetLanguage("es")
                .callType("inbound")
                .callId(generateCallId(lead.getMovilContacto(), fechaActual))
                .agentId(agentIdConsistente)
                .callDatetime(fechaActual.toString())
                .processType("FULL")
                .build();
    }

    private String generateCallId(String numeroMovil, LocalDateTime fechaActual) {
        String callId = "CALL_" + UUID.randomUUID().toString().replace("-","").substring(0,8).toUpperCase();
        log.info("🆔 CALL ID FORMATO API generado: {} (EXACTO COMO SWAGGER)", callId);
        return callId;
    }

    // ========== LISTENERS DE RABBITMQ ==========

    /**
     * Excepción interna para cortar el flujo tras hacer basicAck.
     */
    private static class EarlyAckException extends RuntimeException { }

    @RabbitListener(queues = RabbitMQConfig.TRANSCRIPTION_QUEUE, containerFactory = "rabbitListenerContainerFactory")
    public void processTranscriptionMessage(
            TranscriptionQueueMessage msg,
            Channel channel,
            @Header(AmqpHeaders.DELIVERY_TAG) long tag) throws IOException {

        log.info("▶️ Procesando lead {} con OPTIMIZACIONES", msg.getLeadId());

        if (!isChannelOpen(channel)) {
            log.error("❌ Channel cerrado antes de procesar lead {}", msg.getLeadId());
            return;
        }

        try {
            Optional<ClienteResidencial> leadOpt = clienteResidencialRepository.findById(msg.getLeadId());
            if (leadOpt.isPresent()) {
                ClienteResidencial cr = leadOpt.get();
                if (isAlreadyTranscribed(cr)) {
                    safeAck(channel, tag, "Lead ya transcrito - validación temprana");
                    log.info("⏩ Lead {} ya tiene transcripción – descartado", msg.getLeadId());
                    return;
                }
            }

            String audioUrl = findMp3FileInGoogleDrive(msg);
            if (audioUrl == null) {
                if (leadOpt.isPresent()) {
                    ClienteResidencial cr = leadOpt.get();
                    cr.setObservacion("AUDIO_NOT_FOUND");
                    clienteResidencialRepository.save(cr);
                }
                safeAck(channel, tag, "Audio no encontrado para lead " + msg.getLeadId());
                return;
            }
            msg.setArchivoMp3Url(audioUrl);

            leadOpt = clienteResidencialRepository.findById(msg.getLeadId());
            if (leadOpt.isPresent() && isAlreadyTranscribed(leadOpt.get())) {
                log.warn("⏩ Lead {} ya fue transcrito por otro proceso - ACK y descartado", msg.getLeadId());
                safeAck(channel, tag, "Lead transcrito por otro proceso");
                return;
            }

            Map<String, Object> tr = callTranscriptionAPIOptimized(msg);

            saveTranscriptionResult(msg, tr);

            safeAck(channel, tag, "Transcripción completada para lead " + msg.getLeadId());
            log.info("✅ Transcripción persistida para lead {}", msg.getLeadId());

            if (!"TRANSCRIPTION_ONLY".equals(msg.getProcessType())) {
                sendToComparisonQueue(msg, tr);
                log.info("📤 Lead {} enviado a cola de comparación", msg.getLeadId());
            } else {
                log.info("⏩ Lead {} procesado SOLO para transcripción - NO se envía a comparación", msg.getLeadId());
            }

        } catch (Exception ex) {
            log.error("❌ Error procesando lead {}: {}", msg.getLeadId(), ex.getMessage(), ex);

            boolean shouldRetry = !msg.intentosAgotados() && isRetryableError(ex);
            if (shouldRetry) {
                msg.incrementarIntentos();
            }

            safeNack(channel, tag, shouldRetry, "Error general: " + ex.getMessage());
        }
    }

    @RabbitListener(queues = RabbitMQConfig.COMPARISON_QUEUE, containerFactory = "autoAckFactory")
    public void processComparisonMessage(TranscriptionQueueMessage message) {
        if (!processingActive.get()) {
            log.info("Procesamiento pausado, reenviando mensaje de comparación a la cola");
            rabbitTemplate.convertAndSend(
                    RabbitMQConfig.COMPARISON_EXCHANGE,
                    RabbitMQConfig.COMPARISON_ROUTING_KEY,
                    message
            );
            return;
        }

        log.info("Procesando mensaje de comparación para lead ID: {}", message.getLeadId());

        try {
            message.incrementarIntentos();

            Map<String, Object> comparisonResult = callComparisonAPI(message);
            saveComparisonResult(message, comparisonResult);

            log.info("Comparación completada para lead ID: {}", message.getLeadId());

        } catch (Exception e) {
            log.error("Error al procesar comparación para lead ID: {}", message.getLeadId(), e);

            if (!message.intentosAgotados()) {
                log.info("Reenviando mensaje de comparación para reintento. Intento: {}/{}",
                        message.getIntentos(), message.getMaxIntentos());

                rabbitTemplate.convertAndSend(
                        RabbitMQConfig.COMPARISON_EXCHANGE,
                        RabbitMQConfig.COMPARISON_ROUTING_KEY,
                        message
                );
            } else {
                log.error("Intentos agotados para comparación de lead ID: {}", message.getLeadId());
            }
        }
    }

    private String findMp3FileInGoogleDrive(TranscriptionQueueMessage msg) {
        String agente = msg.getNumeroAgenteNormalizado();
        String movil = msg.getNumeroMovil();

        if (!StringUtils.hasText(movil) || !StringUtils.hasText(agente)) {
            log.warn("Lead {} descartado: falta móvil o agente (móvil='{}', agente='{}')", msg.getLeadId(), movil, agente);
            return null;
        }

        log.info("🔍 Buscando audio para móvil {} + agente {} (lead {})", movil, agente, msg.getLeadId());

        try {
            String fechaCreacionStr = msg.getFechaCreacion() != null
                    ? msg.getFechaCreacion().toLocalDate().toString()
                    : null;

            String url = googleDriveService.findAudioByMovilAgentAndDate(movil, agente, fechaCreacionStr, null);

            if (url != null) {
                log.info("✅ Archivo encontrado: {}", url);
                return processAudioFileAsGsm(url, msg);
            } else {
                log.warn("🛑 No se encontró ningún archivo para móvil {} + agente {} (lead {})", movil, agente, msg.getLeadId());
                return null;
            }

        } catch (Exception e) {
            log.error("❌ Error buscando audio en Drive para lead {}: {}", msg.getLeadId(), e.getMessage(), e);
            return null;
        }
    }

    private String processAudioFileAsGsm(String audioUrl, TranscriptionQueueMessage message) {
        try {
            log.info("Procesando archivo de audio como GSM: {}", audioUrl);

            if (!audioConversionService.isFFmpegAvailable()) {
                log.warn("FFmpeg no está disponible. Enviando archivo GSM directamente al API de transcripción");
                log.warn("NOTA: Para mejor calidad de transcripción, instale FFmpeg para conversión a MP3");
                return audioUrl;
            }

            log.info("Archivo GSM detectado (por contexto de grabación), iniciando conversión a MP3");
            String outputFileName = generateMp3FileName(message);
            String mp3Url = audioConversionService.convertGsmToMp3(audioUrl, outputFileName);

            message.setNombreArchivoMp3(outputFileName + ".mp3");

            log.info("Conversión GSM a MP3 completada. URL del MP3: {}", mp3Url);
            return mp3Url;

        } catch (Exception e) {
            log.error("Error al procesar archivo de audio GSM para lead ID: {}", message.getLeadId(), e);
            log.warn("Fallback: Enviando archivo GSM original al API de transcripción");
            return audioUrl;
        }
    }

    private String generateMp3FileName(TranscriptionQueueMessage message) {
        StringBuilder fileName = new StringBuilder();
        LocalDateTime now = LocalDateTime.now();
        fileName.append(now.format(DateTimeFormatter.ofPattern("yyyyMMdd-HHmmss")));

        if (message.getNumeroMovil() != null && !message.getNumeroMovil().trim().isEmpty()) {
            String cleanMovil = message.getNumeroMovil().replaceAll("[^0-9]", "");
            fileName.append("_").append(cleanMovil);
        }

        if (message.getNumeroAgenteNormalizado() != null) {
            fileName.append("_agent").append(String.format("%03d", Integer.parseInt(message.getNumeroAgenteNormalizado())));
        }

        fileName.append("_lead").append(message.getLeadId());
        fileName.append("_converted");

        return fileName.toString();
    }

    private String generateEnhancedCallId(TranscriptionQueueMessage message) {
        LocalDateTime now = LocalDateTime.now();
        String timestamp = now.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String randomSuffix = String.valueOf((int) (Math.random() * 10000));
        String callId = String.format("CALL_%s_%s", timestamp, randomSuffix);

        log.info("🆔 Call ID SIMPLE generado: {} para lead {} (COMPATIBLE CON SWAGGER)",
                callId, message.getLeadId());

        return callId;
    }

    private void sendAudioForTranscriptionAsyncSafe(TranscriptionQueueMessage message, long fileSize) {
        try {
            System.out.println("📤 Enviando audio para transcripción asíncrona...");
            double fileSizeMb = fileSize / (1024.0 * 1024.0);
            System.out.println("📏 Tamaño del archivo: " + String.format("%.2f MB", fileSizeMb));

            byte[] audioFileBytes = transcriptionRestTemplate.getForObject(message.getArchivoMp3Url(), byte[].class);
            if (audioFileBytes == null || audioFileBytes.length == 0) {
                throw new RuntimeException("No se pudo descargar el archivo de audio");
            }

            System.out.println("📁 Archivo descargado: " + audioFileBytes.length + " bytes");

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);

            ByteArrayResource audioResource = new ByteArrayResource(audioFileBytes) {
                @Override
                public String getFilename() {
                    return message.getNombreArchivoMp3() != null ?
                            message.getNombreArchivoMp3() : "audio.mp3";
                }
            };

            LocalDateTime now = LocalDateTime.now();
            MultiValueMap<String, Object> requestData = new LinkedMultiValueMap<>();
            requestData.add("audio_file", audioResource);
            requestData.add("whisper_model", message.getWhisperModel());
            requestData.add("device", message.getDevice());
            requestData.add("target_language", message.getTargetLanguage());
            requestData.add("call_id", message.getCallId());
            requestData.add("call_type", "lead");
            requestData.add("agent_id", message.getAgentId());
            requestData.add("caller_phone", message.getNumeroMovil());
            requestData.add("call_datetime", now.toString());

            HttpEntity<MultiValueMap<String, Object>> entity = new HttpEntity<>(requestData, headers);

            log.info("🔍 DEBUG ENVÍO - Parámetros enviados al API:");
            log.info("  📋 Call ID: {}", message.getCallId());
            log.info("  👤 Agent ID: {}", message.getAgentId());
            log.info("  📱 Caller Phone: {}", message.getNumeroMovil());
            log.info("  📅 Call DateTime: {}", now.toString());
            log.info("  🎤 Whisper Model: {}", message.getWhisperModel());
            log.info("  💻 Device: {}", message.getDevice());
            log.info("  🌍 Target Language: {}", message.getTargetLanguage());
            log.info("  📞 Call Type: lead");
            log.info("  🔗 API URL: {}/transcribe/", transcriptionApiUrl);

            String realCallId = message.getCallId();

            try {
                System.out.println("🚀 Enviando petición al API...");
                System.out.println("🆔 Call ID enviado: " + message.getCallId());
                System.out.println("👤 Agent ID: " + message.getAgentId());
                System.out.println("📅 Call DateTime: " + now);

                ResponseEntity<Map> response = quickRestTemplate.postForEntity(
                        transcriptionApiUrl + "/transcribe/", entity, Map.class);

                if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                    Map<String, Object> responseBody = response.getBody();
                    if (responseBody.containsKey("call_id")) {
                        realCallId = (String) responseBody.get("call_id");
                        System.out.println("✅ RESPUESTA RECIBIDA - Call ID real del API: " + realCallId);
                        log.info("✅ CALL ID REAL OBTENIDO - Enviado: {} → API asignó: {}",
                                message.getCallId(), realCallId);
                        message.setCallId(realCallId);
                    }
                }

                System.out.println("✅ Petición enviada exitosamente");
                log.info("✅ ENVÍO EXITOSO - Call ID final: {}", realCallId);

            } catch (ResourceAccessException ex) {
                System.out.println("⚠️ Timeout esperado (RunPod procesando en background)");
                System.out.println("📝 Usando call_id generado para polling: " + realCallId);
                log.info("⚠️ TIMEOUT ESPERADO - Usando call_id generado: {} para polling", realCallId);
                log.debug("🔄 ResourceAccessException: {}", ex.getMessage());

            } catch (Exception e) {
                System.out.println("⚠️ Excepción durante envío - usando call_id generado");
                System.out.println("📝 Call ID para polling: " + realCallId);
                log.warn("⚠️ EXCEPCIÓN EN ENVÍO - Usando call_id generado: {} - Error: {}",
                        realCallId, e.getMessage());
                log.debug("🔄 Excepción completa: {}", e.getMessage());
            }

            message.setCallId(realCallId);

            System.out.println("📋 PARÁMETROS FINALES PARA POLLING:");
            System.out.println("  🆔 Call ID final: " + realCallId);
            System.out.println("  👤 Agent ID: " + message.getAgentId());
            System.out.println("  📱 Caller Phone: " + message.getNumeroMovil());
            System.out.println("  📅 Call DateTime enviado: " + now.toString());
            System.out.println("  📅 Fecha para polling: " + LocalDate.now().toString());

            log.info("📋 PARÁMETROS CRÍTICOS - Call ID: {}, Agent ID: {}, Phone: {}, DateTime: {}",
                    realCallId, message.getAgentId(), message.getNumeroMovil(), now.toString());

        } catch (Exception e) {
            System.err.println("❌ Error crítico enviando audio: " + e.getMessage());
            log.error("❌ ERROR CRÍTICO EN ENVÍO - Call ID: {} - Error: {}", message.getCallId(), e.getMessage(), e);
            throw new RuntimeException("Error crítico enviando audio: " + e.getMessage(), e);
        }
    }

    private boolean isChannelOpen(Channel channel) {
        try {
            return channel != null && channel.isOpen();
        } catch (Exception e) {
            return false;
        }
    }

    private void safeAck(Channel channel, long tag, String reason) {
        try {
            if (isChannelOpen(channel)) {
                channel.basicAck(tag, false);
                log.debug("✅ ACK: {}", reason);
            } else {
                log.warn("⚠️ Channel cerrado - no ACK: {}", reason);
            }
        } catch (Exception e) {
            log.error("❌ Error en ACK seguro: {}", e.getMessage());
        }
    }

    private void safeNack(Channel channel, long tag, boolean requeue, String reason) {
        try {
            if (isChannelOpen(channel)) {
                channel.basicNack(tag, false, requeue);
                log.debug("📋 NACK {} (requeue={}): {}", requeue ? "reintento" : "DLQ", requeue, reason);
            } else {
                log.warn("⚠️ Channel cerrado - no NACK: {}", reason);
            }
        } catch (Exception e) {
            log.error("❌ Error en NACK seguro: {}", e.getMessage());
        }
    }

    private boolean isRetryableError(Exception ex) {
        String message = ex.getMessage().toLowerCase();

        if (message.contains("timeout") || message.contains("connection") ||
                message.contains("network") || message.contains("unavailable")) {
            return true;
        }

        if (message.contains("channel") && message.contains("closed")) {
            return false;
        }

        return true;
    }

    private Map<String, Object> enrichAudioFileWithMetadata(Map<String, Object> audioFile) {
        String fileName = (String) audioFile.getOrDefault("name", "");
        String[] extracted = extractMovilAndAgentFromFileName(fileName);

        audioFile.put("extractedMovil", extracted != null ? extracted[0] : null);
        audioFile.put("extractedAgente", extracted != null ? extracted[1] : null);
        audioFile.put("hasValidData", extracted != null);

        return audioFile;
    }

    private boolean shouldProcessAudioFile(Map<String, Object> audioFile,
                                           Set<String> nombresRegistrados,
                                           Map<String, ClienteResidencial> leadsIndex) {
        String fileName = (String) audioFile.get("name");
        String mobile = (String) audioFile.get("extractedMovil");
        String agent = (String) audioFile.get("extractedAgente");
        Boolean hasValidData = (Boolean) audioFile.get("hasValidData");

        if (nombresRegistrados.contains(fileName)) {
            if (log.isDebugEnabled()) log.debug("⏩ {} ya registrado", fileName);
            return false;
        }

        if (!hasValidData || mobile == null || agent == null) {
            if (log.isDebugEnabled()) log.debug("⏩ {} sin datos válidos - mantener para registro", fileName);
            return true;
        }

        String key = mobile + "_" + normalizeAgentNumber(agent);
        ClienteResidencial lead = leadsIndex.get(key);

        if (lead == null) {
            if (log.isDebugEnabled()) log.debug("⏩ {} sin lead - mantener para registro", fileName);
            return true;
        }

        boolean alreadyTranscribed = isAlreadyTranscribed(lead);
        if (alreadyTranscribed && log.isDebugEnabled()) {
            log.debug("⏩ {} lead ya transcrito", fileName);
        }

        return !alreadyTranscribed;
    }

    /**
     * 📝 Registra audio sin lead en tabla separada para análisis futuro
     */
    private void registrarAudioSinLead(Map<String, Object> audioFile, LocalDate fechaProcesamiento, String motivo) {
        try {
            String fileName = (String) audioFile.get("name");
            String extractedMovil = (String) audioFile.get("extractedMovil");
            String extractedAgente = (String) audioFile.get("extractedAgente");

            if (extractedMovil != null && extractedAgente != null) {
                log.info("🔍 Analizando audio aparentemente sin lead: {}", fileName);
                log.info(" 📱 Móvil extraído: {}", extractedMovil);
                log.info(" 👤 Agente extraído: {}", extractedAgente);
                log.info(" 📅 Fecha: {}", fechaProcesamiento);

                ClienteResidencial leadEncontrado = findExistingLeadForAudio(extractedMovil, extractedAgente, fechaProcesamiento);
                if (leadEncontrado != null) {
                    log.warn("⚠️ FALSO POSITIVO DETECTADO: Audio {} SÍ tiene lead correspondiente (ID: {})", fileName, leadEncontrado.getId());
                    log.warn(" 📋 Lead - móvil: {}, agente: {}, fecha: {}", leadEncontrado.getMovilContacto(), leadEncontrado.getNumeroAgente(), leadEncontrado.getFechaCreacion());
                    motivo = "LEAD_ENCONTRADO_EN_SEGUNDA_VERIFICACION - ID: " + leadEncontrado.getId();
                } else {
                    log.info("✅ Confirmado: Audio {} realmente no tiene lead correspondiente", fileName);
                }
            }

            audioSinLeadService.registrarAudioSinLead(audioFile, motivo);
            log.debug("📝 Audio registrado como sin lead: {} - Motivo: {}", fileName, motivo);

        } catch (Exception e) {
            log.error("❌ Error al registrar audio sin lead: {}", e.getMessage(), e);
        }
    }

    /**
     * Extrae el móvil y agente del nombre del archivo de audio
     */
    private String[] extractMovilAndAgentFromFileName(String fileName) {
        try {
            if (fileName == null || fileName.trim().isEmpty()) {
                log.warn("Nombre de archivo vacío o nulo");
                return null;
            }

            String nameWithoutExtension = fileName;
            if (fileName.contains(".")) {
                nameWithoutExtension = fileName.substring(0, fileName.lastIndexOf("."));
            }

            log.debug("Procesando nombre de archivo: {} -> {}", fileName, nameWithoutExtension);

            // Formato específico: fecha-hora_movil_TESTCAMP_agentXXX(-all)
            if (nameWithoutExtension.contains("TESTCAMP_agent")) {
                String[] mainParts = nameWithoutExtension.split("-");
                if (mainParts.length >= 2) {
                    String restoParte = mainParts.length > 2
                            ? String.join("-", Arrays.copyOfRange(mainParts, 1, mainParts.length))
                            : mainParts[1];

                    String[] parts = restoParte.split("_");
                    if (parts.length >= 4) {
                        String movil = parts[1].trim();
                        String agenteRaw = parts[3].trim();

                        if (agenteRaw.contains("-")) {
                            agenteRaw = agenteRaw.split("-")[0];
                        }

                        String agente = normalizeAgentNumber(agenteRaw.replaceAll("^agent", ""));
                        if (movil.matches("\\d{6,15}") && agente != null && !agente.isEmpty()) {
                            log.debug("✅ Extraído TESTCAMP REAL del archivo '{}': móvil='{}', agente='{}'", fileName, movil, agente);
                            return new String[]{movil, agente};
                        }
                    }
                }
            }

            String[] separators = {"_", "-", " "};
            for (String separator : separators) {
                if (nameWithoutExtension.contains(separator)) {
                    String[] parts = nameWithoutExtension.split(Pattern.quote(separator));
                    if (parts.length >= 2) {
                        String movil = parts[0].trim();
                        String agente = parts[1].trim();
                        if (movil.matches("\\d{6,15}")) {
                            agente = normalizeAgentNumber(agente);
                            if (agente != null && !agente.isEmpty()) {
                                log.debug("✅ Extraído tradicional del archivo '{}': móvil='{}', agente='{}'", fileName, movil, agente);
                                return new String[]{movil, agente};
                            }
                        }
                    }
                }
            }

            if (nameWithoutExtension.matches("\\d{6,15}[a-zA-Z]*\\d+")) {
                String movil = nameWithoutExtension.replaceAll("^(\\d{6,15}).*", "$1");
                String agenteRaw = nameWithoutExtension.substring(movil.length());
                String agente = normalizeAgentNumber(agenteRaw);
                if (agente != null && !agente.isEmpty()) {
                    log.debug("Extraído con patrón del archivo '{}': móvil='{}', agente='{}'", fileName, movil, agente);
                    return new String[]{movil, agente};
                }
            }

            log.warn("❌ No se pudo extraer móvil y agente del archivo: {} (formato no reconocido)", fileName);
            return null;

        } catch (Exception e) {
            log.error("Error al extraer móvil y agente del archivo '{}': {}", fileName, e.getMessage());
            return null;
        }
    }

    /**
     * 📋 Extrae la transcripción de la respuesta del API de forma segura
     */
    private Map<String, Object> extractTranscriptionFromResponse(Map<String, Object> responseBody, String strategy) {
        try {
            if (responseBody == null) {
                System.out.println("⚠️ " + strategy + " - Respuesta nula");
                return null;
            }

            System.out.println("🔑 Claves en respuesta: " + responseBody.keySet());

            if (responseBody.containsKey("results")) {
                System.out.println("📋 Respuesta paginada detectada");

                Object resultsObj = responseBody.get("results");
                if (resultsObj instanceof List) {
                    List<?> results = (List<?>) resultsObj;
                    System.out.println("📋 " + strategy + " - Encontradas " + results.size() + " transcripciones en lista");

                    if (!results.isEmpty()) {
                        Object firstResult = results.get(0);
                        if (firstResult instanceof Map) {
                            Map<String, Object> transcription = (Map<String, Object>) firstResult;
                            System.out.println("✅ " + strategy + " - Transcripción encontrada en formato paginado");
                            return transcription;
                        }
                    }
                }
            }
            else if (responseBody.containsKey("transcription") || responseBody.containsKey("text")) {
                System.out.println("✅ " + strategy + " - Transcripción encontrada en formato directo");
                return responseBody;
            }
            else if (responseBody.containsKey("call_id") &&
                    (responseBody.containsKey("transcription") || responseBody.containsKey("text"))) {
                System.out.println("✅ " + strategy + " - Transcripción encontrada en formato completo");
                return responseBody;
            }

            System.out.println("⚠️ " + strategy + " - No se encontraron transcripciones en formato esperado");
            return null;

        } catch (Exception e) {
            System.out.println("❌ Error procesando respuesta de " + strategy + ": " + e.getMessage());
            return null;
        }
    }

    /**
     * Llama a la API de comparación externa
     */
    private Map<String, Object> callComparisonAPI(TranscriptionQueueMessage message) {
        try {
            Optional<ClienteResidencial> leadOpt = clienteResidencialRepository.findById(message.getLeadId());
            if (!leadOpt.isPresent()) {
                throw new RuntimeException("Lead no encontrado para comparación");
            }

            ClienteResidencial lead = leadOpt.get();

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            Map<String, Object> requestData = new HashMap<>();
            requestData.put("texto_audio", lead.getTextoTranscription());
            requestData.put("datos_lead", buildCompleteLeadStructure(lead));

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestData, headers);

            log.info("=== 📋 DATOS ENVIADOS AL API COMPARADOR ===");
            log.info("🎯 Lead ID: {}", message.getLeadId());
            log.info("📡 API URL: {}", comparisonApiUrl + "/comparar/");
            log.info("🔤 texto_audio (longitud): {} caracteres",
                    lead.getTextoTranscription() != null ? lead.getTextoTranscription().length() : 0);
            log.info("👤 nombresApellidos: {}", lead.getNombresApellidos());
            log.info("📞 movilContacto: {}", lead.getMovilContacto());
            log.info("🎯 numeroAgente: {}", lead.getNumeroAgente());
            log.info("================================================");

            String url = comparisonApiUrl + "/comparar/";
            log.info("🚀 Enviando petición al API comparador...");
            ResponseEntity<Map> response = restTemplate.postForEntity(url, entity, Map.class);

            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                return response.getBody();
            } else {
                throw new RuntimeException("API de comparación retornó estado: " + response.getStatusCode());
            }

        } catch (Exception e) {
            log.error("Error al llamar API de comparación para lead ID: {}", message.getLeadId(), e);
            throw new RuntimeException("Error en API de comparación: " + e.getMessage(), e);
        }
    }

    /**
     * Construye la estructura completa del lead para el API comparador
     */
    private Map<String, Object> buildCompleteLeadStructure(ClienteResidencial lead) {
        Map<String, Object> datosLead = new HashMap<>();

        datosLead.put("campania", lead.getCampania());
        datosLead.put("nombresApellidos", lead.getNombresApellidos());
        datosLead.put("nifNie", lead.getNifNie());
        datosLead.put("permanencia", lead.getPermanencia());
        datosLead.put("direccion", lead.getDireccion());
        datosLead.put("planActual", lead.getPlanActual());
        datosLead.put("codigoPostal", lead.getCodigoPostal());
        datosLead.put("provincia", lead.getProvincia());
        datosLead.put("distrito", lead.getDistrito());
        datosLead.put("numeroMoviles", lead.getNumeroMoviles());
        datosLead.put("textoTranscription", lead.getTextoTranscription());
        datosLead.put("velocidad", lead.getVelocidad());
        datosLead.put("autorizaSeguros", lead.getAutorizaSeguros());
        datosLead.put("autorizaEnergias", lead.getAutorizaEnergias());
        datosLead.put("deseaPromocionesLowi", lead.getDeseaPromocionesLowi());
        datosLead.put("nacionalidad",          lead.getNacionalidad());
        datosLead.put("correoElectronico",     lead.getCorreoElectronico());
        datosLead.put("cuentaBancaria",        lead.getCuentaBancaria());
        datosLead.put("tipoFibra",             lead.getTipoFibra());
        datosLead.put("fijoCompania",          lead.getFijoCompania());
        datosLead.put("icc",                   lead.getIcc());
        datosLead.put("movilesAPortar",        lead.getMovilesAPortar());
        datosLead.put("tipoTecnologia",        lead.getTipoTecnologia());
        datosLead.put("futbol",                lead.getFutbol());

        Map<String, Object> usuario = new HashMap<>();

        try {
            if (lead.getUsuario() != null) {
                usuario.put("id", lead.getUsuario().getId());
                usuario.put("username", null);
                usuario.put("nombre", null);
                usuario.put("apellido", null);
            } else {
                usuario.put("id", null);
                usuario.put("username", null);
                usuario.put("nombre", null);
                usuario.put("apellido", null);
            }

        } catch (Exception e) {
            log.warn("Error al acceder a datos del usuario para lead {}: {}", lead.getId(), e.getMessage());

            usuario.put("id", null);
            usuario.put("username", null);
            usuario.put("nombre", null);
            usuario.put("apellido", null);
        }

        datosLead.put("usuario", usuario);

        log.debug("Estructura completa del lead construida para API comparador. Lead ID: {}", lead.getId());
        return datosLead;
    }

    /**
     * 💾 Guarda resultado de transcripción con organización mejorada
     */
    private void saveTranscriptionResult(TranscriptionQueueMessage message, Map<String, Object> transcriptionResult) {
        try {
            Optional<ClienteResidencial> leadOpt = clienteResidencialRepository.findById(message.getLeadId());
            if (!leadOpt.isPresent()) {
                throw new RuntimeException("Lead no encontrado para guardar transcripción");
            }
            ClienteResidencial lead = leadOpt.get();

            String transcriptionText = (String) Optional
                    .ofNullable(transcriptionResult.get("transcription"))
                    .orElse(transcriptionResult.get("original_text"));

            if (transcriptionText != null && !transcriptionText.trim().isEmpty()) {
                lead.setTextoTranscription(transcriptionText);
                log.info("✅ Texto de transcripción extraído: {} caracteres", transcriptionText.length());

                if (transcriptionText.length() < 10) {
                    log.warn("⚠️ ADVERTENCIA: Texto de transcripción muy corto ({} chars): {}",
                            transcriptionText.length(), transcriptionText);
                }
            } else {
                log.error("❌ ERROR: No se encontró texto de transcripción válido");
                log.error("🔍 Claves disponibles en respuesta: {}", transcriptionResult.keySet());
                lead.setObservacion("TRANSCRIPTION_EMPTY");
            }

            String nombreArchivoMp3 = extractMp3FileName(message.getArchivoMp3Url());
            if (nombreArchivoMp3 != null) {
                lead.setNombreArchivoMp3(nombreArchivoMp3);
            }

            ClienteResidencial savedLead = clienteResidencialRepository.save(lead);
            log.info("✅ LEAD GUARDADO EN BD - ID: {}", savedLead.getId());
            log.info("📄 Texto guardado: {}",
                    savedLead.getTextoTranscription() != null ?
                            savedLead.getTextoTranscription().length() + " chars" : "NULL");

            if (transcriptionText != null && transcriptionText.length() > 10) {
                try {
                    String driveUrl = saveTranscriptionToGoogleDrive(transcriptionText, message);
                    if (driveUrl != null) {
                        lead.setUrlDriveTranscripcion(driveUrl);
                        clienteResidencialRepository.save(lead);
                        log.info("☁️ URL de Drive guardada en estructura organizada: {}", driveUrl);
                    }
                } catch (Exception e) {
                    log.warn("⚠️ Error guardando en Drive organizado (intentando método legacy): {}", e.getMessage());
                    try {
                        String legacyUrl = saveTranscriptionToGoogleDriveLegacy(transcriptionText, message);
                        if (legacyUrl != null) {
                            lead.setUrlDriveTranscripcion(legacyUrl);
                            clienteResidencialRepository.save(lead);
                            log.info("☁️ URL de Drive guardada (legacy): {}", legacyUrl);
                        }
                    } catch (Exception legacyEx) {
                        log.error("❌ Error guardando en Drive (legacy fallback): {}", legacyEx.getMessage());
                    }
                }
            }

            log.info("✅ Transcripción guardada completamente para lead ID: {}", message.getLeadId());

        } catch (Exception e) {
            log.error("❌ Error crítico guardando transcripción para lead ID: {} - {}",
                    message.getLeadId(), e.getMessage(), e);
            throw new RuntimeException("Error al guardar transcripción: " + e.getMessage(), e);
        }
    }

    /**
     * Extrae el nombre del archivo MP3 desde la URL de Google Drive
     */
    private String extractMp3FileName(String mp3Url) {
        try {
            if (mp3Url == null || mp3Url.trim().isEmpty()) {
                return null;
            }

            if (mp3Url.contains("drive.google.com")) {
                String fileId = extractGoogleDriveFileId(mp3Url);
                if (fileId != null) {
                    return googleDriveService.getFileName(fileId);
                }
            }

            String[] urlParts = mp3Url.split("/");
            String fileName = urlParts[urlParts.length - 1];

            if (fileName.contains("?")) {
                fileName = fileName.split("\\?")[0];
            }

            return fileName;

        } catch (Exception e) {
            log.warn("No se pudo extraer el nombre del archivo MP3 de la URL: {}", mp3Url, e);
            return null;
        }
    }

    /**
     * Extrae el ID del archivo de una URL de Google Drive
     */
    private String extractGoogleDriveFileId(String driveUrl) {
        try {
            if (driveUrl.contains("/file/d/")) {
                String[] parts = driveUrl.split("/file/d/");
                if (parts.length > 1) {
                    String idPart = parts[1];
                    return idPart.contains("/") ? idPart.split("/")[0] : idPart;
                }
            } else if (driveUrl.contains("id=")) {
                String[] parts = driveUrl.split("id=");
                if (parts.length > 1) {
                    String idPart = parts[1];
                    return idPart.contains("&") ? idPart.split("&")[0] : idPart;
                }
            }
            return null;
        } catch (Exception e) {
            log.warn("No se pudo extraer el ID del archivo de Google Drive: {}", driveUrl, e);
            return null;
        }
    }

    private String saveTranscriptionToGoogleDrive(String transcriptionText, TranscriptionQueueMessage message) {
        try {
            log.info("📁 Guardando transcripción organizada para lead ID: {}", message.getLeadId());

            LocalDate fechaTranscripcion = message.getFechaCreacion() != null
                    ? message.getFechaCreacion().toLocalDate()
                    : LocalDate.now();

            log.info("📅 Fecha para organización: {}", fechaTranscripcion);

            String folderId = driveOrganizationHelper.getOrCreateDateStructure(
                    FolderType.TEXTOS_TRANSCRITOS,
                    fechaTranscripcion
            );

            String fileName = generateTranscriptionFileNameEnhanced(message, fechaTranscripcion);
            String enrichedContent = createEnrichedTranscriptionContent(transcriptionText, message, fechaTranscripcion);

            String fileId = googleDriveService.uploadFile(
                    enrichedContent.getBytes(StandardCharsets.UTF_8),
                    fileName,
                    "text/plain",
                    folderId
            );

            String driveUrl = googleDriveService.getPublicLink(fileId);

            log.info("✅ Transcripción guardada en estructura organizada:");
            log.info("📁 Ubicación: TEXTOS_TRANSCRITOS/{}/{}/{}",
                    fechaTranscripcion.getYear(),
                    String.format("%02d-%s", fechaTranscripcion.getMonthValue(),
                            fechaTranscripcion.getMonth().getDisplayName(TextStyle.FULL, new Locale("es", "ES")).toUpperCase()),
                    fechaTranscripcion.format(DateTimeFormatter.ofPattern("dd-MM-yyyy")));
            log.info("📄 Archivo: {}", fileName);
            log.info("🔗 URL: {}", driveUrl);

            return driveUrl;

        } catch (Exception e) {
            log.error("❌ Error al guardar transcripción organizada para lead ID: {}", message.getLeadId(), e);
            return saveTranscriptionToGoogleDriveLegacy(transcriptionText, message);
        }
    }


    private String generateTranscriptionFileNameEnhanced(TranscriptionQueueMessage message, LocalDate fecha) {
        StringBuilder fileName = new StringBuilder();

        // CAMBIO 1: Añadir la fecha original del audio/lead (que ya viene en el parámetro 'fecha')
        if (fecha != null) {
            fileName.append(fecha.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        } else {
            // Si por alguna razón la fecha no viene, usar la fecha actual como fallback
            fileName.append(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        }
        fileName.append("_");

        // CAMBIO 2: Añadir solo la HORA del momento de procesamiento para mantener la unicidad
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HHmmss");
        fileName.append(now.format(timeFormatter));

        // El resto del código se mantiene igual para añadir el móvil, agente, etc.
        if (message.getNumeroMovil() != null && !message.getNumeroMovil().trim().isEmpty()) {
            String cleanMovil = message.getNumeroMovil().replaceAll("[^0-9]", "");
            fileName.append("_").append(cleanMovil);
        }

        if (message.getNumeroAgenteNormalizado() != null) {
            fileName.append("_agent").append(String.format("%03d",
                    Integer.parseInt(message.getNumeroAgenteNormalizado())));
        }

        fileName.append("_lead").append(message.getLeadId());
        fileName.append("_transcription");
        fileName.append(".txt");

        return fileName.toString();
    }

    private String createEnrichedTranscriptionContent(String transcriptionText,
                                                      TranscriptionQueueMessage message,
                                                      LocalDate fechaTranscripcion) {
        StringBuilder content = new StringBuilder();

        content.append("TRANSCRIPCIÓN DE LLAMADA - CRM MIDAS\n");
        content.append(String.join("", Collections.nCopies(50, "=")));
        content.append("\n\n");

        content.append("📊 INFORMACIÓN DEL LEAD:\n");
        content.append("─".repeat(30)).append("\n");
        content.append("🆔 Lead ID: ").append(message.getLeadId()).append("\n");
        content.append("📱 Móvil: ").append(message.getNumeroMovil() != null ? message.getNumeroMovil() : "N/A").append("\n");
        content.append("👤 Agente: ").append(message.getNumeroAgente() != null ? message.getNumeroAgente() : "N/A").append("\n");
        content.append("👤 Agente (Normalizado): ").append(message.getNumeroAgenteNormalizado() != null ? message.getNumeroAgenteNormalizado() : "N/A").append("\n");
        content.append("👥 Cliente: ").append(message.getNombreCliente() != null ? message.getNombreCliente() : "N/A").append("\n");
        content.append("🆔 DNI: ").append(message.getDniCliente() != null ? message.getDniCliente() : "N/A").append("\n");
        content.append("📅 Fecha Lead: ").append(message.getFechaCreacion() != null ? message.getFechaCreacion().format(DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss")) : "N/A").append("\n");
        content.append("📅 Fecha Transcripción: ").append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss"))).append("\n");

        content.append("\n🎵 INFORMACIÓN DEL AUDIO:\n");
        content.append("─".repeat(30)).append("\n");
        content.append("📄 Archivo: ").append(message.getNombreArchivoMp3() != null ? message.getNombreArchivoMp3() : "N/A").append("\n");
        content.append("🔗 URL: ").append(message.getArchivoMp3Url() != null ? message.getArchivoMp3Url() : "N/A").append("\n");

        content.append("\n🤖 INFORMACIÓN DE PROCESAMIENTO:\n");
        content.append("─".repeat(30)).append("\n");
        content.append("🎙️ Modelo Whisper: ").append(message.getWhisperModel() != null ? message.getWhisperModel() : "N/A").append("\n");
        content.append("💻 Dispositivo: ").append(message.getDevice() != null ? message.getDevice() : "N/A").append("\n");
        content.append("🌍 Idioma: ").append(message.getTargetLanguage() != null ? message.getTargetLanguage() : "N/A").append("\n");
        content.append("📞 Call ID: ").append(message.getCallId() != null ? message.getCallId() : "N/A").append("\n");

        content.append("\n").append(String.join("", Collections.nCopies(50, "=")));
        content.append("\n📄 TEXTO TRANSCRITO:\n");
        content.append(String.join("", Collections.nCopies(50, "=")));
        content.append("\n\n");
        content.append(transcriptionText != null ? transcriptionText : "No se pudo obtener la transcripción");
        content.append("\n\n");
        content.append(String.join("", Collections.nCopies(50, "=")));
        content.append("\nFin de la transcripción - Generado automáticamente por CRM Midas");

        return content.toString();
    }

    private String saveTranscriptionToGoogleDriveLegacy(String transcriptionText, TranscriptionQueueMessage message) {
        try {
            log.info("📁 Guardando transcripción (método legacy) para lead ID: {}", message.getLeadId());

            String folderId = findOrCreateTranscriptionFolder();
            String fileName = generateTranscriptionFileName(message);
            String tempFilePath = createTempTranscriptionFile(transcriptionText, fileName);

            String fileId = googleDriveService.uploadFile(
                    createMultipartFileFromPath(tempFilePath, fileName + ".txt"),
                    fileName + ".txt",
                    folderId
            );

            String driveUrl = "https://drive.google.com/file/d/" + fileId + "/view";
            log.info("📁 Transcripción guardada (legacy): {}", driveUrl);
            return driveUrl;

        } catch (Exception e) {
            log.error("❌ Error al guardar transcripción (legacy) para lead ID: {}", message.getLeadId(), e);
            return null;
        }
    }

    private String findOrCreateTranscriptionFolder() throws IOException {
        String folderName = "TEXTOS_TRANSCRITOS";

        String existingFolderId = googleDriveService.findFolderByName(folderName);
        if (existingFolderId != null) {
            log.debug("📁 Carpeta de transcripciones legacy encontrada: {}", existingFolderId);
            return existingFolderId;
        }

        log.info("📁 Creando carpeta de transcripciones legacy: {}", folderName);
        String folderId = googleDriveService.createFolder(folderName);
        log.info("📁 Carpeta de transcripciones legacy creada: {}", folderId);
        return folderId;
    }

    private String generateTranscriptionFileName(TranscriptionQueueMessage message) {
        StringBuilder fileName = new StringBuilder();

        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd-HHmmss");
        fileName.append(now.format(formatter));

        if (message.getNumeroMovil() != null) {
            fileName.append("_").append(message.getNumeroMovil());
        }

        if (message.getNumeroAgenteNormalizado() != null) {
            fileName.append("_agent").append(message.getNumeroAgenteNormalizado());
        }

        fileName.append("_lead").append(message.getLeadId());
        fileName.append("_transcription");

        return fileName.toString();
    }

    private String createTempTranscriptionFile(String transcriptionText, String fileName) throws IOException {
        Path tempDir = Paths.get(System.getProperty("java.io.tmpdir"), "transcriptions");
        if (!Files.exists(tempDir)) {
            Files.createDirectories(tempDir);
        }
        Path tempFile = tempDir.resolve(fileName + ".txt");
        Files.write(tempFile, transcriptionText.getBytes(StandardCharsets.UTF_8));
        return tempFile.toString();
    }

    private MultipartFile createMultipartFileFromPath(String filePath, String fileName) throws IOException {
        Path path = Paths.get(filePath);
        byte[] content = Files.readAllBytes(path);

        return new MultipartFile() {
            @Override
            public String getName() { return "file"; }

            @Override
            public String getOriginalFilename() { return fileName; }

            @Override
            public String getContentType() { return "text/plain"; }

            @Override
            public boolean isEmpty() { return content.length == 0; }

            @Override
            public long getSize() { return content.length; }

            @Override
            public byte[] getBytes() { return content; }

            @Override
            public InputStream getInputStream() { return new ByteArrayInputStream(content); }

            @Override
            public void transferTo(File dest) throws IOException {
                Files.write(dest.toPath(), content);
            }
        };
    }

    /**
     * Guarda el resultado de la comparación en la base de datos
     */
    private void saveComparisonResult(TranscriptionQueueMessage message, Map<String, Object> comparisonResult) {
        try {
            log.info("=== 📥 RESPUESTA DEL API COMPARADOR ===");
            log.info("🎯 Lead ID: {}", message.getLeadId());
            log.info("📦 Tamaño del body: {} campos", comparisonResult.size());
            log.info("🔑 Claves disponibles: {}", comparisonResult.keySet());

            for (Map.Entry<String, Object> entry : comparisonResult.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();
                String valueType = value != null ? value.getClass().getSimpleName() : "null";
                String valuePreview = value != null ?
                        (value.toString().length() > 100 ? value.toString().substring(0, 100) + "..." : value.toString()) : "null";
                log.info(" 📝 {}: ({}) {}", key, valueType, valuePreview);
            }

            Optional<ClienteResidencial> leadOpt = clienteResidencialRepository.findById(message.getLeadId());
            if (!leadOpt.isPresent()) {
                throw new RuntimeException("Lead no encontrado para guardar comparación");
            }

            ClienteResidencial lead = leadOpt.get();
            Double porcentaje = extractPorcentajeFromComparadorResponse(comparisonResult);

            if (porcentaje != null) {
                BigDecimal notaBigDecimal = BigDecimal.valueOf(porcentaje);
                clienteResidencialService.actualizarNotaAgenteComparadorIA(message.getLeadId(), notaBigDecimal);
                log.info("💾 Nota del comparador guardada: {}", porcentaje);
            } else {
                log.warn("⚠️ No se encontró porcentaje en el comparador");
                log.warn("🔍 Campos disponibles: {}", comparisonResult.keySet());
            }

            try {
                transcriptionAnalysisService.saveAnalysis(message.getLeadId(), comparisonResult);
                log.info("📊 Análisis completo guardado para lead ID: {}", message.getLeadId());
            } catch (Exception e) {
                log.error("❌ Error guardando análisis (ignorado): {}", e.getMessage());
            }

            log.info("✅ Resultado guardado para lead ID: {}", message.getLeadId());
            log.info("================================================");

        } catch (Exception e) {
            log.error("❌ Error al guardar comparación para lead ID: {}", message.getLeadId(), e);
            throw new RuntimeException("Error al guardar comparación: " + e.getMessage(), e);
        }
    }

    /**
     * Extrae el porcentaje de coincidencia de la respuesta del API comparador
     */
    private Double extractPorcentajeFromComparadorResponse(Map<String, Object> comparisonResult) {
        try {
            Object estadisticasObj = comparisonResult.get("estadisticas");
            if (estadisticasObj instanceof Map) {
                Map<String, Object> estadisticas = (Map<String, Object>) estadisticasObj;
                Object porcentajePromedioObj = estadisticas.get("porcentaje_promedio");
                if (porcentajePromedioObj != null) {
                    Double porcentaje = convertToDouble(porcentajePromedioObj, "estadisticas.porcentaje_promedio");
                    if (porcentaje != null) {
                        log.info("✅ Porcentaje en 'estadisticas.porcentaje_promedio': {}", porcentaje);
                        return porcentaje;
                    }
                }
            }

            Object porcentajeObj = comparisonResult.get("porcentaje_coincidencia");
            if (porcentajeObj != null) {
                Double porcentaje = convertToDouble(porcentajeObj, "porcentaje_coincidencia");
                if (porcentaje != null) {
                    log.info("✅ Porcentaje en 'porcentaje_coincidencia': {}", porcentaje);
                    return porcentaje;
                }
            }

            String[] possibleKeys = {"porcentaje", "coincidencia", "score", "percentage", "nota"};
            for (String key : possibleKeys) {
                Object obj = comparisonResult.get(key);
                if (obj != null) {
                    Double porcentaje = convertToDouble(obj, key);
                    if (porcentaje != null) {
                        log.info("✅ Porcentaje en '{}': {}", key, porcentaje);
                        return porcentaje;
                    }
                }
            }

            Double calculated = calculatePercentageFromBooleanFields(comparisonResult);
            if (calculated != null) {
                log.info("✅ Porcentaje calculado desde booleanos: {}", calculated);
                return calculated;
            }

            log.warn("⚠️ No se encontró porcentaje en: {}", comparisonResult.keySet());
            return null;

        } catch (Exception e) {
            log.error("❌ Error al extraer porcentaje: {}", e.getMessage(), e);
            return null;
        }
    }

    private Double convertToDouble(Object obj, String fieldName) {
        try {
            if (obj instanceof Number) {
                return ((Number) obj).doubleValue();
            } else if (obj instanceof String) {
                String str = (String) obj;
                if (!str.trim().isEmpty()) {
                    return Double.parseDouble(str);
                }
            }
            return null;
        } catch (NumberFormatException e) {
            log.warn("⚠️ No se pudo convertir '{}' a número en '{}': {}", obj, fieldName, e.getMessage());
            return null;
        }
    }

    private Double calculatePercentageFromBooleanFields(Map<String, Object> response) {
        try {
            int totalFields = 0;
            int trueFields = 0;

            for (Map.Entry<String, Object> entry : response.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();
                if (key.startsWith("data.") && value instanceof Boolean) {
                    totalFields++;
                    if ((Boolean) value) {
                        trueFields++;
                    }
                }
            }

            if (totalFields > 0) {
                double percentage = (trueFields * 100.0) / totalFields;
                log.info("📊 Porcentaje calculado: {}/{} = {}%", trueFields, totalFields, String.format("%.1f", percentage));
                return percentage;
            }

            return null;
        } catch (Exception e) {
            log.warn("⚠️ Error calculando porcentaje desde booleanos: {}", e.getMessage());
            return null;
        }
    }

    private boolean sendLeadToTranscriptionQueueOnly(ClienteResidencial lead) {
        if (isAlreadyTranscribed(lead)) {
            log.debug("⏩ Lead {} ya tiene transcripción – no se envía a Rabbit", lead.getId());
            return false;
        }

        TranscriptionQueueMessage message = convertToQueueMessage(lead);
        message.setProcessType("TRANSCRIPTION_ONLY");

        rabbitTemplate.convertAndSend(
                RabbitMQConfig.TRANSCRIPTION_EXCHANGE,
                RabbitMQConfig.TRANSCRIPTION_ROUTING_KEY,
                message
        );
        return true;
    }

    private boolean sendLeadToComparisonQueueOnly(ClienteResidencial lead) {
        TranscriptionQueueMessage message = convertToQueueMessage(lead);
        message.setProcessType("COMPARISON_ONLY");

        rabbitTemplate.convertAndSend(
                RabbitMQConfig.COMPARISON_EXCHANGE,
                RabbitMQConfig.COMPARISON_ROUTING_KEY,
                message
        );
        return true;
    }

    private boolean hasTranscription(ClienteResidencial lead) {
        return lead.getTextoTranscription() != null &&
                !lead.getTextoTranscription().trim().isEmpty();
    }

    private List<ClienteResidencial> fetchPageWithTranscription(Pageable pageRequest,
                                                                String numeroAgente,
                                                                boolean allDates,
                                                                LocalDate selectedDate) {
        if (selectedDate != null) {
            return clienteResidencialRepository.findLeadsWithTranscriptionByDate(selectedDate, numeroAgente, pageRequest);
        } else if (allDates) {
            return clienteResidencialRepository.findLeadsWithTranscriptionAllDates(numeroAgente, pageRequest);
        } else {
            return clienteResidencialRepository.findLeadsWithTranscriptionCurrentMonth(numeroAgente, pageRequest);
        }
    }

    private Map<String, Object> processAllAudiosFromDateTranscriptionOnly(LocalDate selectedDate, int batchSize,
                                                                          Map<String, Object> result, List<String> skipped) {
        int sent = 0, errors = 0;
        List<String> processed = new ArrayList<>();
        List<String> audioErrors = new ArrayList<>();
        List<String> audiosSinLead = new ArrayList<>();

        try {
            log.info("🎯 Obteniendo archivos de audio para fecha: {} (procesando máximo {} - SOLO TRANSCRIPCIÓN)", selectedDate, batchSize);
            List<Map<String, Object>> allAudioFiles = googleDriveService.getAllAudioFilesByDate(selectedDate.toString());
            log.info("📁 Encontrados {} archivos de audio para la fecha {}", allAudioFiles.size(), selectedDate);

            if (allAudioFiles.isEmpty()) {
                result.put("sentToQueue", 0);
                result.put("skippedNoAudio", skipped);
                result.put("errorsEnqueuing", 0);
                result.put("timestamp", LocalDateTime.now());
                result.put("processType", "TRANSCRIPTION_ONLY");
                result.put("message", "No se encontraron archivos de audio para la fecha especificada");
                return result;
            }

            List<Map<String, Object>> audiosSinProcesar = filtrarArchivosNoProcesados(allAudioFiles, selectedDate);
            log.info("🔍 Después del filtrado: {} archivos CON LEADS de {} totales", audiosSinProcesar.size(), allAudioFiles.size());

            int archivosAProcesar = Math.min(audiosSinProcesar.size(), batchSize);
            log.info("🎯 Procesando {} archivos (batchSize: {}, disponibles con leads: {})",
                    archivosAProcesar, batchSize, audiosSinProcesar.size());

            for (Map<String, Object> audioFile : audiosSinProcesar) {
                if (sent >= batchSize) {
                    log.info("🛑 Límite de lote alcanzado: {}", batchSize);
                    break;
                }

                try {
                    String fileName = (String) audioFile.get("name");
                    String fileId = (String) audioFile.get("id");
                    String downloadUrl = (String) audioFile.get("downloadUrl");

                    log.debug("🎵 Procesando archivo: {}", fileName);

                    String[] parts = extractMovilAndAgentFromFileName(fileName);
                    if (parts == null) {
                        audioErrors.add("Archivo con formato inválido: " + fileName);
                        continue;
                    }

                    String movil = parts[0];
                    String agente = parts[1];

                    ClienteResidencial existingLead = findExistingLeadForAudio(movil, agente, selectedDate);
                    if (existingLead != null) {
                        if (!isAlreadyTranscribed(existingLead)) {
                            existingLead.setNombreArchivoMp3(fileName);
                            boolean ok = sendLeadToTranscriptionQueueOnly(existingLead);
                            if (ok) {
                                sent++;
                                processed.add(fileName);
                                log.debug("✅ Lead {} enviado a transcripción", existingLead.getId());
                            } else {
                                errors++;
                                audioErrors.add("Error al enviar lead a cola: " + fileName);
                            }
                        } else {
                            skipped.add("Lead ya tiene transcripción: " + fileName);
                        }
                    } else {
                        audiosSinLead.add(fileName);
                        log.warn("❌ No se encontró lead para móvil: {}, agente: {} (norm: {}), fecha: {}", movil, agente, agente, selectedDate);
                        String motivo = String.format("TRANSCRIPCION_ONLY: No se encontró lead con móvil=%s y agente=%s para fecha=%s", movil, agente, selectedDate);
                        registrarAudioSinLead(audioFile, selectedDate, motivo);
                    }
                } catch (Exception e) {
                    log.error("❌ Error procesando archivo individual: {}", e.getMessage());
                    audioErrors.add("Error procesando: " + audioFile.get("name"));
                    errors++;
                }
            }

            result.put("sentToQueue", sent);
            result.put("processedFiles", processed);
            result.put("skippedNoAudio", skipped);
            result.put("audiosSinLead", audiosSinLead);
            result.put("audioErrors", audioErrors);
            result.put("errorsEnqueuing", errors);
            result.put("timestamp", LocalDateTime.now());
            result.put("processType", "TRANSCRIPTION_ONLY");
            result.put("totalAudioFiles", allAudioFiles.size());
            result.put("filteredAudioFiles", audiosSinProcesar.size());

            log.info("✔ processAllAudiosFromDateTranscriptionOnly completado. Enviados={}, Errores={}, AudiosSinLead={}",
                    sent, errors, audiosSinLead.size());

        } catch (Exception e) {
            log.error("❌ Error general en processAllAudiosFromDateTranscriptionOnly", e);
            result.put("error", e.getMessage());
        }

        return result;
    }

    private void sendToComparisonQueue(TranscriptionQueueMessage msg, Map<String,Object> tr) {
        msg.setIntentos(0);
        msg.setEstadoProcesamiento("PENDING");

        rabbitTemplate.convertAndSend(
                RabbitMQConfig.COMPARISON_EXCHANGE,
                RabbitMQConfig.COMPARISON_ROUTING_KEY,
                msg);

        log.info("📤 Lead {} enviado a cola de comparación", msg.getLeadId());
    }

    private boolean isAlreadyTranscribed(ClienteResidencial c) {
        boolean hasTextoTranscription = c.getTextoTranscription() != null && !c.getTextoTranscription().isBlank();
        boolean hasUrlDriveTranscripcion = c.getUrlDriveTranscripcion() != null && !c.getUrlDriveTranscripcion().isBlank();
        boolean hasNotaComparador = c.getNotaAgenteComparadorIA() != null;

        boolean isTranscribed = hasTextoTranscription || hasUrlDriveTranscripcion || hasNotaComparador;

        if (isTranscribed) {
            log.info("✅ Lead {} YA TIENE transcripción - textoTranscription: {}, urlDriveTranscripcion: {}, notaComparador: {}",
                    c.getId(),
                    hasTextoTranscription ? "SÍ (" + c.getTextoTranscription().length() + " chars)" : "NO",
                    hasUrlDriveTranscripcion ? "SÍ" : "NO",
                    hasNotaComparador ? "SÍ (" + c.getNotaAgenteComparadorIA() + ")" : "NO");
        } else {
            log.info("🔄 Lead {} NO tiene transcripción - Procederá a transcribir", c.getId());
        }

        return isTranscribed;
    }

    @Override
    public Page<ClienteResidencial> getPendingLeadsFromDatabasePaginated(
            int page,
            int size,
            String numeroAgente,
            boolean currentMonthOnly
    ) {
        boolean allDates = !currentMonthOnly;
        return getPendingLeadsFromDatabasePaginated(page, size, numeroAgente, allDates, null);
    }

    @Override
    public Page<ClienteResidencial> getPendingLeadsFromDatabasePaginated(
            int page,
            int size,
            String numeroAgente,
            boolean allDates,
            LocalDate selectedDate
    ) {
        log.info("▶ getPendingLeadsFromDatabasePaginated → page={}, size={}, agente='{}', allDates={}, selectedDate={}",
                page, size, numeroAgente, allDates, selectedDate);

        Pageable pageable = PageRequest.of(page, size, Sort.by("fechaCreacion").ascending());

        if (selectedDate != null) {
            LocalDateTime start = selectedDate.atStartOfDay();
            LocalDateTime end = selectedDate.atTime(LocalTime.MAX);
            return StringUtils.hasText(numeroAgente)
                    ? clienteResidencialRepository.findPendingLeadsByDateRangeAndAgent(start, end, numeroAgente.trim(), pageable)
                    : clienteResidencialRepository.findPendingLeadsByDateRange(start, end, pageable);
        } else if (!allDates) {
            LocalDateTime start = LocalDate.now().withDayOfMonth(1).atStartOfDay();
            LocalDateTime end = start.plusMonths(1).minusNanos(1);
            return StringUtils.hasText(numeroAgente)
                    ? clienteResidencialRepository.findPendingLeadsByDateRangeAndAgent(start, end, numeroAgente.trim(), pageable)
                    : clienteResidencialRepository.findPendingLeadsByDateRange(start, end, pageable);
        } else {
            return StringUtils.hasText(numeroAgente)
                    ? clienteResidencialRepository.findPendingLeadsByNumeroAgente(numeroAgente.trim(), pageable)
                    : clienteResidencialRepository.findPendingLeads(pageable);
        }
    }

    @Override
    public long getTotalPendingLeadsCount(String numeroAgente, boolean currentMonthOnly) {
        log.info("Contando leads pendientes. Agente: {}, Solo Mes Actual: {}", numeroAgente, currentMonthOnly);
        try {
            if (numeroAgente != null && !numeroAgente.trim().isEmpty()) {
                String normalizedAgent = normalizeAgentNumber(numeroAgente);
                if (currentMonthOnly) {
                    log.debug("🗓️ Repositorio: countPendingLeadsCurrentMonthByNumeroAgente para agente: {}", normalizedAgent);
                    return clienteResidencialRepository.countPendingLeadsCurrentMonthByNumeroAgente(normalizedAgent);
                } else {
                    log.debug("📅 Repositorio: countPendingLeadsByNumeroAgente para agente: {}", normalizedAgent);
                    return clienteResidencialRepository.countPendingLeadsByNumeroAgente(normalizedAgent);
                }
            } else {
                if (currentMonthOnly) {
                    log.debug("🗓️ Repositorio: countPendingLeadsCurrentMonth (todos los agentes)");
                    return clienteResidencialRepository.countPendingLeadsCurrentMonth();
                } else {
                    log.debug("📅 Repositorio: countPendingLeads (todos los agentes)");
                    return clienteResidencialRepository.countPendingLeads();
                }
            }
        } catch (Exception e) {
            log.error("Error al contar leads pendientes de la base de datos", e);
            return 0L;
        }
    }

    /**
     * 📊 Análisis detallado de audios vs leads para debugging y verificación
     */
    public Map<String, Object> analizarAudiosVsLeads(LocalDate fecha) {
        log.info("📊 Iniciando análisis detallado de audios vs leads para fecha: {}", fecha);

        Map<String, Object> resultado = new HashMap<>();
        List<String> coincidenciasEncontradas = new ArrayList<>();
        List<String> audiosSinLead = new ArrayList<>();
        List<String> leadsConAudio = new ArrayList<>();
        List<String> leadsSinAudio = new ArrayList<>();

        try {
            List<Map<String, Object>> audios = googleDriveService.getAllAudioFilesByDate(fecha.toString());
            log.info("🎵 Encontrados {} archivos de audio para fecha {}", audios.size(), fecha);

            Map<String, ClienteResidencial> leadsIndex = createLeadsIndexOptimizado(fecha);
            log.info("📋 Creado índice con {} leads para fecha {}", leadsIndex.size(), fecha);

            for (Map<String, Object> audio : audios) {
                String fileName = (String) audio.get("name");
                String[] extracted = extractMovilAndAgentFromFileName(fileName);
                String extractedMovil = extracted != null ? extracted[0] : null;
                String extractedAgente = extracted != null ? extracted[1] : null;

                if (extractedMovil != null && extractedAgente != null) {
                    String key = extractedMovil + "_" + normalizeAgentNumber(extractedAgente);
                    ClienteResidencial lead = leadsIndex.get(key);
                    if (lead != null) {
                        String coincidencia = String.format("Audio: %s -> Lead ID: %s (móvil: %s, agente: %s)",
                                fileName, lead.getId(), lead.getMovilContacto(), lead.getNumeroAgente());
                        coincidenciasEncontradas.add(coincidencia);
                        leadsConAudio.add(lead.getId().toString());
                    } else {
                        audiosSinLead.add(String.format("Audio: %s (móvil: %s, agente: %s) - SIN LEAD",
                                fileName, extractedMovil, extractedAgente));
                    }
                } else {
                    audiosSinLead.add("Audio: " + fileName + " - INFORMACIÓN INCOMPLETA");
                }
            }

            Set<String> leadsConAudioSet = new HashSet<>(leadsConAudio);
            for (ClienteResidencial lead : leadsIndex.values()) {
                if (!leadsConAudioSet.contains(lead.getId().toString())) {
                    leadsSinAudio.add(String.format("Lead ID: %s (móvil: %s, agente: %s) - SIN AUDIO",
                            lead.getId(), lead.getMovilContacto(), lead.getNumeroAgente()));
                }
            }

            resultado.put("fecha", fecha.toString());
            resultado.put("totalAudios", audios.size());
            resultado.put("totalLeads", leadsIndex.size());
            resultado.put("coincidenciasEncontradas", coincidenciasEncontradas.size());
            resultado.put("audiosSinLead", audiosSinLead.size());
            resultado.put("leadsSinAudio", leadsSinAudio.size());
            resultado.put("detalleCoincidencias", coincidenciasEncontradas);
            resultado.put("detalleAudiosSinLead", audiosSinLead);
            resultado.put("detalleLeadsSinAudio", leadsSinAudio);
            resultado.put("timestamp", LocalDateTime.now());

            log.info("📊 Análisis completado - Audios: {}, Leads: {}, Coincidencias: {}, Audios sin lead: {}, Leads sin audio: {}",
                    audios.size(), leadsIndex.size(), coincidenciasEncontradas.size(), audiosSinLead.size(), leadsSinAudio.size());

        } catch (Exception e) {
            log.error("❌ Error en análisis de audios vs leads: {}", e.getMessage(), e);
            resultado.put("error", e.getMessage());
        }

        return resultado;
    }

    /**
     * 🧪 Método de prueba: Simula el flujo exacto de Swagger UI para comparación
     */
    public Map<String, Object> debugSwaggerFlow(String agentId, String callerPhone) {
        try {
            System.out.println("🧪 SIMULANDO FLUJO DE SWAGGER UI...");

            String swaggerCallId = "CALL_" + UUID.randomUUID().toString().replace("-","").substring(0,8).toUpperCase();
            System.out.println("🆔 Call ID estilo Swagger: " + swaggerCallId);

            String currentDate = LocalDate.now().toString();
            String currentDateTime = LocalDateTime.now().toString();

            System.out.println("📋 PARÁMETROS ESTILO SWAGGER:");
            System.out.println("  🆔 call_id: " + swaggerCallId);
            System.out.println("  👤 agent_id: " + agentId);
            System.out.println("  📱 caller_phone: " + callerPhone);
            System.out.println("  📅 call_datetime: " + currentDateTime);
            System.out.println("  📅 fecha para búsqueda: " + currentDate);

            String transcriptionsUrl = transcriptionApiUrl.replace("/api", "/api/transcriptions/");

            System.out.println("🔍 BUSCANDO COMO SWAGGER UI...");

            String url1 = transcriptionsUrl + "?call_id=" + swaggerCallId;
            System.out.println("🔗 URL 1: " + url1);

            String url2 = transcriptionsUrl + "?agent_id=" + agentId + "&date=" + currentDate;
            System.out.println("🔗 URL 2: " + url2);

            String url3 = transcriptionsUrl + "?caller_phone=" + callerPhone + "&date=" + currentDate;
            System.out.println("🔗 URL 3: " + url3);

            Map<String, Object> result = new HashMap<>();
            result.put("swagger_call_id", swaggerCallId);
            result.put("agent_id", agentId);
            result.put("caller_phone", callerPhone);
            result.put("current_date", currentDate);
            result.put("current_datetime", currentDateTime);
            result.put("search_urls", List.of(url1, url2, url3));

            System.out.println("✅ Simulación de Swagger completada");
            return result;

        } catch (Exception e) {
            System.err.println("❌ Error en simulación de Swagger: " + e.getMessage());
            return Map.of("error", e.getMessage());
        }
    }

    /**
     * Método para verificar si el API de transcripción está funcionando
     */
    public void testTranscriptionApiHealth() {
        try {
            String healthUrl = transcriptionApiUrl.replace("/api", "/health");
            String transcriptionsUrl = transcriptionApiUrl.replace("/api", "/api/transcriptions/");

            System.out.println("🔍 Verificando salud del API...");

            try {
                ResponseEntity<String> healthResponse = restTemplate.getForEntity(healthUrl, String.class);
                System.out.println("💚 Health status: " + healthResponse.getStatusCode());
            } catch (Exception e) {
                System.out.println("❌ Health check falló: " + e.getMessage());
            }

            try {
                ResponseEntity<String> listResponse = restTemplate.getForEntity(transcriptionsUrl, String.class);
                System.out.println("📋 Lista transcripciones status: " + listResponse.getStatusCode());
                System.out.println("📄 Respuesta: " + listResponse.getBody().substring(0, Math.min(500, listResponse.getBody().length())));
            } catch (Exception e) {
                System.out.println("❌ Lista transcripciones falló: " + e.getMessage());
            }

        } catch (Exception e) {
            System.out.println("❌ Error general verificando API: " + e.getMessage());
        }
    }

    /**
     * Método de debugging: Verifica si existe una transcripción específica
     */
    public Map<String, Object> debugSearchTranscription(String callId, String agentId, String fecha) {
        try {
            System.out.println("🔍 DEBUG - Buscando transcripción:");
            System.out.println("  Call ID: " + callId);
            System.out.println("  Agent ID: " + agentId);
            System.out.println("  Fecha: " + fecha);

            String baseUrl = transcriptionApiUrl.replace("/api", "/api/transcriptions/");

            Map<String, Object> result1 = searchByCallIdOptimized(baseUrl, callId);
            Map<String, Object> result2 = searchByAgentDateOptimized(baseUrl, agentId, fecha);

            Map<String, Object> debugResult = new HashMap<>();
            debugResult.put("strategy1_call_id", result1 != null ? "FOUND" : "NOT_FOUND");
            debugResult.put("strategy2_agent_date", result2 != null ? "FOUND" : "NOT_FOUND");
            debugResult.put("best_result", result1 != null ? result1 : result2);

            return debugResult;

        } catch (Exception e) {
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", e.getMessage());
            return errorResult;
        }
    }

    private static String generateSimpleCallId() {
        return "CALL_" + UUID.randomUUID().toString().replace("-","").substring(0,8).toUpperCase();
    }
}
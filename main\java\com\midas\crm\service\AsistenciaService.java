package com.midas.crm.service;

import com.midas.crm.entity.Asistencia;
import com.midas.crm.entity.DTO.asistencia.AsistenciaCreateDTO;
import com.midas.crm.entity.DTO.asistencia.AsistenciaDTO;
import com.midas.crm.entity.DTO.asistencia.AsistenciaFilterDTO;
import com.midas.crm.entity.DTO.asistencia.AsistenciaUpdateDTO;
import com.midas.crm.utils.GenericResponse;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

public interface AsistenciaService {

    /**
     * Registra una nueva asistencia
     */
    GenericResponse<AsistenciaDTO> registrarAsistencia(AsistenciaCreateDTO dto);

    /**
     * Registra asistencia automática al hacer login
     */
    GenericResponse<AsistenciaDTO> registrarAsistenciaAutomatica(Long usuarioId, String ip, String dispositivo, String ubicacion);

    /**
     * Obtiene todas las asistencias con filtros
     */
    GenericResponse<Page<AsistenciaDTO>> obtenerAsistenciasConFiltros(AsistenciaFilterDTO filtros);

    /**
     * Obtiene asistencias por fecha específica
     */
    GenericResponse<List<AsistenciaDTO>> obtenerAsistenciasPorFecha(LocalDate fecha);

    /**
     * Obtiene asistencias de un usuario específico
     */
    GenericResponse<List<AsistenciaDTO>> obtenerAsistenciasPorUsuario(Long usuarioId);

    /**
     * Obtiene asistencias de un usuario en un rango de fechas
     */
    GenericResponse<List<AsistenciaDTO>> obtenerAsistenciasPorUsuarioYFechas(Long usuarioId, LocalDate fechaInicio, LocalDate fechaFin);

    /**
     * Obtiene estadísticas de asistencias por fecha
     */
    GenericResponse<Map<String, Object>> obtenerEstadisticasPorFecha(LocalDate fechaInicio, LocalDate fechaFin);

    /**
     * Registra salida de un usuario
     */
    GenericResponse<AsistenciaDTO> registrarSalida(Long usuarioId, String ip, String dispositivo, String ubicacion);

    /**
     * 🔥 NUEVO: Verifica si ya marcó salida hoy
     */
    boolean yaMarcoSalidaHoy(Long usuarioId);

    /**
     * Obtiene la asistencia por ID
     */
    GenericResponse<AsistenciaDTO> obtenerAsistenciaPorId(Long id);

    /**
     * Verifica si un usuario ya registró asistencia hoy
     */
    boolean yaRegistroAsistenciaHoy(Long usuarioId);

    /**
     * Inicia un break para el usuario
     */
    GenericResponse<AsistenciaDTO> iniciarBreak(Long usuarioId, String ip, String dispositivo, String ubicacion);

    /**
     * Finaliza un break para el usuario
     */
    GenericResponse<AsistenciaDTO> finalizarBreak(Long usuarioId, String ip, String dispositivo, String ubicacion);

    /**
     * Inicia ida al baño para el usuario
     */
    GenericResponse<AsistenciaDTO> iniciarBano(Long usuarioId, String ip, String dispositivo, String ubicacion);

    /**
     * Finaliza ida al baño para el usuario
     */
    GenericResponse<AsistenciaDTO> finalizarBano(Long usuarioId, String ip, String dispositivo, String ubicacion);

    /**
     * Inicia sesión CRM para el usuario
     */
    GenericResponse<AsistenciaDTO> iniciarSesionCrm(Long usuarioId, String ip, String dispositivo, String ubicacion);

    /**
     * Finaliza sesión CRM para el usuario
     */
    GenericResponse<AsistenciaDTO> finalizarSesionCrm(Long usuarioId, String ip, String dispositivo, String ubicacion);

    /**
     * Verifica si el usuario puede tomar un break (máximo 2 por día)
     */
    boolean puedeTomarBreak(Long usuarioId);

    /**
     * Verifica si el usuario puede ir al baño (máximo 2 por día)
     */
    boolean puedeIrAlBano(Long usuarioId);

    /**
     * Verifica si el usuario tiene una actividad activa
     */
    boolean tieneActividadActiva(Long usuarioId, Asistencia.TipoActividad tipoActividad);

    /**
     * 🔥 NUEVO: Obtiene el estado actual de todas las actividades de un usuario
     */
    GenericResponse<Map<String, Object>> obtenerEstadoActividades(Long usuarioId);

    /**
     * 🔥 NUEVO: Auto-finaliza actividades que excedan el tiempo límite
     */
    GenericResponse<List<AsistenciaDTO>> autoFinalizarActividadesPorTiempo(Long usuarioId);

    /**
     * 🔥 NUEVO: Obtiene estado completo de un usuario
     */
    GenericResponse<Map<String, Object>> obtenerEstadoCompleto(Long usuarioId);

    // ======================================
    // 🔥 MÉTODOS PARA ADMINISTRADORES
    // ======================================

    /**
     * 🔥 ADMIN: Obtiene usuarios de una sede específica
     */
    GenericResponse<List<Map<String, Object>>> obtenerUsuariosDeSede(Long sedeId);

    /**
     * 🔥 ADMIN: Obtiene usuarios de una sede específica con paginación
     */
    GenericResponse<Map<String, Object>> obtenerUsuariosDeSedePaginado(Long sedeId, int page, int size, String search);

    @Transactional(readOnly = true)
    GenericResponse<Map<String, Object>> obtenerAsistenciaUsuario(Long usuarioId);

    /**
     * 🔥 ADMIN: Dashboard de asistencias para una sede
     */
    GenericResponse<Map<String, Object>> obtenerDashboardSede(Long sedeId, LocalDate fecha);

    /**
     * 🔥 ADMIN: Exportar asistencias a Excel
     */
    byte[] exportarAsistenciasExcel(AsistenciaFilterDTO filtros);

    /**
     * 🔥 ADMIN: Exportar asistencias a CSV
     */
    byte[] exportarAsistenciasCSV(AsistenciaFilterDTO filtros);

    /**
     * 🔥 ADMIN: Generar reporte PDF de asistencias
     */
    byte[] generarReportePDF(AsistenciaFilterDTO filtros);

    Long extractUserIdFromToken(String token);
    GenericResponse<AsistenciaDTO> actualizarAsistenciaAdmin(Long asistenciaId, AsistenciaUpdateDTO dto);


}
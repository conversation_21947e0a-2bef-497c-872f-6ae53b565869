package com.midas.crm.service.serviceImpl;

import com.midas.crm.entity.DTO.EstadisticaSedeDTO;
import com.midas.crm.entity.DTO.EstadisticaSedePaginadaResponse;
import com.midas.crm.entity.DTO.estadisticas.DetalleContactoDTO;
import com.midas.crm.entity.DTO.estadisticas.EstadisticaTranscripcionCoordinadorDTO;
import com.midas.crm.entity.DTO.estadisticas.EstadisticaTranscripcionAsesorDTO;
import com.midas.crm.entity.DTO.cliente.ClienteConUsuarioDTO;
import com.midas.crm.entity.Role;
import com.midas.crm.entity.User;
import com.midas.crm.repository.AudioSinLeadRepository;
import com.midas.crm.repository.ClienteResidencialRepository;
import com.midas.crm.repository.UserRepository;
import com.midas.crm.service.ClienteResidencialExcelService;
import com.midas.crm.service.EstadisticasSedeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Implementación del servicio de estadísticas por sede
 */
@Service
public class EstadisticasSedeServiceImpl implements EstadisticasSedeService {

    @Autowired
    private ClienteResidencialRepository clienteResidencialRepository;

    @Autowired
    private AudioSinLeadRepository audioSinLeadRepo;

    @Autowired
    private UserRepository userRepository;

    @Override
    public List<EstadisticaSedeDTO> obtenerEstadisticasPorSede(Long sedeId, LocalDate fecha) {
        if (sedeId != null) {
            return clienteResidencialRepository.obtenerEstadisticasPorSedeYFecha(sedeId, fecha);
        } else {
            return clienteResidencialRepository.obtenerEstadisticasPorFechaSinPaginacion(fecha);
        }
    }

    @Override
    public List<EstadisticaSedeDTO> obtenerResumenPorSede(LocalDate fecha) {
        return clienteResidencialRepository.obtenerResumenPorSedeYFecha(fecha);
    }

    @Override
    public List<EstadisticaSedeDTO> obtenerEstadisticasPorRango(LocalDate fechaInicio, LocalDate fechaFin,
                                                                Long sedeId) {
        if (sedeId != null) {
            return clienteResidencialRepository.obtenerEstadisticasPorSedeYRango(sedeId, fechaInicio, fechaFin);
        } else {
            return clienteResidencialRepository.obtenerEstadisticasPorRango(fechaInicio, fechaFin);
        }
    }

    // ===== IMPLEMENTACIÓN DE MÉTODOS PAGINADOS =====

    @Override
    public EstadisticaSedePaginadaResponse obtenerEstadisticasPorSedePaginado(Long sedeId, Long supervisorId,
                                                                              LocalDate fecha,
                                                                              Pageable pageable) {

        Page<EstadisticaSedeDTO> page;

        // Determinar qué método usar según los filtros
        if (sedeId != null && supervisorId != null) {
            page = clienteResidencialRepository.obtenerEstadisticasPorSedeYSupervisorPaginado(sedeId, supervisorId,
                    fecha, pageable);
        } else if (sedeId != null) {
            page = clienteResidencialRepository.obtenerEstadisticasPorSedeYFechaPaginado(sedeId, fecha, pageable);
        } else if (supervisorId != null) {
            page = clienteResidencialRepository.obtenerEstadisticasPorSupervisorPaginado(supervisorId, fecha, pageable);
        } else {
            page = clienteResidencialRepository.obtenerEstadisticasPorFecha(fecha, pageable);
        }

        EstadisticaSedePaginadaResponse response = new EstadisticaSedePaginadaResponse(
                page.getContent(),
                page.getNumber(),
                page.getTotalPages(),
                page.getTotalElements(),
                page.getSize());

        return response;
    }

    @Override
    public EstadisticaSedePaginadaResponse obtenerEstadisticasPorSedePaginadoConBusqueda(Long sedeId, Long supervisorId,
                                                                                         LocalDate fecha, String busquedaVendedor,
                                                                                         Pageable pageable) {

        Page<EstadisticaSedeDTO> page;

        // Determinar qué método usar según los filtros
        if (sedeId != null && supervisorId != null) {
            page = clienteResidencialRepository.obtenerEstadisticasPorSedeYSupervisorConBusquedaPaginado(sedeId,
                    supervisorId,
                    fecha, busquedaVendedor, pageable);
        } else if (sedeId != null) {
            page = clienteResidencialRepository.obtenerEstadisticasPorSedeYFechaConBusquedaPaginado(sedeId, fecha,
                    busquedaVendedor, pageable);
        } else if (supervisorId != null) {
            page = clienteResidencialRepository.obtenerEstadisticasPorSupervisorConBusquedaPaginado(supervisorId, fecha,
                    busquedaVendedor, pageable);
        } else {
            page = clienteResidencialRepository.obtenerEstadisticasPorFechaConBusqueda(fecha, busquedaVendedor,
                    pageable);
        }

        EstadisticaSedePaginadaResponse response = new EstadisticaSedePaginadaResponse(
                page.getContent(),
                page.getNumber(),
                page.getTotalPages(),
                page.getTotalElements(),
                page.getSize());

        return response;
    }

    @Override
    public EstadisticaSedePaginadaResponse obtenerEstadisticasPorRangoPaginado(LocalDate fechaInicio,
                                                                               LocalDate fechaFin, Long sedeId, Pageable pageable) {
        // Para el rango, usamos el método sin paginación por ahora
        // Se puede implementar una versión paginada específica si es necesario
        List<EstadisticaSedeDTO> estadisticas = obtenerEstadisticasPorRango(fechaInicio, fechaFin, sedeId);

        // Simular paginación manual
        int page = pageable.getPageNumber();
        int size = pageable.getPageSize();
        int start = page * size;
        int end = Math.min(start + size, estadisticas.size());

        List<EstadisticaSedeDTO> pageContent = estadisticas.subList(start, end);
        int totalPages = (int) Math.ceil((double) estadisticas.size() / size);

        return new EstadisticaSedePaginadaResponse(
                pageContent,
                page,
                totalPages,
                estadisticas.size(),
                size);
    }

    @Override
    public Map<String, Object> obtenerLeadsPorAsesorYFecha(String nombreAsesor, LocalDate fecha, String numeroMovil,
                                                           Pageable pageable) {
        // Usar el método existente del repositorio para obtener clientes filtrados
        Page<ClienteConUsuarioDTO> pageClientes = clienteResidencialRepository.obtenerClientesConUsuarioFiltrados(
                null, // dniAsesor
                nombreAsesor, // nombreAsesor
                numeroMovil, // numeroMovil
                fecha, // fecha
                pageable);

        // Crear respuesta con formato similar al usado en ClienteResidencialService
        Map<String, Object> response = new HashMap<>();
        response.put("clientes", pageClientes.getContent());
        response.put("currentPage", pageClientes.getNumber());
        response.put("totalItems", pageClientes.getTotalElements());
        response.put("totalPages", pageClientes.getTotalPages());
        response.put("asesor", nombreAsesor);
        response.put("fecha", fecha.toString());

        return response;
    }

    @Override
    public Map<String, Object> obtenerLeadsPorAsesorYRangoFechas(String nombreAsesor, LocalDate fechaInicio,
                                                                 LocalDate fechaFin, String numeroMovil, Pageable pageable) {
        // Usar el método existente del repositorio para obtener clientes filtrados por
        // rango de fechas
        Page<ClienteConUsuarioDTO> pageClientes = clienteResidencialRepository
                .obtenerClientesConUsuarioFiltradosPorRango(
                        null, // dniAsesor
                        nombreAsesor, // nombreAsesor
                        numeroMovil, // numeroMovil
                        fechaInicio, // fechaInicio
                        fechaFin, // fechaFin
                        pageable);

        // Crear el resultado con la estructura esperada
        Map<String, Object> resultado = new HashMap<>();
        resultado.put("clientes", pageClientes.getContent());
        resultado.put("currentPage", pageClientes.getNumber());
        resultado.put("totalPages", pageClientes.getTotalPages());
        resultado.put("totalItems", pageClientes.getTotalElements());
        resultado.put("pageSize", pageClientes.getSize());
        resultado.put("hasNext", pageClientes.hasNext());
        resultado.put("hasPrevious", pageClientes.hasPrevious());

        return resultado;
    }

    @Override
    public List<Map<String, Object>> obtenerSupervisoresPorSede(Long sedeId) {
        // Obtener coordinadores (supervisores) de la sede específica
        List<User> coordinadores = userRepository.findBySedeIdAndRole(sedeId, Role.COORDINADOR);

        // Convertir a Map para el frontend
        List<Map<String, Object>> supervisores = coordinadores.stream()
                .map(coordinador -> {
                    Map<String, Object> supervisor = new HashMap<>();
                    supervisor.put("id", coordinador.getId());
                    supervisor.put("nombre", coordinador.getNombre() + " " + coordinador.getApellido());
                    supervisor.put("username", coordinador.getUsername());
                    return supervisor;
                })
                .collect(Collectors.toList());

        return supervisores;
    }

    @Autowired
    private ClienteResidencialExcelService clienteResidencialExcelService;

    @Override
    public byte[] exportarLeadsPorRango(Long sedeId, Long supervisorId, LocalDate fecha) {
        try {
            // Usar el método ULTRA-RÁPIDO que hace una sola consulta directa
            // Similar al método de rango de fechas que funciona muy rápido
            return clienteResidencialExcelService.generarExcelEstadisticasOptimizado(sedeId, supervisorId, fecha);

        } catch (Exception e) {
            throw new RuntimeException("Error al generar el archivo Excel optimizado: " + e.getMessage(), e);
        }
    }

    @Override
    public EstadisticaSedePaginadaResponse obtenerEstadisticasPorRangoFechas(Long sedeId, Long supervisorId,
                                                                             LocalDate fechaInicio, LocalDate fechaFin, Pageable pageable) {

        try {
            // Obtener estadísticas acumuladas por rango de fechas
            List<EstadisticaSedeDTO> estadisticasAcumuladas = obtenerEstadisticasAcumuladasPorRango(
                    sedeId, supervisorId, fechaInicio, fechaFin);

            // Aplicar paginación manual
            int totalElementos = estadisticasAcumuladas.size();
            int inicio = (int) pageable.getOffset();
            int fin = Math.min(inicio + pageable.getPageSize(), totalElementos);

            List<EstadisticaSedeDTO> estadisticasPaginadas = estadisticasAcumuladas.subList(inicio, fin);

            // Crear respuesta paginada usando el constructor existente
            EstadisticaSedePaginadaResponse response = new EstadisticaSedePaginadaResponse(
                    estadisticasPaginadas,
                    pageable.getPageNumber(),
                    (int) Math.ceil((double) totalElementos / pageable.getPageSize()),
                    totalElementos,
                    pageable.getPageSize());

            return response;

        } catch (Exception e) {
            throw new RuntimeException("Error al obtener estadísticas por rango de fechas: " + e.getMessage(), e);
        }
    }

    @Override
    public EstadisticaSedePaginadaResponse obtenerEstadisticasPorRangoFechasConBusqueda(Long sedeId, Long supervisorId,
                                                                                        LocalDate fechaInicio, LocalDate fechaFin, String busquedaVendedor, Pageable pageable) {

        try {
            // Obtener estadísticas acumuladas por rango de fechas con búsqueda
            List<EstadisticaSedeDTO> estadisticasAcumuladas = obtenerEstadisticasAcumuladasPorRangoConBusqueda(
                    sedeId, supervisorId, fechaInicio, fechaFin, busquedaVendedor);

            // Aplicar paginación manual
            int totalElementos = estadisticasAcumuladas.size();
            int inicio = (int) pageable.getOffset();
            int fin = Math.min(inicio + pageable.getPageSize(), totalElementos);

            List<EstadisticaSedeDTO> estadisticasPaginadas = estadisticasAcumuladas.subList(inicio, fin);

            // Crear respuesta paginada usando el constructor existente
            EstadisticaSedePaginadaResponse response = new EstadisticaSedePaginadaResponse(
                    estadisticasPaginadas,
                    pageable.getPageNumber(),
                    (int) Math.ceil((double) totalElementos / pageable.getPageSize()),
                    totalElementos,
                    pageable.getPageSize());

            return response;

        } catch (Exception e) {
            throw new RuntimeException(
                    "Error al obtener estadísticas por rango de fechas con búsqueda: " + e.getMessage(), e);
        }
    }

    /**
     * Método auxiliar para obtener estadísticas acumuladas por rango de fechas
     * OPTIMIZADO: Usa consultas SQL directas en lugar de acumulación manual
     * CORREGIDO: Ahora respeta el filtro de supervisorId
     */
    private List<EstadisticaSedeDTO> obtenerEstadisticasAcumuladasPorRango(Long sedeId, Long supervisorId,
                                                                           LocalDate fechaInicio, LocalDate fechaFin) {

        // Usar las consultas SQL optimizadas que respetan TODOS los filtros
        List<EstadisticaSedeDTO> estadisticas;

        if (sedeId != null && supervisorId != null) {
            // Filtro por sede Y supervisor específicos
            estadisticas = clienteResidencialRepository.obtenerEstadisticasPorSedeYSupervisorYRango(
                    sedeId, supervisorId, fechaInicio, fechaFin);
        } else if (sedeId != null) {
            // Solo filtro por sede
            estadisticas = clienteResidencialRepository.obtenerEstadisticasPorSedeYRango(sedeId, fechaInicio, fechaFin);
        } else if (supervisorId != null) {
            // Solo filtro por supervisor
            estadisticas = clienteResidencialRepository.obtenerEstadisticasPorSupervisorYRango(
                    supervisorId, fechaInicio, fechaFin);
        } else {
            // Sin filtros específicos
            estadisticas = clienteResidencialRepository.obtenerEstadisticasPorRango(fechaInicio, fechaFin);
        }

        return estadisticas != null ? estadisticas : new ArrayList<>();
    }

    /**
     * Método auxiliar para obtener estadísticas de una fecha específica
     */
    private List<EstadisticaSedeDTO> obtenerEstadisticasPorFecha(Long sedeId, Long supervisorId, LocalDate fecha) {
        List<EstadisticaSedeDTO> estadisticas;

        // Usar los métodos existentes del repositorio
        if (sedeId != null) {
            estadisticas = clienteResidencialRepository.obtenerEstadisticasPorSedeYFecha(sedeId, fecha);
        } else {
            estadisticas = clienteResidencialRepository.obtenerEstadisticasPorFechaSinPaginacion(fecha);
        }

        // Si hay filtro de supervisor, filtrar los resultados
        if (supervisorId != null && estadisticas != null) {
            // Por ahora, simplemente logueamos que se aplicaría el filtro
            // El filtro de supervisor se manejará en el frontend o en consultas más
            // específicas
        }

        return estadisticas != null ? estadisticas : new ArrayList<>();
    }

    /**
     * Método auxiliar para obtener estadísticas acumuladas por rango de fechas con
     * búsqueda
     * OPTIMIZADO: Usa consultas SQL directas con filtro de búsqueda
     * CORREGIDO: Ahora respeta el filtro de supervisorId
     */
    private List<EstadisticaSedeDTO> obtenerEstadisticasAcumuladasPorRangoConBusqueda(Long sedeId, Long supervisorId,
                                                                                      LocalDate fechaInicio, LocalDate fechaFin, String busquedaVendedor) {

        // Usar las consultas SQL optimizadas con búsqueda que respetan TODOS los
        // filtros
        List<EstadisticaSedeDTO> estadisticas;

        if (sedeId != null && supervisorId != null) {
            // Filtro por sede Y supervisor específicos con búsqueda
            estadisticas = clienteResidencialRepository.obtenerEstadisticasPorSedeYSupervisorYRangoConBusqueda(
                    sedeId, supervisorId, fechaInicio, fechaFin, busquedaVendedor);
        } else if (sedeId != null) {
            // Solo filtro por sede con búsqueda
            estadisticas = clienteResidencialRepository.obtenerEstadisticasPorSedeYRangoConBusqueda(sedeId, fechaInicio,
                    fechaFin, busquedaVendedor);
        } else if (supervisorId != null) {
            // Solo filtro por supervisor con búsqueda
            estadisticas = clienteResidencialRepository.obtenerEstadisticasPorSupervisorYRangoConBusqueda(
                    supervisorId, fechaInicio, fechaFin, busquedaVendedor);
        } else {
            // Sin filtros específicos, solo búsqueda
            estadisticas = clienteResidencialRepository.obtenerEstadisticasPorRangoConBusqueda(fechaInicio, fechaFin,
                    busquedaVendedor);
        }

        return estadisticas != null ? estadisticas : new ArrayList<>();
    }

    @Override
    public byte[] exportarLeadsPorRangoFechas(Long sedeId, Long supervisorId, LocalDate fechaInicio,
                                              LocalDate fechaFin) {
        try {
            // Usar el método ULTRA-RÁPIDO de rango de fechas que ya existe y funciona
            // perfecto
            // Aplicando filtros de sede y supervisor
            return clienteResidencialExcelService.generarExcelEstadisticasPorRangoFechas(sedeId, supervisorId,
                    fechaInicio, fechaFin);

        } catch (Exception e) {
            throw new RuntimeException("Error al generar el archivo Excel por rango de fechas: " + e.getMessage(), e);
        }
    }

    @Override
    public byte[] exportarLeadsFiltrados(Long sedeId, Long supervisorId, LocalDate fecha,
                                         LocalDate fechaInicio, LocalDate fechaFin, String busquedaVendedor) {
        try {
            // Determinar si es exportación por fecha específica o por rango
            if (fecha != null) {
                // Exportación por fecha específica con filtro de vendedor
                return clienteResidencialExcelService.generarExcelEstadisticasFiltrado(
                        sedeId, supervisorId, fecha, null, null, busquedaVendedor);
            } else if (fechaInicio != null && fechaFin != null) {
                // Exportación por rango de fechas con filtro de vendedor
                return clienteResidencialExcelService.generarExcelEstadisticasFiltrado(
                        sedeId, supervisorId, null, fechaInicio, fechaFin, busquedaVendedor);
            } else {
                throw new IllegalArgumentException("Debe proporcionar una fecha específica o un rango de fechas");
            }

        } catch (Exception e) {
            throw new RuntimeException("Error al generar el archivo Excel filtrado: " + e.getMessage(), e);
        }
    }

    @Override
    public List<Map<String, Object>> obtenerRendimientoLeadsPorAsesor(String periodo, Long sedeId, Long supervisorId,
                                                                      LocalDate fecha) {
        try {
            // Calcular fechas según el período
            LocalDate fechaInicio;
            LocalDate fechaFin;

            switch (periodo.toLowerCase()) {
                case "diario":
                    fechaInicio = fecha;
                    fechaFin = fecha;
                    break;
                case "semanal":
                    // Obtener el lunes de la semana de la fecha dada
                    fechaInicio = fecha.with(java.time.DayOfWeek.MONDAY);
                    fechaFin = fechaInicio.plusDays(6); // Domingo
                    break;
                case "mensual":
                    // Primer y último día del mes
                    fechaInicio = fecha.withDayOfMonth(1);
                    fechaFin = fecha.withDayOfMonth(fecha.lengthOfMonth());
                    break;
                default:
                    throw new IllegalArgumentException("Período inválido: " + periodo);
            }

            // Obtener estadísticas para el rango calculado
            List<EstadisticaSedeDTO> estadisticas;
            if (sedeId != null) {
                estadisticas = clienteResidencialRepository.obtenerEstadisticasPorSedeYRango(sedeId, fechaInicio,
                        fechaFin);
            } else {
                estadisticas = clienteResidencialRepository.obtenerEstadisticasPorRango(fechaInicio, fechaFin);
            }

            // Filtrar por supervisor si se especifica
            if (supervisorId != null && estadisticas != null) {
                // Aquí se podría implementar el filtro por supervisor
                // Por ahora, mantenemos todos los datos
            }

            // Convertir a formato de respuesta para el frontend
            List<Map<String, Object>> resultado = new ArrayList<>();

            if (estadisticas != null) {
                for (EstadisticaSedeDTO estadistica : estadisticas) {
                    Map<String, Object> item = new HashMap<>();
                    item.put("nombreAsesor", estadistica.getVendedor());
                    item.put("sede", estadistica.getSede());
                    item.put("supervisor", estadistica.getSupervisor());
                    item.put("totalLeads", estadistica.getTomaDatos());
                    item.put("periodo", periodo);
                    item.put("fechaInicio", fechaInicio.toString());
                    item.put("fechaFin", fechaFin.toString());

                    resultado.add(item);
                }
            }

            // Ordenar por total de leads descendente
            resultado.sort((a, b) -> {
                Integer leadsA = (Integer) a.get("totalLeads");
                Integer leadsB = (Integer) b.get("totalLeads");
                return leadsB.compareTo(leadsA);
            });

            return resultado;

        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    // ===== IMPLEMENTACIÓN DE MÉTODOS PARA ESTADÍSTICAS DE TRANSCRIPCIONES (CORREGIDO) =====

    @Override
    public List<EstadisticaTranscripcionCoordinadorDTO> obtenerEstadisticasTranscripcionPorCoordinador() {
        try {
            List<Object[]> resultados = clienteResidencialRepository.obtenerEstadisticasTranscripcionPorCoordinador();
            return convertirResultadosADTO(resultados);
        } catch (Exception e) {
            throw new RuntimeException("Error al obtener estadísticas de transcripciones por coordinador: " + e.getMessage(), e);
        }
    }

    @Override
    public List<EstadisticaTranscripcionCoordinadorDTO> obtenerEstadisticasTranscripcionPorCoordinadorYSede(Long sedeId) {
        try {
            List<Object[]> resultados = clienteResidencialRepository.obtenerEstadisticasTranscripcionPorCoordinadorYSede(sedeId);
            return convertirResultadosADTO(resultados);
        } catch (Exception e) {
            throw new RuntimeException("Error al obtener estadísticas de transcripciones por coordinador y sede: " + e.getMessage(), e);
        }
    }

    @Override
    public List<EstadisticaTranscripcionCoordinadorDTO> obtenerEstadisticasTranscripcionPorCoordinadorYFecha(LocalDate fecha) {
        try {
            List<Object[]> resultados = clienteResidencialRepository.obtenerEstadisticasTranscripcionPorCoordinadorYFecha(fecha);
            return convertirResultadosADTO(resultados);
        } catch (Exception e) {
            throw new RuntimeException("Error al obtener estadísticas de transcripciones por coordinador y fecha: " + e.getMessage(), e);
        }
    }

    @Override
    public List<EstadisticaTranscripcionCoordinadorDTO> obtenerEstadisticasTranscripcionPorCoordinadorSedeYFecha(Long sedeId, LocalDate fecha) {
        try {
            List<Object[]> resultados = clienteResidencialRepository.obtenerEstadisticasTranscripcionPorCoordinadorSedeYFecha(sedeId, fecha);
            return convertirResultadosADTO(resultados);
        } catch (Exception e) {
            throw new RuntimeException("Error al obtener estadísticas de transcripciones por coordinador, sede y fecha: " + e.getMessage(), e);
        }
    }

    @Override
    public List<EstadisticaTranscripcionCoordinadorDTO> obtenerEstadisticasTranscripcionPorCoordinadorYRangoFechas(LocalDate fechaInicio, LocalDate fechaFin) {
        try {
            List<Object[]> resultados = clienteResidencialRepository.obtenerEstadisticasTranscripcionPorCoordinadorYRangoFechas(fechaInicio, fechaFin);
            return convertirResultadosADTO(resultados);
        } catch (Exception e) {
            throw new RuntimeException("Error al obtener estadísticas de transcripciones por coordinador y rango de fechas: " + e.getMessage(), e);
        }
    }

    @Override
    public List<EstadisticaTranscripcionCoordinadorDTO> obtenerEstadisticasTranscripcionPorCoordinadorSedeYRangoFechas(Long sedeId, LocalDate fechaInicio, LocalDate fechaFin) {
        try {
            List<Object[]> resultados = clienteResidencialRepository.obtenerEstadisticasTranscripcionPorCoordinadorSedeYRangoFechas(sedeId, fechaInicio, fechaFin);
            return convertirResultadosADTO(resultados);
        } catch (Exception e) {
            throw new RuntimeException("Error al obtener estadísticas de transcripciones por coordinador, sede y rango de fechas: " + e.getMessage(), e);
        }
    }

    // Asegúrate de que este método auxiliar (que ya corregimos) esté en la misma clase
    private List<EstadisticaTranscripcionCoordinadorDTO> convertirResultadosADTO(List<Object[]> resultados) {
        if (resultados == null) {
            return new ArrayList<>();
        }
        return resultados.stream()
                .map(resultado -> {
                    String sede = (String) resultado[0];
                    String coordinador = (String) resultado[1];
                    Long coordinadorId = ((Number) resultado[2]).longValue();
                    long leadsContacto = ((Number) resultado[3]).longValue();
                    long leadsRegistrado = ((Number) resultado[4]).longValue();
                    long bienRegistrado = ((Number) resultado[5]).longValue();
                    long malRegistrado = ((Number) resultado[6]).longValue();
                    long noRegistrado = ((Number) resultado[7]).longValue();

                    // Lógica de cálculo de eficiencia
                    double eficiencia = (leadsContacto > 0)
                            ? ((double) leadsRegistrado / leadsContacto) * 100.0
                            : 0.0;

                    // Llama al constructor del DTO que incluye la eficiencia
                    return new EstadisticaTranscripcionCoordinadorDTO(
                            coordinadorId, sede, coordinador,
                            leadsContacto, leadsRegistrado, bienRegistrado,
                            malRegistrado, noRegistrado, eficiencia
                    );
                })
                .collect(Collectors.toList());
    }
    // ===== IMPLEMENTACIÓN DE MÉTODOS PARA ESTADÍSTICAS DE TRANSCRIPCIONES POR ASESOR =====

    @Override
    public List<EstadisticaTranscripcionAsesorDTO> obtenerEstadisticasTranscripcionPorAsesor(
            Long coordinadorId, Long sedeId, LocalDate fecha, LocalDate fechaInicio, LocalDate fechaFin) {
        try {
            // Determinar qué método usar según los filtros proporcionados
            if (fechaInicio != null && fechaFin != null) {
                // Rango de fechas
                if (sedeId != null) {
                    return clienteResidencialRepository.obtenerEstadisticasTranscripcionPorAsesorCoordinadorSedeYRangoFechas(
                            coordinadorId, sedeId, fechaInicio, fechaFin);
                } else {
                    return clienteResidencialRepository.obtenerEstadisticasTranscripcionPorAsesorCoordinadorYRangoFechas(
                            coordinadorId, fechaInicio, fechaFin);
                }
            } else if (fecha != null) {
                // Fecha específica
                if (sedeId != null) {
                    return clienteResidencialRepository.obtenerEstadisticasTranscripcionPorAsesorCoordinadorSedeYFecha(
                            coordinadorId, sedeId, fecha);
                } else {
                    return clienteResidencialRepository.obtenerEstadisticasTranscripcionPorAsesorCoordinadorYFecha(
                            coordinadorId, fecha);
                }
            } else {
                // Sin filtro de fecha
                if (sedeId != null) {
                    return clienteResidencialRepository.obtenerEstadisticasTranscripcionPorAsesorCoordinadorYSede(
                            coordinadorId, sedeId);
                } else {
                    return clienteResidencialRepository.obtenerEstadisticasTranscripcionPorAsesorYCoordinador(
                            coordinadorId);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException("Error al obtener estadísticas de transcripciones por asesor: " + e.getMessage(), e);
        }
    }

    // ===== IMPLEMENTACIÓN DE MÉTODOS PARA LEADS FILTRADOS POR COORDINADOR Y TIPO DE INTERÉS =====

    @Override
    public Map<String, Object> obtenerLeadsPorCoordinadorYTipoInteres(
            String nombreCoordinador, String tipoInteres, LocalDate fecha,
            Long sedeId, String numeroMovil, Pageable pageable) {

        try {
            // Determinar el campo booleano según el tipo de interés
            String campoInteres = obtenerCampoInteresPorTipo(tipoInteres);

            // Obtener leads filtrados por coordinador y tipo de interés
            Page<ClienteConUsuarioDTO> pageClientes = clienteResidencialRepository
                    .obtenerLeadsPorCoordinadorYTipoInteres(
                            nombreCoordinador, campoInteres, fecha, sedeId, numeroMovil, pageable);

            // Crear respuesta con formato estándar
            Map<String, Object> response = new HashMap<>();
            response.put("leads", pageClientes.getContent());
            response.put("currentPage", pageClientes.getNumber());
            response.put("totalPages", pageClientes.getTotalPages());
            response.put("totalElements", pageClientes.getTotalElements());
            response.put("pageSize", pageClientes.getSize());
            response.put("hasNext", pageClientes.hasNext());
            response.put("hasPrevious", pageClientes.hasPrevious());
            response.put("coordinador", nombreCoordinador);
            response.put("tipoInteres", tipoInteres);
            response.put("fecha", fecha.toString());

            return response;

        } catch (Exception e) {
            throw new RuntimeException("Error al obtener leads por coordinador y tipo de interés: " + e.getMessage(), e);
        }
    }

    @Override
    public Map<String, Object> obtenerLeadsPorCoordinadorYTipoInteresRango(
            String nombreCoordinador, String tipoInteres, LocalDate fechaInicio, LocalDate fechaFin,
            Long sedeId, String numeroMovil, Pageable pageable) {

        try {
            // Determinar el campo booleano según el tipo de interés
            String campoInteres = obtenerCampoInteresPorTipo(tipoInteres);

            // Obtener leads filtrados por coordinador y tipo de interés en rango de fechas
            Page<ClienteConUsuarioDTO> pageClientes = clienteResidencialRepository
                    .obtenerLeadsPorCoordinadorYTipoInteresRango(
                            nombreCoordinador, campoInteres, fechaInicio, fechaFin, sedeId, numeroMovil, pageable);

            // Crear respuesta con formato estándar
            Map<String, Object> response = new HashMap<>();
            response.put("leads", pageClientes.getContent());
            response.put("currentPage", pageClientes.getNumber());
            response.put("totalPages", pageClientes.getTotalPages());
            response.put("totalElements", pageClientes.getTotalElements());
            response.put("pageSize", pageClientes.getSize());
            response.put("hasNext", pageClientes.hasNext());
            response.put("hasPrevious", pageClientes.hasPrevious());
            response.put("coordinador", nombreCoordinador);
            response.put("tipoInteres", tipoInteres);
            response.put("fechaInicio", fechaInicio.toString());
            response.put("fechaFin", fechaFin.toString());

            return response;

        } catch (Exception e) {
            throw new RuntimeException("Error al obtener leads por coordinador y tipo de interés en rango: " + e.getMessage(), e);
        }
    }

    /**
     * Método auxiliar para obtener el nombre del campo de interés según el tipo
     *
     * @param tipoInteres Tipo de interés: 'seguros', 'energia', 'lowi'
     * @return Nombre del campo booleano correspondiente
     */
    private String obtenerCampoInteresPorTipo(String tipoInteres) {
        switch (tipoInteres.toLowerCase()) {
            case "seguros":
                return "interesadosSeguro";
            case "energia":
                return "interesadosEnergia";
            case "lowi":
                return "interesadosLowi";
            default:
                throw new IllegalArgumentException("Tipo de interés inválido: " + tipoInteres);
        }
    }

    @Override
    public byte[] exportarLeadsCoordinador(String coordinador, String tipoInteres, LocalDate fecha,
                                           LocalDate fechaFin, Long sedeId, String busquedaMovil) {
        try {
            // Usar el servicio de Excel existente para generar el archivo
            // Crear parámetros para el filtro específico de coordinador

            // Si es rango de fechas
            if (fechaFin != null && !fecha.equals(fechaFin)) {
                return clienteResidencialExcelService.generarExcelLeadsCoordinadorRango(
                        coordinador, tipoInteres, fecha, fechaFin, sedeId, busquedaMovil);
            } else {
                // Si es fecha específica
                return clienteResidencialExcelService.generarExcelLeadsCoordinadorFecha(
                        coordinador, tipoInteres, fecha, sedeId, busquedaMovil);
            }

        } catch (Exception e) {
            throw new RuntimeException("Error al generar el archivo Excel de leads del coordinador: " + e.getMessage(), e);
        }
    }

    // ===== IMPLEMENTACIÓN DE NUEVOS MÉTODOS CON LÓGICA DE AUDIOS HUÉRFANOS =====

    @Override
    public List<EstadisticaTranscripcionCoordinadorDTO> obtenerEstadisticasTranscripcionConAudiosHuerfanos() {
        try {
            List<Object[]> resultados = clienteResidencialRepository.obtenerEstadisticasTranscripcionConAudiosHuerfanos();
            return convertirResultadosADTO(resultados);
        } catch (Exception e) {
            throw new RuntimeException("Error al obtener estadísticas de transcripciones con audios huérfanos: " + e.getMessage(), e);
        }
    }

    @Override
    public List<EstadisticaTranscripcionCoordinadorDTO> obtenerEstadisticasTranscripcionConAudiosHuerfanosPorSede(Long sedeId) {
        try {
            List<Object[]> resultados = clienteResidencialRepository.obtenerEstadisticasTranscripcionConAudiosHuerfanosPorSede(sedeId);
            return convertirResultadosADTO(resultados);
        } catch (Exception e) {
            throw new RuntimeException("Error al obtener estadísticas de transcripciones con audios huérfanos por sede: " + e.getMessage(), e);
        }
    }

    @Override
    public List<EstadisticaTranscripcionCoordinadorDTO> obtenerEstadisticasTranscripcionConAudiosHuerfanosPorFecha(LocalDate fecha) {
        try {
            List<Object[]> resultados = clienteResidencialRepository.obtenerEstadisticasTranscripcionConAudiosHuerfanosPorFecha(fecha);
            return convertirResultadosADTO(resultados);
        } catch (Exception e) {
            throw new RuntimeException("Error al obtener estadísticas de transcripciones con audios huérfanos por fecha: " + e.getMessage(), e);
        }
    }

    @Override
    public List<EstadisticaTranscripcionCoordinadorDTO> obtenerEstadisticasTranscripcionConAudiosHuerfanosPorSedeYFecha(Long sedeId, LocalDate fecha) {
        try {
            List<Object[]> resultados = clienteResidencialRepository.obtenerEstadisticasTranscripcionConAudiosHuerfanosPorSedeYFecha(sedeId, fecha);
            return convertirResultadosADTO(resultados);
        } catch (Exception e) {
            throw new RuntimeException("Error al obtener estadísticas de transcripciones con audios huérfanos por sede y fecha: " + e.getMessage(), e);
        }
    }

    @Override
    public List<EstadisticaTranscripcionCoordinadorDTO> obtenerEstadisticasTranscripcionConAudiosHuerfanosPorRangoFechas(LocalDate fechaInicio, LocalDate fechaFin) {
        try {
            List<Object[]> resultados = clienteResidencialRepository.obtenerEstadisticasTranscripcionConAudiosHuerfanosPorRangoFechas(fechaInicio, fechaFin);
            return convertirResultadosADTO(resultados);
        } catch (Exception e) {
            throw new RuntimeException("Error al obtener estadísticas de transcripciones con audios huérfanos por rango de fechas: " + e.getMessage(), e);
        }
    }

    @Override
    public List<EstadisticaTranscripcionCoordinadorDTO> obtenerEstadisticasTranscripcionConAudiosHuerfanosPorSedeYRangoFechas(Long sedeId, LocalDate fechaInicio, LocalDate fechaFin) {
        try {
            List<Object[]> resultados = clienteResidencialRepository.obtenerEstadisticasTranscripcionConAudiosHuerfanosPorSedeYRangoFechas(sedeId, fechaInicio, fechaFin);
            return convertirResultadosADTO(resultados);
        } catch (Exception e) {
            throw new RuntimeException("Error al obtener estadísticas de transcripciones con audios huérfanos por sede y rango de fechas: " + e.getMessage(), e);
        }
    }

    @Override
    public String obtenerNombreAsesorPorAgenteYFecha(String agente, LocalDate fecha) {
        if (agente == null || agente.trim().isEmpty() || fecha == null) {
            return null;
        }

        // We only need the first result to identify the user for that day
        Pageable limit = PageRequest.of(0, 1);

        List<User> usuarios = clienteResidencialRepository.findTopUsuarioByNumeroAgenteAndFechaCreacion(agente, fecha, limit);

        if (!usuarios.isEmpty() && usuarios.get(0) != null) {
            User asesor = usuarios.get(0);
            // Return the full name of the found advisor
            return asesor.getNombre() + " " + asesor.getApellido();
        }

        // Return null if no advisor was found for that agent on that day
        return null;
    }

    // ===== IMPLEMENTACIÓN DE NUEVOS MÉTODOS PARA ASESORES CON LÓGICA DE AUDIOS HUÉRFANOS =====

    @Override
    public List<EstadisticaTranscripcionAsesorDTO> obtenerEstadisticasTranscripcionAsesorConAudiosHuerfanos() {
        try {
            List<Object[]> resultados = clienteResidencialRepository.obtenerEstadisticasTranscripcionAsesorConAudiosHuerfanos();
            return convertirResultadosAsesorADTO(resultados);
        } catch (Exception e) {
            throw new RuntimeException("Error al obtener estadísticas de transcripciones de asesor con audios huérfanos: " + e.getMessage(), e);
        }
    }

    @Override
    public List<EstadisticaTranscripcionAsesorDTO> obtenerEstadisticasTranscripcionAsesorConAudiosHuerfanosPorSede(Long sedeId) {
        try {
            List<Object[]> resultados = clienteResidencialRepository.obtenerEstadisticasTranscripcionAsesorConAudiosHuerfanosPorSede(sedeId);
            return convertirResultadosAsesorADTO(resultados);
        } catch (Exception e) {
            throw new RuntimeException("Error al obtener estadísticas de transcripciones de asesor con audios huérfanos por sede: " + e.getMessage(), e);
        }
    }

    @Override
    public List<EstadisticaTranscripcionAsesorDTO> obtenerEstadisticasTranscripcionAsesorConAudiosHuerfanosPorFecha(LocalDate fecha) {
        try {
            List<Object[]> resultados = clienteResidencialRepository.obtenerEstadisticasTranscripcionAsesorConAudiosHuerfanosPorFecha(fecha);
            return convertirResultadosAsesorADTO(resultados);
        } catch (Exception e) {
            throw new RuntimeException("Error al obtener estadísticas de transcripciones de asesor con audios huérfanos por fecha: " + e.getMessage(), e);
        }
    }

    @Override
    public List<EstadisticaTranscripcionAsesorDTO> obtenerEstadisticasTranscripcionAsesorConAudiosHuerfanosPorSedeYFecha(Long sedeId, LocalDate fecha) {
        try {
            List<Object[]> resultados = clienteResidencialRepository.obtenerEstadisticasTranscripcionAsesorConAudiosHuerfanosPorSedeYFecha(sedeId, fecha);
            return convertirResultadosAsesorADTO(resultados);
        } catch (Exception e) {
            throw new RuntimeException("Error al obtener estadísticas de transcripciones de asesor con audios huérfanos por sede y fecha: " + e.getMessage(), e);
        }
    }

    @Override
    public List<EstadisticaTranscripcionAsesorDTO> obtenerEstadisticasTranscripcionAsesorConAudiosHuerfanosPorRangoFechas(LocalDate fechaInicio, LocalDate fechaFin) {
        try {
            List<Object[]> resultados = clienteResidencialRepository.obtenerEstadisticasTranscripcionAsesorConAudiosHuerfanosPorRangoFechas(fechaInicio, fechaFin);
            return convertirResultadosAsesorADTO(resultados);
        } catch (Exception e) {
            throw new RuntimeException("Error al obtener estadísticas de transcripciones de asesor con audios huérfanos por rango de fechas: " + e.getMessage(), e);
        }
    }

    @Override
    public List<EstadisticaTranscripcionAsesorDTO> obtenerEstadisticasTranscripcionAsesorConAudiosHuerfanosPorSedeYRangoFechas(Long sedeId, LocalDate fechaInicio, LocalDate fechaFin) {
        try {
            List<Object[]> resultados = clienteResidencialRepository.obtenerEstadisticasTranscripcionAsesorConAudiosHuerfanosPorSedeYRangoFechas(sedeId, fechaInicio, fechaFin);
            return convertirResultadosAsesorADTO(resultados);
        } catch (Exception e) {
            throw new RuntimeException("Error al obtener estadísticas de transcripciones de asesor con audios huérfanos por sede y rango de fechas: " + e.getMessage(), e);
        }
    }

    /**
     * Convierte los resultados de Object[] a EstadisticaTranscripcionAsesorDTO
     * Mapea los resultados de las consultas nativas a DTOs para asesores
     *
     * @param resultados Lista de Object[] con los datos de la consulta
     * @return Lista de EstadisticaTranscripcionAsesorDTO
     */
    private List<EstadisticaTranscripcionAsesorDTO> convertirResultadosAsesorADTO(List<Object[]> resultados) {
        return resultados.stream()
                .map(resultado -> {
                    String nombreComercial = (String) resultado[0];
                    Long leadsContacto = ((Number) resultado[1]).longValue();
                    Long leadsRegistrado = ((Number) resultado[2]).longValue();
                    Long bienRegistrado = ((Number) resultado[3]).longValue();
                    Long malRegistrado = ((Number) resultado[4]).longValue();
                    Long noRegistrado = ((Number) resultado[5]).longValue();

                    return new EstadisticaTranscripcionAsesorDTO(
                            nombreComercial,
                            leadsContacto,
                            leadsRegistrado,
                            bienRegistrado,
                            malRegistrado,
                            noRegistrado
                    );
                })
                .collect(Collectors.toList());
    }

    // ===== IMPLEMENTACIÓN DE MÉTODOS PARA OBTENER DETALLES DE MÉTRICAS CON AUDIOS HUÉRFANOS =====

    @Override
    public Map<String, Object> obtenerLeadsBienRegistradosPorCoordinador(
            String nombreCoordinador, Long sedeId, LocalDate fecha,
            LocalDate fechaInicio, LocalDate fechaFin, Pageable pageable) {
        try {
            Page<ClienteConUsuarioDTO> pageLeads = clienteResidencialRepository
                    .obtenerLeadsBienRegistradosPorCoordinador(nombreCoordinador, sedeId, fecha, fechaInicio, fechaFin, pageable);

            Map<String, Object> response = new HashMap<>();
            response.put("leads", pageLeads.getContent());
            response.put("currentPage", pageLeads.getNumber());
            response.put("totalPages", pageLeads.getTotalPages());
            response.put("totalElements", pageLeads.getTotalElements());
            response.put("pageSize", pageLeads.getSize());
            response.put("hasNext", pageLeads.hasNext());
            response.put("hasPrevious", pageLeads.hasPrevious());
            response.put("coordinador", nombreCoordinador);
            response.put("tipoMetrica", "BIEN_REGISTRADO");

            return response;
        } catch (Exception e) {
            throw new RuntimeException("Error al obtener leads bien registrados por coordinador: " + e.getMessage(), e);
        }
    }

    @Override
    public Map<String, Object> obtenerLeadsMalRegistradosPorCoordinador(
            String nombreCoordinador, Long sedeId, LocalDate fecha,
            LocalDate fechaInicio, LocalDate fechaFin, Pageable pageable) {
        try {
            Page<ClienteConUsuarioDTO> pageLeads = clienteResidencialRepository
                    .obtenerLeadsMalRegistradosPorCoordinador(nombreCoordinador, sedeId, fecha, fechaInicio, fechaFin, pageable);

            Map<String, Object> response = new HashMap<>();
            response.put("leads", pageLeads.getContent());
            response.put("currentPage", pageLeads.getNumber());
            response.put("totalPages", pageLeads.getTotalPages());
            response.put("totalElements", pageLeads.getTotalElements());
            response.put("pageSize", pageLeads.getSize());
            response.put("hasNext", pageLeads.hasNext());
            response.put("hasPrevious", pageLeads.hasPrevious());
            response.put("coordinador", nombreCoordinador);
            response.put("tipoMetrica", "MAL_REGISTRADO");

            return response;
        } catch (Exception e) {
            throw new RuntimeException("Error al obtener leads mal registrados por coordinador: " + e.getMessage(), e);
        }
    }

    @Override
    public Map<String, Object> obtenerLeadsRegistradosPorCoordinador(
            String nombreCoordinador, Long sedeId, LocalDate fecha,
            LocalDate fechaInicio, LocalDate fechaFin, Pageable pageable) {
        try {
            Page<ClienteConUsuarioDTO> pageLeads = clienteResidencialRepository
                    .obtenerLeadsRegistradosPorCoordinador(nombreCoordinador, sedeId, fecha, fechaInicio, fechaFin, pageable);

            Map<String, Object> response = new HashMap<>();
            response.put("leads", pageLeads.getContent());
            response.put("currentPage", pageLeads.getNumber());
            response.put("totalPages", pageLeads.getTotalPages());
            response.put("totalElements", pageLeads.getTotalElements());
            response.put("pageSize", pageLeads.getSize());
            response.put("hasNext", pageLeads.hasNext());
            response.put("hasPrevious", pageLeads.hasPrevious());
            response.put("coordinador", nombreCoordinador);
            response.put("tipoMetrica", "LEADS_REGISTRADO");

            return response;
        } catch (Exception e) {
            throw new RuntimeException("Error al obtener leads registrados por coordinador: " + e.getMessage(), e);
        }
    }

    // Archivo: EstadisticasSedeServiceImpl.java
// Reemplaza el método obtenerAudiosHuerfanosPorCoordinador

    @Override
    public Map<String, Object> obtenerAudiosHuerfanosPorCoordinador(
            String nombreCoordinador, Long sedeId, LocalDate fecha,
            LocalDate fechaInicio, LocalDate fechaFin, Pageable pageable) {
        try {
            Page<Object[]> pageAudios = clienteResidencialRepository
                    .obtenerAudiosHuerfanosPorCoordinador(nombreCoordinador, sedeId, fecha, fechaInicio, fechaFin, pageable);

            // Convertir Object[] a un DTO más manejable y claro
            List<Map<String, Object>> audiosConvertidos = pageAudios.getContent().stream()
                    .map(record -> {
                        Map<String, Object> item = new HashMap<>();
                        item.put("agente", "Agente: " + (record[0] != null ? record[0].toString() : "N/A"));
                        item.put("nombreAsesor", record[1]); // Nombre del asesor que usó el agente
                        item.put("nombreCoordinador", record[4]); // Nombre del coordinador del asesor

                        LocalDateTime fechaCreacion = null;
                        if (record[2] instanceof java.sql.Timestamp) {
                            fechaCreacion = ((java.sql.Timestamp) record[2]).toLocalDateTime();
                        } else if (record[2] instanceof LocalDateTime) {
                            fechaCreacion = (LocalDateTime) record[2];
                        }
                        item.put("fechaCreacion", fechaCreacion);
                        item.put("movilContacto", record[3]);
                        item.put("nombreArchivoOriginal", record[5]);

                        // El lead identificador ahora incluye información del asesor y coordinador
                        Map<String, Object> leadIdentificador = new HashMap<>();
                        leadIdentificador.put("id", record[6]);
                        leadIdentificador.put("nombreAsesor", record[7]); // Nombre del asesor en el lead de referencia
                        leadIdentificador.put("nombreCoordinador", record[8]); // Nombre del coordinador en el lead de referencia
                        leadIdentificador.put("telefono", record[9]);
                        LocalDateTime fechaLeadRef = null;
                        if (record[10] instanceof java.sql.Timestamp) {
                            fechaLeadRef = ((java.sql.Timestamp) record[10]).toLocalDateTime();
                        } else if (record[10] instanceof LocalDateTime) {
                            fechaLeadRef = (LocalDateTime) record[10];
                        }
                        leadIdentificador.put("fechaCreacion", fechaLeadRef);
                        item.put("leadIdentificador", leadIdentificador);

                        return item;
                    })
                    .collect(Collectors.toList());

            Map<String, Object> response = new HashMap<>();
            response.put("leads", audiosConvertidos);
            response.put("currentPage", pageAudios.getNumber());
            response.put("totalPages", pageAudios.getTotalPages());
            response.put("totalElements", pageAudios.getTotalElements());
            response.put("pageSize", pageAudios.getSize());
            response.put("hasNext", pageAudios.hasNext());
            response.put("hasPrevious", pageAudios.hasPrevious());
            response.put("coordinador", nombreCoordinador);
            response.put("tipoMetrica", "NO_REGISTRADO");

            return response;
        } catch (Exception e) {
            // Es buena práctica loggear el error para debugging
            // log.error("Error al obtener audios huérfanos por coordinador: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener audios huérfanos por coordinador: " + e.getMessage(), e);
        }
    }

    @Override
    public Map<String, Object> obtenerLeadsBienRegistradosPorAsesor(
            String nombreAsesor, Long sedeId, LocalDate fecha,
            LocalDate fechaInicio, LocalDate fechaFin, Pageable pageable) {
        try {
            Page<ClienteConUsuarioDTO> pageLeads = clienteResidencialRepository
                    .obtenerLeadsBienRegistradosPorAsesor(nombreAsesor, sedeId, fecha, fechaInicio, fechaFin, pageable);

            Map<String, Object> response = new HashMap<>();
            response.put("leads", pageLeads.getContent());
            response.put("currentPage", pageLeads.getNumber());
            response.put("totalPages", pageLeads.getTotalPages());
            response.put("totalElements", pageLeads.getTotalElements());
            response.put("pageSize", pageLeads.getSize());
            response.put("hasNext", pageLeads.hasNext());
            response.put("hasPrevious", pageLeads.hasPrevious());
            response.put("asesor", nombreAsesor);
            response.put("tipoMetrica", "BIEN_REGISTRADO");

            return response;
        } catch (Exception e) {
            throw new RuntimeException("Error al obtener leads bien registrados por asesor: " + e.getMessage(), e);
        }
    }

    @Override
    public Map<String, Object> obtenerLeadsMalRegistradosPorAsesor(
            String nombreAsesor, Long sedeId, LocalDate fecha,
            LocalDate fechaInicio, LocalDate fechaFin, Pageable pageable) {
        try {
            Page<ClienteConUsuarioDTO> pageLeads = clienteResidencialRepository
                    .obtenerLeadsMalRegistradosPorAsesor(nombreAsesor, sedeId, fecha, fechaInicio, fechaFin, pageable);

            Map<String, Object> response = new HashMap<>();
            response.put("leads", pageLeads.getContent());
            response.put("currentPage", pageLeads.getNumber());
            response.put("totalPages", pageLeads.getTotalPages());
            response.put("totalElements", pageLeads.getTotalElements());
            response.put("pageSize", pageLeads.getSize());
            response.put("hasNext", pageLeads.hasNext());
            response.put("hasPrevious", pageLeads.hasPrevious());
            response.put("asesor", nombreAsesor);
            response.put("tipoMetrica", "MAL_REGISTRADO");

            return response;
        } catch (Exception e) {
            throw new RuntimeException("Error al obtener leads mal registrados por asesor: " + e.getMessage(), e);
        }
    }

    @Override
    public Map<String, Object> obtenerLeadsRegistradosPorAsesor(
            String nombreAsesor, Long sedeId, LocalDate fecha,
            LocalDate fechaInicio, LocalDate fechaFin, Pageable pageable) {
        try {
            Page<ClienteConUsuarioDTO> pageLeads = clienteResidencialRepository
                    .obtenerLeadsRegistradosPorAsesor(nombreAsesor, sedeId, fecha, fechaInicio, fechaFin, pageable);

            Map<String, Object> response = new HashMap<>();
            response.put("leads", pageLeads.getContent());
            response.put("currentPage", pageLeads.getNumber());
            response.put("totalPages", pageLeads.getTotalPages());
            response.put("totalElements", pageLeads.getTotalElements());
            response.put("pageSize", pageLeads.getSize());
            response.put("hasNext", pageLeads.hasNext());
            response.put("hasPrevious", pageLeads.hasPrevious());
            response.put("asesor", nombreAsesor);
            response.put("tipoMetrica", "LEADS_REGISTRADO");

            return response;
        } catch (Exception e) {
            throw new RuntimeException("Error al obtener leads registrados por asesor: " + e.getMessage(), e);
        }
    }

    // MODIFICACIÓN EN EstadisticasSedeServiceImpl.java
// Reemplaza el método obtenerAudiosHuerfanosPorAsesor existente

    @Override
    public Map<String, Object> obtenerAudiosHuerfanosPorAsesor(
            String nombreAsesor, Long sedeId, LocalDate fecha,
            LocalDate fechaInicio, LocalDate fechaFin, Pageable pageable) {
        try {
            Page<Object[]> pageAudios = clienteResidencialRepository
                    .obtenerAudiosHuerfanosPorAsesor(nombreAsesor, sedeId, fecha, fechaInicio, fechaFin, pageable);

            // Convertir Object[] a un DTO más manejable (en este caso, un Map)
            List<Map<String, Object>> audiosConvertidos = pageAudios.getContent().stream()
                    .map(record -> {
                        try {
                            // 0: agente_extraido → agente (número de agente)
                            String numeroAgente = record[0] != null ? "Agente: " + record[0].toString() : "N/A";

                            // 1: nombre_completo (nombre del asesor que usó ese agente en esa fecha)
                            String nombreAsesorQueUso = record[1] != null ? record[1].toString() : "N/A";

                            // 2: fecha_creacion - MANEJO CORRECTO DE TIMESTAMP
                            LocalDateTime fechaCreacion = null;
                            if (record[2] != null) {
                                if (record[2] instanceof java.sql.Timestamp) {
                                    fechaCreacion = ((java.sql.Timestamp) record[2]).toLocalDateTime();
                                } else if (record[2] instanceof LocalDateTime) {
                                    fechaCreacion = (LocalDateTime) record[2];
                                }
                            }

                            // 3: movil_contacto
                            String movilContacto = record[3] != null ? record[3].toString() : "N/A";

                            // 4: asesor (el asesor al que pertenece este audio huérfano)
                            String asesorPropietario = record[4] != null ? record[4].toString() : nombreAsesor;

                            // 5: url_google_drive
                            String urlGoogleDrive = record[5] != null ? record[5].toString() : null;

                            Map<String, Object> item = new HashMap<>();
                            item.put("agente", numeroAgente);
                            item.put("nombreCompleto", nombreAsesorQueUso);
                            item.put("fechaCreacion", fechaCreacion);
                            item.put("movilContacto", movilContacto);
                            item.put("asesor", asesorPropietario);
                            item.put("urlGoogleDrive", urlGoogleDrive);

                            return item;
                        } catch (Exception e) {
                            return new HashMap<String, Object>(); // Retorna un mapa vacío en caso de error en un registro
                        }
                    })
                    .collect(Collectors.toList());

            Map<String, Object> response = new HashMap<>();
            response.put("leads", audiosConvertidos);
            response.put("currentPage", pageAudios.getNumber());
            response.put("totalPages", pageAudios.getTotalPages());
            response.put("totalElements", pageAudios.getTotalElements());
            response.put("pageSize", pageAudios.getSize());
            response.put("hasNext", pageAudios.hasNext());
            response.put("hasPrevious", pageAudios.hasPrevious());
            response.put("asesor", nombreAsesor);
            response.put("tipoMetrica", "NO_REGISTRADO");

            return response;
        } catch (Exception e) {
            throw new RuntimeException("Error al obtener audios huérfanos por asesor: " + e.getMessage(), e);
        }
    }

    @Override
    public List<String> obtenerNumerosAgentePorCoordinador(
            Long coordinadorId,
            Long sedeId,
            LocalDate fecha,
            LocalDate fechaInicio,
            LocalDate fechaFin
    ) {
        // 1) Calcula rango de fechas
        LocalDateTime desde;
        LocalDateTime hasta;
        if (fecha != null) {
            desde = fecha.atStartOfDay();
            hasta = fecha.atTime(LocalTime.MAX);
        } else {
            desde = fechaInicio.atStartOfDay();
            hasta = fechaFin.atTime(LocalTime.MAX);
        }

        // 2) Trae los agentes "registrados" (clientes residenciales)
        List<String> agentesLeads = clienteResidencialRepository
                .findDistinctNumeroAgenteByCoordinadorAndSedeAndFechaRange(
                        coordinadorId, sedeId, desde, hasta
                );

        // 3) Trae los agentes "huérfanos" (audios sin lead), solo si hay leads
        List<String> agentesHuerfanos = agentesLeads.isEmpty()
                ? List.of()
                : audioSinLeadRepo
                .findDistinctAgenteExtraidoByAgentesAndFechaRange(
                        agentesLeads, desde, hasta
                );

        // 4) Unifica, limpia y ordena
        return Stream
                .concat(agentesLeads.stream(), agentesHuerfanos.stream())
                .filter(Objects::nonNull)
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .distinct()
                .sorted()
                .toList();
    }

// Reemplaza el método existente en EstadisticasSedeServiceImpl.java

    @Override
    public Map<String, Object> obtenerDetalleLeadsContacto(Long coordinadorId, Long sedeId, LocalDate fecha, LocalDate fechaInicio, LocalDate fechaFin, Pageable pageable) {
        LocalDateTime desde = (fecha != null) ? fecha.atStartOfDay() : (fechaInicio != null ? fechaInicio.atStartOfDay() : null);
        LocalDateTime hasta = (fecha != null) ? fecha.atTime(LocalTime.MAX) : (fechaFin != null ? fechaFin.atTime(LocalTime.MAX) : null);

        Page<Object[]> paginatedResults = clienteResidencialRepository.findCombinedContactDetailsPaginated(coordinadorId, sedeId, desde, hasta, pageable);

        List<Map<String, Object>> pageContent = paginatedResults.getContent().stream().map(record -> {
            Map<String, Object> item = new HashMap<>();
            item.put("tipo", record[0]);
            item.put("dni", record[1]);
            item.put("nombreCompleto", record[2]);
            item.put("movilContacto", record[3]);

            LocalDateTime fechaRegistro = null;
            if (record[4] instanceof java.sql.Timestamp) {
                fechaRegistro = ((java.sql.Timestamp) record[4]).toLocalDateTime();
            } else if (record[4] instanceof LocalDateTime) {
                fechaRegistro = (LocalDateTime) record[4];
            }
            item.put("fechaCreacion", fechaRegistro);
            item.put("coordinador", record[5]);
            item.put("leadId", record[6]);

            // ==================== CAMBIO AQUÍ ====================
            item.put("urlTranscripcion", record[7]); // Se reemplaza nombreArchivo por la URL
            // =====================================================

            return item;
        }).collect(Collectors.toList());

        Map<String, Object> response = new HashMap<>();
        response.put("content", pageContent);
        response.put("totalElements", paginatedResults.getTotalElements());
        response.put("totalPages", paginatedResults.getTotalPages());
        response.put("currentPage", paginatedResults.getNumber());

        return response;
    }
}

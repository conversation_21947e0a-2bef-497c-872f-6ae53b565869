package com.midas.crm.utils;

import lombok.extern.slf4j.Slf4j;

import java.util.Locale;

/**
 * Utilidad centralizada para normalización de números de agente.
 * Garantiza consistencia en toda la aplicación.
 */
@Slf4j
public class AgentNormalizationUtils {

    /**
     * Normaliza un número de agente para garantizar consistencia.
     *
     * Casos manejados:
     * - "agente 052" -> "52"
     * - "agent017" -> "17"
     * - "agen26" -> "26"
     * - "te005" -> "5"
     * - "011" -> "11"
     * - "000" -> "0"
     * - "16" -> "16"
     * - null/empty -> null
     *
     * @param numeroAgente Número de agente original
     * @return Número de agente normalizado o null si es inválido
     */
    public static String normalizeAgentNumber(String numeroAgente) {
        if (numeroAgente == null || numeroAgente.trim().isEmpty()) {
            return null;
        }

        String agente = numeroAgente.toLowerCase(Locale.ROOT).replaceAll("\\s+", "");

        // Remover prefijos comunes: "agente", "agent", "agen", "te"
        agente = agente.replaceFirst("^(agente|agent|agen|te)([:\\-_\\s])?", "");

        // Extraer solo números
        agente = agente.replaceAll("[^0-9]", "");

        // Remover ceros a la izquierda, pero mantener al menos un dígito
        agente = agente.replaceFirst("^0+", "");

        // Si quedó vacío después de remover ceros, devolver "0"
        if (agente.isEmpty()) {
            agente = "0";
        }

        log.debug("🔧 Normalización agente: '{}' -> '{}'", numeroAgente, agente);
        return agente;
    }

    /**
     * Verifica si dos números de agente son equivalentes después de normalización.
     *
     * @param agente1 Primer número de agente
     * @param agente2 Segundo número de agente
     * @return true si son equivalentes después de normalización
     */
    public static boolean areAgentsEquivalent(String agente1, String agente2) {
        String normalized1 = normalizeAgentNumber(agente1);
        String normalized2 = normalizeAgentNumber(agente2);

        if (normalized1 == null || normalized2 == null) {
            return normalized1 == normalized2;
        }

        return normalized1.equals(normalized2);
    }

    /**
     * Genera variaciones posibles de un número de agente para búsquedas.
     * Útil para encontrar coincidencias en bases de datos con formatos inconsistentes.
     *
     * @param numeroAgente Número de agente base
     * @return Array con variaciones [normalizado, con_ceros_2_digitos, con_ceros_3_digitos, con_prefijo_agente]
     */
    public static String[] generateAgentVariations(String numeroAgente) {
        String normalized = normalizeAgentNumber(numeroAgente);
        if (normalized == null) {
            return new String[0];
        }

        String with2Zeros = String.format("%02d", Integer.parseInt(normalized));
        String with3Zeros = String.format("%03d", Integer.parseInt(normalized));
        String withPrefix = "agente " + with3Zeros;

        return new String[]{normalized, with2Zeros, with3Zeros, withPrefix};
    }
}

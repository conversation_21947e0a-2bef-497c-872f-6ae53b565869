package com.midas.crm.service.serviceImpl;

import com.midas.crm.entity.DTO.manual.ManualDTO;
import com.midas.crm.entity.DTO.manual.ManualResponseDto;
import com.midas.crm.mapper.ManualMapper;
import com.midas.crm.entity.Manual;
import com.midas.crm.entity.Role;
import com.midas.crm.repository.ManualRepository;
import com.midas.crm.repository.SedeRepository;
import com.midas.crm.service.ManualService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.*;
import org.springframework.data.domain.*;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class ManualServiceImpl implements ManualService {

    /* ---------- L1 cache ---------- */
    private final Map<Integer, Manual> manualL1 = new ConcurrentHashMap<>();
    private final Map<String, Page<Manual>> pageL1 = new ConcurrentHashMap<>();
    private final Map<String, List<Manual>> listL1 = new ConcurrentHashMap<>();

    /* ---------- dependencias ---------- */
    private final ManualRepository manualRepository;
    private final SedeRepository sedeRepository;

    // En tu clase ManualServiceImpl
    @Override
    @Transactional(readOnly = true)
    public Page<ManualResponseDto> index(String search, int page, int size, String column, String order) {
        // La lógica para buscar es la misma que ya tenías
        Pageable pageable = PageRequest.of(
                page,
                size,
                Sort.by(
                        Sort.Direction.fromString(Optional.ofNullable(order).orElse("DESC")),
                        Optional.ofNullable(column).orElse("id")));

        Page<Manual> result = Optional.ofNullable(search)
                .filter(s -> !s.isEmpty())
                .map(s -> manualRepository.findByNombreContainingIgnoreCaseOrTipoContainingIgnoreCase(s, s, pageable))
                .orElseGet(() -> manualRepository.findAll(pageable));

        // AÑADE LA TRANSFORMACIÓN A DTO ANTES DE DEVOLVER
        return result.map(ManualMapper::toResponseDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<ManualResponseDto> indexByRole(String search, int page, int size, String column, String order, Role role) {
        // 1. Define la paginación y el ordenamiento
        Pageable pageable = PageRequest.of(
                page,
                size,
                Sort.by(
                        Sort.Direction.fromString(Optional.ofNullable(order).orElse("DESC")),
                        Optional.ofNullable(column).orElse("id")));

        // 2. Lógica de negocio para diferenciar por rol (ACTIVOS vs TODOS)
        Page<Manual> manualPage;

        // Determinar si el usuario puede ver manuales inactivos
        boolean canSeeInactive = role == Role.ADMIN || role == Role.PROGRAMADOR;

        if (canSeeInactive) {
            // Administradores y Programadores pueden ver todos los manuales (activos e inactivos)
            manualPage = Optional.ofNullable(search)
                    .filter(s -> !s.isBlank())
                    .map(s -> manualRepository.findByNombreContainingIgnoreCaseOrTipoContainingIgnoreCase(s, s, pageable))
                    .orElseGet(() -> manualRepository.findAll(pageable));
        } else {
            // Los demás usuarios solo pueden ver manuales activos
            manualPage = Optional.ofNullable(search)
                    .filter(s -> !s.isBlank())
                    .map(s -> manualRepository.findByNombreContainingIgnoreCaseOrTipoContainingIgnoreCaseAndIsActiveTrue(s, s, pageable))
                    .orElseGet(() -> manualRepository.findAllByIsActiveTrue(pageable));
        }

        // 3. Transforma la página de Entidades a una página de DTOs antes de devolverla
        return manualPage.map(ManualMapper::toResponseDto);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Manual> getAll() {
        // Obtener manuales activos y aplicar transformación en un solo flujo
        return manualRepository.findAllActiveIncludingDeleted().stream()
                .peek(Manual::setTipoTextFromCode)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    @Cacheable(cacheNames = "manualList", key = "#role")
    public List<Manual> getAllByRole(Role role) {
        String key = "role:" + role.name();
        return listL1.computeIfAbsent(key, k -> {
            // Determinar si el usuario puede ver manuales inactivos
            boolean canSeeInactive = role == Role.ADMIN || role == Role.PROGRAMADOR || role == Role.MARKETING;

            // Obtener manuales según el rol y aplicar transformación en un solo flujo
            if (canSeeInactive) {
                // Administradores pueden ver todos los manuales (activos e inactivos)
                return manualRepository.findAllIncludingDeleted().stream()
                        .peek(Manual::setTipoTextFromCode)
                        .collect(Collectors.toList());
            } else {
                // Otros usuarios solo pueden ver manuales activos
                return manualRepository.findAllActive().stream()
                        .peek(Manual::setTipoTextFromCode)
                        .collect(Collectors.toList());
            }
        });
    }

    @Override
    @Transactional(readOnly = true)
    @Cacheable(cacheNames = "manualById", key = "#id")
    public Manual getById(int id) {
        return manualL1.computeIfAbsent(id,
                k -> manualRepository.findById(k)
                        .map(manual -> {
                            manual.setTipoTextFromCode();
                            return manual;
                        })
                        .orElseThrow(() -> new RuntimeException("Manual no encontrado")));
    }

    @Override
    @Transactional
    @CacheEvict(cacheNames = { "manualById", "manualPage", "manualList" }, allEntries = true)
    public Manual create(ManualDTO dto, MultipartFile file) {
        Manual saved;
        // Si se especifica una sede, buscarla y crear el manual con la sede
        if (dto.getSedeId() != null) {
            saved = sedeRepository.findById(dto.getSedeId())
                    .map(sede -> {
                        Manual manual = ManualMapper.toEntity(dto, sede);
                        Manual result = manualRepository.save(manual);
                        result.setTipoTextFromCode();
                        return result;
                    })
                    .orElseThrow(() -> new RuntimeException("Sede no encontrada con ID: " + dto.getSedeId()));
        } else {
            // Crear manual sin sede específica (global)
            saved = Optional.of(dto)
                    .map(ManualMapper::toEntity)
                    .map(manualRepository::save)
                    .map(result -> {
                        result.setTipoTextFromCode();
                        return result;
                    })
                    .orElseThrow(() -> new RuntimeException("Error al crear el manual"));
        }
        clearL1();
        return saved;
    }

    /**
     * Actualiza un manual existente con los datos proporcionados en el DTO.
     * Permite actualizaciones parciales donde solo se modifican los campos que
     * vienen en el DTO.
     * Respeta el estado de eliminación (soft delete) del manual.
     * Utiliza una consulta nativa para evitar problemas con el bloqueo optimista.
     */
    @Override
    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames="manualById", key="#id"),
            @CacheEvict(cacheNames={"manualPage","manualList"}, allEntries=true)
    })
    public Manual update(int id, ManualDTO dto, MultipartFile file) {
        try {
            // Usar Optional para manejar el flujo de verificación y actualización
            return manualRepository.findByIdIncludingDeleted(id)
                    .map(manual -> {
                        // Verificar si el manual está eliminado
                        if (manual.getDeletedAt() != null) {
                            throw new RuntimeException("No se puede actualizar un manual eliminado. ID: " + id);
                        }

                        // Preparar los datos para la actualización usando Optional
                        String nombre = Optional.ofNullable(dto.getNombre())
                                .filter(n -> !n.trim().isEmpty())
                                .map(String::trim)
                                .orElse(manual.getNombre());

                        String tipo = Optional.ofNullable(dto.getTipo())
                                .filter(t -> !t.trim().isEmpty())
                                .map(String::trim)
                                .orElse(manual.getTipo());

                        String archivo = Optional.ofNullable(dto.getArchivo())
                                .orElse(manual.getArchivo());

                        String horario = Optional.ofNullable(dto.getHorario())
                                .orElse(manual.getHorario());

                        Long userUpdateId = Optional.ofNullable(dto.getUserAuthId())
                                .orElse(manual.getUserUpdateId());

                        // Determinar el estado (isActive) del manual
                        Boolean isActive = manual.getIsActive(); // Valor actual por defecto

                        // Si viene en el DTO, usar ese valor (puede ser true o false)
                        if (dto.getIsActive() != null) {
                            isActive = dto.getIsActive();
                            System.out.println("Estado del manual recibido en DTO: " + isActive);
                        } else if (dto.getIs_active() != null) {
                            // Compatibilidad con el formato antiguo
                            isActive = dto.getIs_active();
                            System.out.println("Estado del manual recibido en formato antiguo: " + isActive);
                        }

                        // Si isActive es null (no debería ocurrir), establecer a true por defecto
                        if (isActive == null) {
                            isActive = true;
                        }

                        // Actualizar el manual usando una consulta nativa
                        int rowsUpdated;
                        if (dto.getSedeId() != null) {
                            // Si se especifica una sede, usar el método que incluye sede_id
                            rowsUpdated = manualRepository.updateManualWithStateAndSedeById(id, nombre, tipo, archivo,
                                    horario,
                                    isActive, dto.getSedeId(), userUpdateId);
                        } else {
                            // Si no se especifica sede (manual global), usar el método original
                            rowsUpdated = manualRepository.updateManualWithStateById(id, nombre, tipo, archivo, horario,
                                    isActive, userUpdateId);
                        }

                        if (rowsUpdated == 0) {
                            throw new RuntimeException("No se pudo actualizar el manual con ID: " + id);
                        }

                        // Obtener y procesar el manual actualizado
                        Manual updated = manualRepository.findByIdIncludingDeleted(id)
                                .map(result -> {
                                    result.setTipoTextFromCode();
                                    return result;
                                })
                                .orElseThrow(() -> new RuntimeException(
                                        "No se pudo encontrar el manual actualizado con ID: " + id));

                        invalidate(id);
                        return updated;
                    })
                    .orElseThrow(() -> new RuntimeException("Manual no encontrado con ID: " + id));
        } catch (Exception e) {
            // Registrar y relanzar la excepción para que sea manejada por el controlador
            throw new RuntimeException("Error al actualizar el manual: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames="manualById", key="#id"),
            @CacheEvict(cacheNames={"manualPage","manualList"}, allEntries=true)
    })
    public boolean delete(int id, Long userDeleteId) {
        // Usar Optional para manejar el flujo de verificación y eliminación
        boolean result = manualRepository.findByIdIncludingDeleted(id)
                .map(manual -> {
                    // Primera etapa: marcar como inactivo con horario 00000
                    int rowsAffected = manualRepository.markAsInactiveById(id, userDeleteId);
                    return rowsAffected > 0;
                })
                .orElse(false);

        if (result) {
            invalidate(id);
        }
        return result;
    }

    @Override
    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames="manualById", key="#id"),
            @CacheEvict(cacheNames={"manualPage","manualList"}, allEntries=true)
    })
    public boolean permanentDelete(int id, Long userDeleteId) {
        // Realizar soft delete directo sin verificar si está inactivo
        boolean result = manualRepository.findByIdIncludingDeleted(id)
                .map(manual -> {
                    // Realizar soft delete estableciendo deleted_at
                    int rowsAffected = manualRepository.softDeleteById(id, userDeleteId);
                    return rowsAffected > 0;
                })
                .orElse(false);

        if (result) {
            invalidate(id);
        }
        return result;
    }

    @Override
    public Manual restore(int id) {
        // Usar Optional y programación funcional para todo el flujo
        return manualRepository.findByIdIncludingDeleted(id)
                .map(manual -> {
                    // Verificar si el manual ya está activo
                    if (manual.getDeletedAt() == null &&
                            (manual.getHorario() == null || !manual.getHorario().equals("00000")) &&
                            manual.getIsActive()) {
                        return manual; // El manual ya está activo, no es necesario restaurarlo
                    }

                    // Restaurar el manual y obtener el resultado
                    return Optional.of(manualRepository.restoreById(id))
                            .filter(rowsAffected -> rowsAffected > 0)
                            .flatMap(rowsAffected -> manualRepository.findByIdIncludingDeleted(id))
                            .map(restoredManual -> {
                                restoredManual.setTipoTextFromCode();
                                return restoredManual;
                            })
                            .orElse(null);
                })
                .orElse(null);
    }

    @Override
    @Transactional(readOnly = true)
    @Cacheable(cacheNames = "manualList", key = "{'sede',#sedeId}")
    public List<Manual> getBySedeId(Long sedeId) {
        String key = "sede:" + sedeId;
        return listL1.computeIfAbsent(key, k ->
                manualRepository.findBySedeIdAndIsActiveTrue(sedeId).stream()
                        .map(manual -> {
                            manual.setTipoTextFromCode();
                            return manual;
                        })
                        .collect(Collectors.toList()));
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Manual> indexBySedeId(Long sedeId, String search, int page, int size, String column, String order) {
        Pageable pageable = PageRequest.of(
                page,
                size,
                Sort.by(
                        Sort.Direction.fromString(Optional.ofNullable(order).orElse("DESC")),
                        Optional.ofNullable(column).orElse("id")));

        Page<Manual> result = Optional.ofNullable(search)
                .filter(s -> !s.isEmpty())
                .map(s -> manualRepository.findBySedeIdAndIsActiveTrueAndSearch(sedeId, s, pageable))
                .orElse(manualRepository.findBySedeIdAndIsActiveTrue(sedeId, pageable));

        return result.map(manual -> {
            manual.setTipoTextFromCode();
            return manual;
        });
    }

    @Override
    @Transactional(readOnly = true)
    @Cacheable(cacheNames = "manualList", key = "{'global',#sedeId}")
    public List<Manual> getGlobalOrBySedeId(Long sedeId) {
        String key = "global:" + sedeId;
        return listL1.computeIfAbsent(key, k ->
                manualRepository.findGlobalOrBySedeIdAndIsActiveTrue(sedeId).stream()
                        .map(manual -> {
                            manual.setTipoTextFromCode();
                            return manual;
                        })
                        .collect(Collectors.toList()));
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Manual> indexGlobalOrBySedeId(Long sedeId, String search, int page, int size, String column,
                                              String order) {
        Pageable pageable = PageRequest.of(
                page,
                size,
                Sort.by(
                        Sort.Direction.fromString(Optional.ofNullable(order).orElse("DESC")),
                        Optional.ofNullable(column).orElse("id")));

        Page<Manual> result = Optional.ofNullable(search)
                .filter(s -> !s.isEmpty())
                .map(s -> manualRepository.findGlobalOrBySedeIdAndIsActiveTrueAndSearch(sedeId, s, pageable))
                .orElse(manualRepository.findGlobalOrBySedeIdAndIsActiveTrue(sedeId, pageable));

        return result.map(manual -> {
            manual.setTipoTextFromCode();
            return manual;
        });
    }

    /* =============================================================== */
    /*                      CACHE MANAGEMENT                           */
    /* =============================================================== */

    /**
     * Limpia todo el cache L1
     */
    private void clearL1() {
        manualL1.clear();
        pageL1.clear();
        listL1.clear();
    }

    /**
     * Invalida cache específico para un manual
     */
    private void invalidate(Integer id) {
        manualL1.remove(id);
        // Limpiar cache de páginas y listas que podrían contener este manual
        pageL1.clear();
        listL1.clear();
    }

    /**
     * Método async para publicar eventos
     */
    @Async
    void publishAsync(Object event) {
    }
}

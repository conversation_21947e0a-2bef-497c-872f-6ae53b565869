package com.midas.crm.repository;

import com.midas.crm.entity.DTO.transcription.TAListadoDTO;
import com.midas.crm.entity.TranscriptionAnalysis;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repositorio para manejar análisis de transcripción
 */
@Repository
public interface TranscriptionAnalysisRepository extends JpaRepository<TranscriptionAnalysis, Long> {

    /**
     * Busca análisis por ID del cliente residencial
     */
    Optional<TranscriptionAnalysis> findByClienteResidencialId(Long clienteResidencialId);

    /**
     * Busca todos los análisis de un cliente residencial
     */
    List<TranscriptionAnalysis> findAllByClienteResidencialIdOrderByFechaCreacionDesc(Long clienteResidencialId);

    /**
     * Busca análisis por estado
     */
    List<TranscriptionAnalysis> findByEstado(String estado);

    /**
     * Busca análisis por rango de fechas
     */
    @Query("SELECT ta FROM TranscriptionAnalysis ta WHERE ta.fechaCreacion BETWEEN :fechaInicio AND :fechaFin ORDER BY ta.fechaCreacion DESC")
    List<TranscriptionAnalysis> findByFechaCreacionBetween(
            @Param("fechaInicio") LocalDateTime fechaInicio,
            @Param("fechaFin") LocalDateTime fechaFin
    );

    /**
     * Busca análisis por rango de fechas paginado
     */
    @Query("SELECT ta FROM TranscriptionAnalysis ta WHERE ta.fechaCreacion BETWEEN :fechaInicio AND :fechaFin ORDER BY ta.fechaCreacion DESC")
    Page<TranscriptionAnalysis> findByFechaCreacionBetween(
            @Param("fechaInicio") LocalDateTime fechaInicio,
            @Param("fechaFin") LocalDateTime fechaFin,
            Pageable pageable
    );

    /**
     * Busca análisis por nivel de confianza
     */
    List<TranscriptionAnalysis> findByNivelConfianza(String nivelConfianza);

    /**
     * Busca análisis con porcentaje promedio mayor o igual al especificado
     */
    @Query("SELECT ta FROM TranscriptionAnalysis ta WHERE ta.porcentajePromedio >= :porcentajeMinimo ORDER BY ta.porcentajePromedio DESC")
    List<TranscriptionAnalysis> findByPorcentajePromedioGreaterThanEqual(@Param("porcentajeMinimo") Double porcentajeMinimo);

    /**
     * Busca análisis con porcentaje promedio menor al especificado
     */
    @Query("SELECT ta FROM TranscriptionAnalysis ta WHERE ta.porcentajePromedio < :porcentajeMaximo ORDER BY ta.porcentajePromedio ASC")
    List<TranscriptionAnalysis> findByPorcentajePromedioLessThan(@Param("porcentajeMaximo") Double porcentajeMaximo);

    /**
     * Cuenta análisis por estado
     */
    long countByEstado(String estado);

    /**
     * Cuenta análisis por nivel de confianza
     */
    long countByNivelConfianza(String nivelConfianza);

    /**
     * Obtiene estadísticas de análisis por rango de fechas
     */
    @Query("""
            SELECT 
                COUNT(ta.id) as total,
                AVG(ta.porcentajePromedio) as promedioGeneral,
                MIN(ta.porcentajePromedio) as minimoPromedio,
                MAX(ta.porcentajePromedio) as maximoPromedio,
                COUNT(CASE WHEN ta.nivelConfianza = 'ALTO' THEN 1 END) as confianzaAlta,
                COUNT(CASE WHEN ta.nivelConfianza = 'MEDIO' THEN 1 END) as confianzaMedia,
                COUNT(CASE WHEN ta.nivelConfianza = 'BAJO' THEN 1 END) as confianzaBaja,
                COUNT(CASE WHEN ta.nivelConfianza = 'MUY_BAJO' THEN 1 END) as confianzaMuyBaja
            FROM TranscriptionAnalysis ta 
            WHERE ta.fechaCreacion BETWEEN :fechaInicio AND :fechaFin
            """)
    Object[] getEstadisticasByFechaCreacionBetween(
            @Param("fechaInicio") LocalDateTime fechaInicio,
            @Param("fechaFin") LocalDateTime fechaFin
    );

    /**
     * Busca análisis por número de agente del cliente residencial
     */
    @Query("SELECT ta FROM TranscriptionAnalysis ta WHERE ta.clienteResidencial.numeroAgente = :numeroAgente ORDER BY ta.fechaCreacion DESC")
    List<TranscriptionAnalysis> findByNumeroAgente(@Param("numeroAgente") String numeroAgente);

    /**
     * Busca análisis por móvil de contacto del cliente residencial
     */
    @Query("SELECT ta FROM TranscriptionAnalysis ta WHERE ta.clienteResidencial.movilContacto = :movilContacto ORDER BY ta.fechaCreacion DESC")
    List<TranscriptionAnalysis> findByMovilContacto(@Param("movilContacto") String movilContacto);

    /**
     * Verifica si existe un análisis para un cliente residencial específico
     */
    boolean existsByClienteResidencialId(Long clienteResidencialId);

    /**
     * Elimina análisis antiguos (más de X días)
     */
    @Query("DELETE FROM TranscriptionAnalysis ta WHERE ta.fechaCreacion < :fechaLimite")
    void deleteByFechaCreacionBefore(@Param("fechaLimite") LocalDateTime fechaLimite);

    /**
     * Busca los últimos N análisis
     */
    List<TranscriptionAnalysis> findTop10ByOrderByFechaCreacionDesc();

    /**
     * Busca análisis con errores o problemas
     */
    @Query("SELECT ta FROM TranscriptionAnalysis ta WHERE ta.estado = 'ERROR' OR ta.porcentajePromedio IS NULL OR ta.porcentajePromedio < 10")
    List<TranscriptionAnalysis> findProblematicAnalysis();

    @Query("""
   SELECT new com.midas.crm.entity.DTO.transcription.TAListadoDTO(
        ta.id,
        cr.id,
        cr.nombresApellidos,
        cr.movilContacto,
        cr.numeroAgente,
        CONCAT(u.nombre, ' ', u.apellido),
        ta.porcentajePromedio,
        ta.nivelConfianza,
        ta.estado,
        ta.fechaCreacion)
   FROM TranscriptionAnalysis ta
   JOIN ta.clienteResidencial cr
   JOIN cr.usuario u
   WHERE ta.fechaCreacion BETWEEN :ini AND :fin
   ORDER BY ta.fechaCreacion DESC
""")
    Page<TAListadoDTO> findListadoDTO(
            @Param("ini") LocalDateTime ini,
            @Param("fin") LocalDateTime fin,
            Pageable pageable);


}

package com.midas.crm.mapper;

import com.midas.crm.entity.Asistencia;
import com.midas.crm.entity.DTO.asistencia.AsistenciaCreateDTO;
import com.midas.crm.entity.DTO.asistencia.AsistenciaDTO;
import com.midas.crm.entity.User;
import com.midas.crm.repository.UserRepository;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

public class AsistenciaMapper {
    
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("dd/MM/yyyy");
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss");
    
    public static AsistenciaDTO toDTO(Asistencia asistencia) {
        if (asistencia == null) {
            return null;
        }
        
        AsistenciaDTO dto = new AsistenciaDTO();
        dto.setId(asistencia.getId());
        dto.setUsuarioId(asistencia.getUsuario().getId());
        dto.setUsuarioNombre(asistencia.getUsuario().getNombre());
        dto.setUsuarioApellido(asistencia.getUsuario().getApellido());
        dto.setUsuarioUsername(asistencia.getUsuario().getUsername());
        dto.setFechaHoraEntrada(asistencia.getFechaHoraEntrada());
        dto.setFechaHoraSalida(asistencia.getFechaHoraSalida());
        dto.setIpEntrada(asistencia.getIpEntrada());
        dto.setIpSalida(asistencia.getIpSalida());
        dto.setDispositivoEntrada(asistencia.getDispositivoEntrada());
        dto.setDispositivoSalida(asistencia.getDispositivoSalida());
        dto.setUbicacionEntrada(asistencia.getUbicacionEntrada());
        dto.setUbicacionSalida(asistencia.getUbicacionSalida());
        dto.setTipoActividad(asistencia.getTipoActividad());
        dto.setSubtipoActividad(asistencia.getSubtipoActividad());
        dto.setDuracionMinutos(asistencia.getDuracionMinutos());
        dto.setTiempoSesionMinutos(asistencia.getTiempoSesionMinutos());
        dto.setContadorBreakDia(asistencia.getContadorBreakDia());
        dto.setContadorBanoDia(asistencia.getContadorBanoDia());
        dto.setEstado(asistencia.getEstado());
        dto.setObservaciones(asistencia.getObservaciones());
        
        // Campos calculados
        dto.setFechaFormateada(asistencia.getFechaHoraEntrada().format(DATE_FORMATTER));
        dto.setHoraEntradaFormateada(asistencia.getFechaHoraEntrada().format(TIME_FORMATTER));
        
        if (asistencia.getFechaHoraSalida() != null) {
            dto.setHoraSalidaFormateada(asistencia.getFechaHoraSalida().format(TIME_FORMATTER));
            dto.setTiempoTrabajado(calcularTiempoTrabajado(asistencia.getFechaHoraEntrada(), asistencia.getFechaHoraSalida()));
        }
        
        return dto;
    }
    
    public static Asistencia toEntity(AsistenciaCreateDTO dto, UserRepository userRepository) {
        if (dto == null) {
            return null;
        }
        
        User usuario = userRepository.findById(dto.getUsuarioId())
                .orElseThrow(() -> new RuntimeException("Usuario no encontrado"));
        
        Asistencia asistencia = new Asistencia();
        asistencia.setUsuario(usuario);
        asistencia.setFechaHoraEntrada(dto.getFechaHoraEntrada() != null ? dto.getFechaHoraEntrada() : LocalDateTime.now());
        asistencia.setFechaHoraSalida(dto.getFechaHoraSalida());
        asistencia.setIpEntrada(dto.getIpEntrada());
        asistencia.setIpSalida(dto.getIpSalida());
        asistencia.setDispositivoEntrada(dto.getDispositivoEntrada());
        asistencia.setDispositivoSalida(dto.getDispositivoSalida());
        asistencia.setUbicacionEntrada(dto.getUbicacionEntrada());
        asistencia.setUbicacionSalida(dto.getUbicacionSalida());
        asistencia.setTipoActividad(dto.getTipoActividad());
        asistencia.setSubtipoActividad(dto.getSubtipoActividad());
        asistencia.setDuracionMinutos(dto.getDuracionMinutos());
        asistencia.setTiempoSesionMinutos(dto.getTiempoSesionMinutos());
        asistencia.setObservaciones(dto.getObservaciones());
        asistencia.setEstado("A");
        
        return asistencia;
    }
    
    public static List<AsistenciaDTO> toDTOList(List<Asistencia> asistencias) {
        return asistencias.stream()
                .map(AsistenciaMapper::toDTO)
                .collect(Collectors.toList());
    }
    
    private static String calcularTiempoTrabajado(LocalDateTime entrada, LocalDateTime salida) {
        if (entrada == null || salida == null) {
            return null;
        }
        
        Duration duration = Duration.between(entrada, salida);
        long horas = duration.toHours();
        long minutos = duration.toMinutesPart();
        
        return String.format("%02d:%02d", horas, minutos);
    }
}

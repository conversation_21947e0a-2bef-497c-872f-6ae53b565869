package com.midas.crm.util;

import lombok.extern.slf4j.Slf4j;

import java.lang.management.ManagementFactory;
import java.lang.management.ThreadInfo;
import java.lang.management.ThreadMXBean;
import java.util.Arrays;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Supplier;

/**
 * Utilidades para gestión de concurrencia
 * Incluye herramientas para:
 * - Detección y prevención de deadlocks
 * - Prevención de livelocks
 * - Protección contra starvation
 */
@Slf4j
public class ConcurrencyUtils {

    // Mapa para rastrear recursos bloqueados por thread
    private static final Map<Long, String> threadResourceMap = new ConcurrentHashMap<>();
    
    // Contador para detectar posibles livelocks
    private static final Map<String, AtomicInteger> operationRetryCounter = new ConcurrentHashMap<>();
    
    // Semáforos para limitar acceso a recursos críticos y prevenir starvation
    private static final Map<String, Semaphore> resourceSemaphores = new ConcurrentHashMap<>();
    
    // Tiempo máximo de espera para adquirir un recurso (ms)
    private static final long DEFAULT_RESOURCE_TIMEOUT = 5000;
    
    // Número máximo de reintentos antes de considerar un posible livelock
    private static final int MAX_RETRY_COUNT = 5;

    /**
     * Detecta deadlocks en la JVM
     * @return true si se detectaron deadlocks, false en caso contrario
     */
    public static boolean detectDeadlocks() {
        ThreadMXBean threadBean = ManagementFactory.getThreadMXBean();
        long[] threadIds = threadBean.findDeadlockedThreads();
        
        if (threadIds != null && threadIds.length > 0) {
            ThreadInfo[] threadInfos = threadBean.getThreadInfo(threadIds, true, true);
            log.error("¡DEADLOCK DETECTADO! Threads involucrados:");
            
            Arrays.stream(threadInfos)
                  .forEach(threadInfo -> {
                      log.error("Thread '{}' bloqueado en: {}", 
                              threadInfo.getThreadName(), 
                              threadInfo.getLockName());
                      log.error("Esperando por el lock en posesión de: '{}'", 
                              threadInfo.getLockOwnerName());
                      log.error("Stack trace: {}", 
                              Arrays.toString(threadInfo.getStackTrace()));
                  });
            return true;
        }
        return false;
    }
    
    /**
     * Ejecuta una operación con protección contra deadlocks
     * @param resourceId Identificador del recurso
     * @param operation Operación a ejecutar
     * @param <T> Tipo de retorno de la operación
     * @return Resultado de la operación
     */
    public static <T> T executeWithDeadlockProtection(String resourceId, Supplier<T> operation) {
        long threadId = Thread.currentThread().getId();
        
        // Verificar si ya hay un deadlock en el sistema
        if (detectDeadlocks()) {
            log.error("Operación cancelada debido a deadlock detectado en el sistema");
            throw new RuntimeException("Deadlock detectado en el sistema");
        }
        
        // Registrar que este thread está intentando acceder al recurso
        String previousResource = threadResourceMap.put(threadId, resourceId);
        
        try {
            // Ejecutar la operación
            return operation.get();
        } finally {
            // Limpiar el registro
            if (previousResource != null) {
                threadResourceMap.put(threadId, previousResource);
            } else {
                threadResourceMap.remove(threadId);
            }
        }
    }
    
    /**
     * Ejecuta una operación con protección contra livelocks
     * @param operationId Identificador de la operación
     * @param operation Operación a ejecutar
     * @param <T> Tipo de retorno de la operación
     * @return Resultado de la operación
     */
    public static <T> T executeWithLivelockProtection(String operationId, Supplier<T> operation) {
        // Obtener o crear contador de reintentos para esta operación
        AtomicInteger retryCount = operationRetryCounter.computeIfAbsent(
                operationId, k -> new AtomicInteger(0));
        
        try {
            // Incrementar contador de reintentos
            int currentRetries = retryCount.incrementAndGet();
            
            // Si superamos el umbral, podría ser un livelock
            if (currentRetries > MAX_RETRY_COUNT) {
                log.warn("Posible livelock detectado en operación: {}. Reintentos: {}", 
                        operationId, currentRetries);
                
                // Esperar un tiempo aleatorio para romper el patrón de livelock
                Thread.sleep((long) (Math.random() * 1000));
            }
            
            // Ejecutar la operación
            return operation.get();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("Operación interrumpida", e);
        } finally {
            // Resetear contador si la operación fue exitosa
            retryCount.set(0);
        }
    }
    
    /**
     * Ejecuta una operación con protección contra starvation
     * @param resourceId Identificador del recurso
     * @param maxConcurrentAccess Número máximo de accesos concurrentes permitidos
     * @param operation Operación a ejecutar
     * @param <T> Tipo de retorno de la operación
     * @return Resultado de la operación
     */
    public static <T> T executeWithStarvationProtection(
            String resourceId, 
            int maxConcurrentAccess, 
            Supplier<T> operation) {
        
        // Obtener o crear semáforo para este recurso
        Semaphore semaphore = resourceSemaphores.computeIfAbsent(
                resourceId, k -> new Semaphore(maxConcurrentAccess, true)); // true = fair
        
        boolean acquired = false;
        try {
            // Intentar adquirir el semáforo con timeout
            acquired = semaphore.tryAcquire(DEFAULT_RESOURCE_TIMEOUT, TimeUnit.MILLISECONDS);
            
            if (!acquired) {
                log.warn("Posible starvation detectada en recurso: {}. No se pudo adquirir acceso después de {}ms", 
                        resourceId, DEFAULT_RESOURCE_TIMEOUT);
                throw new RuntimeException("Timeout al intentar acceder al recurso: " + resourceId);
            }
            
            // Ejecutar la operación
            return operation.get();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("Operación interrumpida", e);
        } finally {
            // Liberar el semáforo si lo adquirimos
            if (acquired) {
                semaphore.release();
            }
        }
    }
    
    /**
     * Ejecuta una operación con todas las protecciones de concurrencia
     * @param resourceId Identificador del recurso
     * @param operationId Identificador de la operación
     * @param maxConcurrentAccess Número máximo de accesos concurrentes permitidos
     * @param operation Operación a ejecutar
     * @param <T> Tipo de retorno de la operación
     * @return Resultado de la operación
     */
    public static <T> T executeWithFullProtection(
            String resourceId, 
            String operationId,
            int maxConcurrentAccess, 
            Supplier<T> operation) {
        
        return executeWithDeadlockProtection(resourceId, () -> 
                executeWithLivelockProtection(operationId, () -> 
                        executeWithStarvationProtection(resourceId, maxConcurrentAccess, operation)));
    }
    
    /**
     * Clase para implementar un patrón de adquisición de múltiples locks en orden
     * para prevenir deadlocks
     */
    public static class OrderedLockAcquisition {
        private final Lock[] locks;
        
        /**
         * Constructor
         * @param locks Locks a adquirir en orden
         */
        public OrderedLockAcquisition(Lock... locks) {
            // Ordenar los locks por su hashCode para garantizar un orden consistente
            this.locks = locks.clone();
            Arrays.sort(this.locks, (l1, l2) -> 
                    Integer.compare(System.identityHashCode(l1), System.identityHashCode(l2)));
        }
        
        /**
         * Ejecuta una operación adquiriendo todos los locks en orden
         * @param operation Operación a ejecutar
         * @param <T> Tipo de retorno de la operación
         * @return Resultado de la operación
         */
        public <T> T execute(Supplier<T> operation) {
            // Adquirir todos los locks en orden
            for (Lock lock : locks) {
                try {
                    if (!lock.tryLock(DEFAULT_RESOURCE_TIMEOUT, TimeUnit.MILLISECONDS)) {
                        // Si no podemos adquirir algún lock, liberar los que ya tenemos
                        for (Lock acquiredLock : locks) {
                            if (acquiredLock == lock) break;
                            acquiredLock.unlock();
                        }
                        throw new RuntimeException("No se pudo adquirir el lock después de " + 
                                DEFAULT_RESOURCE_TIMEOUT + "ms");
                    }
                } catch (InterruptedException e) {
                    // Si somos interrumpidos, liberar los locks que ya tenemos
                    for (Lock acquiredLock : locks) {
                        if (acquiredLock == lock) break;
                        acquiredLock.unlock();
                    }
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("Interrumpido mientras se adquirían los locks", e);
                }
            }
            
            try {
                // Ejecutar la operación con todos los locks adquiridos
                return operation.get();
            } finally {
                // Liberar todos los locks en orden inverso
                for (int i = locks.length - 1; i >= 0; i--) {
                    locks[i].unlock();
                }
            }
        }
    }
}

<div class="min-h-screen bg-gray-50 dark:bg-gray-900 p-4">
  <div class="max-w-6xl mx-auto">
    <!-- Header -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
          <button 
            (click)="router.navigate(['/correos'])"
            class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
          </button>
          <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Bandeja de Entrada</h1>
            <p class="text-gray-600 dark:text-gray-400">{{ mensajes.length }} mensajes</p>
          </div>
        </div>
        
        <div class="flex items-center space-x-3">
          <!-- Selector de cantidad -->
          <select
            (change)="onCambiarCantidad($event)"
            [value]="maxResults"
            class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm">
            <option value="10">10 mensajes</option>
            <option value="20">20 mensajes</option>
            <option value="50">50 mensajes</option>
            <option value="100">100 mensajes</option>
          </select>
          
          <!-- Botón recargar -->
          <button 
            (click)="recargar()"
            [disabled]="cargando"
            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
            <svg class="w-4 h-4" [class.animate-spin]="cargando" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div *ngIf="cargando" class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-8">
      <div class="flex flex-col items-center justify-center space-y-4">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        <p class="text-gray-600 dark:text-gray-400">Cargando mensajes...</p>
      </div>
    </div>

    <!-- Error State -->
    <div *ngIf="error && !cargando" class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-red-200 dark:border-red-700 p-6">
      <div class="flex items-center space-x-3 text-red-600 dark:text-red-400">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <div>
          <h3 class="font-semibold">Error al cargar mensajes</h3>
          <p class="text-sm">{{ error }}</p>
        </div>
      </div>
      <div class="mt-4">
        <button 
          (click)="recargar()"
          class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
          Reintentar
        </button>
      </div>
    </div>

    <!-- Messages List -->
    <div *ngIf="!cargando && !error" class="space-y-2">
      <!-- Empty State -->
      <div *ngIf="mensajes.length === 0" class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-8 text-center">
        <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"></path>
        </svg>
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No hay mensajes</h3>
        <p class="text-gray-600 dark:text-gray-400">Tu bandeja de entrada está vacía.</p>
      </div>

      <!-- Messages -->
      <div *ngFor="let mensaje of mensajes; trackBy: trackByMessageId" 
           (click)="verDetalleMensaje(mensaje)"
           class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors">
        <div class="flex items-start space-x-4">
          <!-- Avatar -->
          <div class="flex-shrink-0">
            <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
              <span class="text-blue-600 dark:text-blue-400 font-medium text-sm">
                {{ extraerNombreRemitente(mensaje.de).charAt(0).toUpperCase() }}
              </span>
            </div>
          </div>

          <!-- Content -->
          <div class="flex-1 min-w-0">
            <div class="flex items-center justify-between mb-1">
              <h3 class="text-sm font-medium text-gray-900 dark:text-white truncate">
                {{ extraerNombreRemitente(mensaje.de) }}
              </h3>
              <span class="text-xs text-gray-500 dark:text-gray-400 flex-shrink-0 ml-2">
                {{ formatearFecha(mensaje.fecha) }}
              </span>
            </div>
            
            <p class="text-sm text-gray-600 dark:text-gray-300 font-medium mb-1 truncate">
              {{ truncarAsunto(mensaje.asunto || '(Sin asunto)') }}
            </p>
            
            <p class="text-sm text-gray-500 dark:text-gray-400 truncate">
              {{ truncarSnippet(mensaje.snippet) }}
            </p>
          </div>

          <!-- Arrow -->
          <div class="flex-shrink-0">
            <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

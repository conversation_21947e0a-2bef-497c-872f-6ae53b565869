package com.midas.crm.service;

import com.google.api.client.auth.oauth2.AuthorizationCodeFlow;
import com.google.api.client.auth.oauth2.Credential;
import com.google.api.client.auth.oauth2.TokenResponse;
import com.google.api.client.googleapis.auth.oauth2.GoogleAuthorizationCodeFlow;
import com.google.api.client.googleapis.auth.oauth2.GoogleClientSecrets;
import com.google.api.client.googleapis.auth.oauth2.GoogleCredential;
import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.JsonFactory;
import com.google.api.client.json.gson.GsonFactory;
import com.google.api.services.drive.DriveScopes;
import com.google.api.services.gmail.GmailScopes;
import com.google.api.services.oauth2.Oauth2;
import com.google.api.services.oauth2.model.Userinfo;
import com.midas.crm.entity.GoogleGlobalCredential;
import com.midas.crm.entity.User;
import com.midas.crm.repository.GoogleGlobalCredentialRepository;
import com.midas.crm.repository.UserRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.security.GeneralSecurityException;
import java.util.Arrays;
import java.util.List;

@Service
@Slf4j
public class GoogleOAuthService {

    private static final JsonFactory JSON_FACTORY = GsonFactory.getDefaultInstance();
    public static final String GLOBAL_DRIVE_TOKEN_ID = "DRIVE_API_TOKEN";

    private final UserRepository userRepository;
    private final GoogleGlobalCredentialRepository globalCredentialRepository;

    @Value("${google.drive.credentials.file:client_secret_com.json}")
    private String credentialsFilePath;

    @Value("${google.drive.oauth.redirect-uri}")
    private String redirectUri;

    private final GoogleClientSecrets clientSecrets;
    private final List<String> scopes;
    private final NetHttpTransport httpTransport;

    public GoogleOAuthService(UserRepository userRepository, GoogleGlobalCredentialRepository globalCredentialRepository) {
        this.userRepository = userRepository;
        this.globalCredentialRepository = globalCredentialRepository;
        try {
            this.httpTransport = GoogleNetHttpTransport.newTrustedTransport();
            InputStream in = getCredentialsInputStream();
            this.clientSecrets = GoogleClientSecrets.load(JSON_FACTORY, new InputStreamReader(in));
            this.scopes = Arrays.asList(
                    DriveScopes.DRIVE, GmailScopes.GMAIL_READONLY, GmailScopes.GMAIL_SEND,
                    "https://www.googleapis.com/auth/userinfo.email", "https://www.googleapis.com/auth/userinfo.profile"
            );
        } catch (GeneralSecurityException | IOException e) {
            throw new RuntimeException("No se pudo inicializar el servicio de OAuth de Google", e);
        }
    }

    private AuthorizationCodeFlow createNewFlow() {
        return new GoogleAuthorizationCodeFlow.Builder(
                httpTransport, JSON_FACTORY, clientSecrets, scopes)
                .setAccessType("offline").setApprovalPrompt("force").build();
    }

    public String getAuthorizationUrl(String state) {
        return createNewFlow().newAuthorizationUrl().setRedirectUri(redirectUri).setState(state).build();
    }

    @Transactional
    public void processAuthorizationCode(String state, String authorizationCode) throws IOException {
        TokenResponse tokenResponse = createNewFlow().newTokenRequest(authorizationCode).setRedirectUri(redirectUri).execute();

        if (GLOBAL_DRIVE_TOKEN_ID.equals(state)) {
            GoogleGlobalCredential credential = new GoogleGlobalCredential(
                    GLOBAL_DRIVE_TOKEN_ID,
                    tokenResponse.getAccessToken(),
                    tokenResponse.getRefreshToken(),
                    System.currentTimeMillis() + (tokenResponse.getExpiresInSeconds() * 1000)
            );
            globalCredentialRepository.save(credential);
            log.info("Token global de Google Drive guardado en la base de datos.");
        } else {
            Long crmUserId = Long.parseLong(state);
            User crmUser = userRepository.findById(crmUserId).orElseThrow(() -> new IllegalStateException("Usuario del CRM no encontrado: " + crmUserId));

            Credential gCredential = new GoogleCredential.Builder().setTransport(httpTransport).setJsonFactory(JSON_FACTORY).setClientSecrets(clientSecrets).build().setFromTokenResponse(tokenResponse);
            Userinfo userInfo = new Oauth2.Builder(httpTransport, JSON_FACTORY, gCredential).setApplicationName("MIDAS CRM").build().userinfo().get().execute();

            crmUser.setGoogleAccessToken(tokenResponse.getAccessToken());
            crmUser.setGoogleRefreshToken(tokenResponse.getRefreshToken());
            crmUser.setGoogleTokenExpiryTime(System.currentTimeMillis() + (tokenResponse.getExpiresInSeconds() * 1000));
            crmUser.setGoogleAccountEmail(userInfo.getEmail());
            userRepository.save(crmUser);
            log.info("Credenciales de Google para {} guardadas para el usuario del CRM: {}", userInfo.getEmail(), crmUser.getUsername());
        }
    }

    public boolean hasValidCredentials() {
        return globalCredentialRepository.findById(GLOBAL_DRIVE_TOKEN_ID).isPresent();
    }

    @Transactional
    public Credential getValidCredential() throws IOException {
        GoogleGlobalCredential stored = globalCredentialRepository.findById(GLOBAL_DRIVE_TOKEN_ID)
                .orElseThrow(() -> new IllegalStateException("No hay credenciales globales de OAuth. Autorice la aplicación primero."));

        Credential credential = new GoogleCredential.Builder().setTransport(httpTransport).setJsonFactory(JSON_FACTORY).setClientSecrets(clientSecrets).build();

        credential.setAccessToken(stored.getAccessToken());
        credential.setRefreshToken(stored.getRefreshToken());
        credential.setExpiresInSeconds((stored.getExpiryTimeMillis() - System.currentTimeMillis()) / 1000);

        if (credential.getExpiresInSeconds() != null && credential.getExpiresInSeconds() <= 60) {
            log.info("Refrescando token de acceso global de Drive.");
            if (credential.refreshToken()) {
                stored.setAccessToken(credential.getAccessToken());
                stored.setExpiryTimeMillis(System.currentTimeMillis() + (credential.getExpiresInSeconds() * 1000));
                globalCredentialRepository.save(stored);
            }
        }
        return credential;
    }

    private InputStream getCredentialsInputStream() throws IOException {
        ClassPathResource resource = new ClassPathResource(credentialsFilePath);
        if (!resource.exists()) {
            throw new IOException("Archivo de credenciales no encontrado: " + credentialsFilePath);
        }
        return resource.getInputStream();
    }
}
package com.midas.crm.service;

import com.midas.crm.config.RequestTracingInterceptor;
import com.midas.crm.config.RequestTracingInterceptor.RequestInfo;
import com.midas.crm.entity.DTO.monitoring.MonitoringDTOs.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class RequestMonitoringService {

    private final RequestTracingInterceptor requestInterceptor;

    public ActiveRequestsDTO getActiveRequests() {
        Map<String, RequestInfo> activeRequests = requestInterceptor.getActiveRequests();
        return new ActiveRequestsDTO(
                activeRequests.size(),
                new Date(),
                activeRequests.values()
        );
    }

    public LongRunningRequestsDTO getLongRunningRequests() {
        Map<String, RequestInfo> longRequests = requestInterceptor.getLongRunningRequests();

        // Usamos Streams y Collectors para calcular estadísticas de forma más eficiente
        Map<String, Long> methodBreakdown = longRequests.values().stream()
                .collect(Collectors.groupingBy(RequestInfo::getMethod, Collectors.counting()));

        long maxDuration = longRequests.values().stream()
                .mapToLong(RequestInfo::getDuration)
                .max()
                .orElse(0);

        return new LongRunningRequestsDTO(
                longRequests.size(),
                maxDuration,
                new Date(),
                methodBreakdown,
                longRequests.values()
        );
    }

    public RequestStatsDTO getRequestStats() {
        Map<String, RequestInfo> activeRequests = requestInterceptor.getActiveRequests();
        Map<String, RequestInfo> longRequests = requestInterceptor.getLongRunningRequests();

        Map<String, Long> activeMethodBreakdown = activeRequests.values().stream()
                .collect(Collectors.groupingBy(RequestInfo::getMethod, Collectors.counting()));

        List<Map.Entry<String, Long>> topUris = activeRequests.values().stream()
                .collect(Collectors.groupingBy(RequestInfo::getUri, Collectors.counting()))
                .entrySet().stream()
                .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
                .limit(10)
                .toList();

        return new RequestStatsDTO(
                activeRequests.size(),
                longRequests.size(),
                new Date(),
                activeMethodBreakdown,
                topUris
        );
    }

    public String logLongRequestsAndGetResponse() {
        Map<String, RequestInfo> longRequests = requestInterceptor.getLongRunningRequests();
        if (longRequests.isEmpty()) {
            log.info("No se encontraron peticiones de larga duración para registrar.");
            return "0 peticiones largas encontradas.";
        }

        log.warn("=== INICIO: REPORTE MANUAL DE PETICIONES LARGAS ===");
        longRequests.values().forEach(req -> log.warn("LONG REQUEST (manual report): {}", req));
        log.warn("=== FIN: REPORTE MANUAL DE PETICIONES LARGAS ===");

        return longRequests.size() + " peticiones largas han sido registradas en los logs.";
    }
}
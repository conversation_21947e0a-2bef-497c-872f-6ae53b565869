package com.midas.crm.service.serviceImpl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.midas.crm.entity.Asistencia;
import com.midas.crm.entity.DTO.asistencia.AsistenciaCreateDTO;
import com.midas.crm.entity.DTO.asistencia.AsistenciaDTO;
import com.midas.crm.entity.DTO.asistencia.AsistenciaFilterDTO;
import com.midas.crm.entity.DTO.asistencia.AsistenciaUpdateDTO;
import com.midas.crm.entity.Sede;
import com.midas.crm.entity.User;
import com.midas.crm.mapper.AsistenciaMapper;
import com.midas.crm.repository.AsistenciaRepository;
import com.midas.crm.repository.SedeRepository;
import com.midas.crm.repository.UserRepository;
import com.midas.crm.service.AsistenciaService;
import com.midas.crm.utils.GenericResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class AsistenciaServiceImpl implements AsistenciaService {

    private final AsistenciaRepository asistenciaRepository;
    private final UserRepository userRepository;
    private final SedeRepository sedeRepository;

    // Constantes
    private static final int BREAK_LIMITE_MINUTOS = 15;
    private static final int BANO_LIMITE_MINUTOS = 10;
    private static final int TIEMPO_MINIMO_ACTIVIDAD = 1;

    @Override
    @Transactional
    public GenericResponse<AsistenciaDTO> registrarAsistencia(AsistenciaCreateDTO dto) {
        try {
            // Validar que el usuario existe
            if (!userRepository.existsById(dto.getUsuarioId())) {
                return new GenericResponse<>(0, "Usuario no encontrado", null);
            }

            Asistencia asistencia = AsistenciaMapper.toEntity(dto, userRepository);
            asistencia = asistenciaRepository.save(asistencia);

            AsistenciaDTO asistenciaDTO = AsistenciaMapper.toDTO(asistencia);

            log.info("Asistencia registrada exitosamente para usuario ID: {}", dto.getUsuarioId());
            return new GenericResponse<>(1, "Asistencia registrada exitosamente", asistenciaDTO);

        } catch (Exception e) {
            log.error("Error al registrar asistencia: {}", e.getMessage(), e);
            return new GenericResponse<>(0, "Error al registrar asistencia: " + e.getMessage(), null);
        }
    }

    @Override
    @Transactional
    public GenericResponse<AsistenciaDTO> registrarAsistenciaAutomatica(Long usuarioId, String ip, String dispositivo, String ubicacion) {
        try {
            // Verificar si ya registró asistencia hoy
            if (yaRegistroAsistenciaHoy(usuarioId)) {
                log.info("Usuario {} ya registró asistencia hoy", usuarioId);
                return new GenericResponse<>(0, "Ya registró asistencia hoy", null);
            }

            AsistenciaCreateDTO dto = new AsistenciaCreateDTO();
            dto.setUsuarioId(usuarioId);
            dto.setFechaHoraEntrada(LocalDateTime.now());
            dto.setIpEntrada(ip);
            dto.setDispositivoEntrada(dispositivo);
            dto.setUbicacionEntrada(ubicacion);
            dto.setTipoActividad(Asistencia.TipoActividad.ENTRADA);
            dto.setSubtipoActividad(Asistencia.SubtipoActividad.AUTOMATICA);
            dto.setObservaciones("Registro automático al iniciar sesión");

            return registrarAsistencia(dto);

        } catch (Exception e) {
            log.error("Error al registrar asistencia automática: {}", e.getMessage(), e);
            return new GenericResponse<>(0, "Error al registrar asistencia automática: " + e.getMessage(), null);
        }
    }

    @Override
    public GenericResponse<Page<AsistenciaDTO>> obtenerAsistenciasConFiltros(AsistenciaFilterDTO filtros) {
        try {
            log.info("🔥 Obteniendo asistencias con filtros: {}", filtros);

            // Configurar paginación y ordenamiento
            Sort sort = Sort.by(Sort.Direction.fromString(filtros.getSortDirection()), filtros.getSortBy());
            Pageable pageable = PageRequest.of(filtros.getPage(), filtros.getSize(), sort);

            // Convertir fechas a LocalDateTime si es necesario
            LocalDateTime fechaInicio = null;
            LocalDateTime fechaFin = null;

            if (filtros.getFechaInicio() != null) {
                fechaInicio = filtros.getFechaInicio().atStartOfDay();
            }
            if (filtros.getFechaFin() != null) {
                fechaFin = filtros.getFechaFin().atTime(LocalTime.MAX);
            }

            // Usar fechaHoraInicio y fechaHoraFin si están disponibles
            if (filtros.getFechaHoraInicio() != null) {
                fechaInicio = filtros.getFechaHoraInicio();
            }
            if (filtros.getFechaHoraFin() != null) {
                fechaFin = filtros.getFechaHoraFin();
            }

            log.info("📊 Buscando entre {} y {} para sede {}", fechaInicio, fechaFin, filtros.getSedeId());

            Page<Asistencia> asistenciasPage = asistenciaRepository.findWithFilters(
                    filtros.getUsuarioId(),
                    filtros.getUsuarioSearch(),
                    fechaInicio,
                    fechaFin,
                    filtros.getTipoActividad(),
                    filtros.getEstado(),
                    filtros.getSedeId(),
                    pageable
            );

            Page<AsistenciaDTO> asistenciasDTOPage = asistenciasPage.map(AsistenciaMapper::toDTO);

            log.info("✅ Encontradas {} asistencias (página {}/{})",
                    asistenciasDTOPage.getTotalElements(),
                    asistenciasDTOPage.getNumber() + 1,
                    asistenciasDTOPage.getTotalPages());

            return new GenericResponse<>(1, "Asistencias obtenidas exitosamente", asistenciasDTOPage);

        } catch (Exception e) {
            log.error("❌ Error al obtener asistencias con filtros: {}", e.getMessage(), e);
            return new GenericResponse<>(0, "Error al obtener asistencias: " + e.getMessage(), null);
        }
    }

    @Override
    public GenericResponse<List<AsistenciaDTO>> obtenerAsistenciasPorUsuario(Long usuarioId) {
        try {
            List<Asistencia> asistencias = asistenciaRepository.findByUsuarioIdOrderByFechaHoraEntradaDesc(usuarioId);
            List<AsistenciaDTO> asistenciasDTO = AsistenciaMapper.toDTOList(asistencias);

            return new GenericResponse<>(1, "Asistencias obtenidas exitosamente", asistenciasDTO);

        } catch (Exception e) {
            log.error("Error al obtener asistencias por usuario: {}", e.getMessage(), e);
            return new GenericResponse<>(0, "Error al obtener asistencias: " + e.getMessage(), null);
        }
    }

    @Override
    public GenericResponse<List<AsistenciaDTO>> obtenerAsistenciasPorFecha(LocalDate fecha) {
        try {
            log.debug("Obteniendo asistencias para la fecha: {}", fecha);

            if (fecha == null) {
                return new GenericResponse<>(0, "Fecha es requerida", null);
            }

            List<Asistencia> asistencias = asistenciaRepository.findByFecha(fecha);
            List<AsistenciaDTO> asistenciasDTO = AsistenciaMapper.toDTOList(asistencias);

            log.debug("Se encontraron {} asistencias para la fecha {}", asistencias.size(), fecha);
            return new GenericResponse<>(1, "Asistencias obtenidas exitosamente", asistenciasDTO);

        } catch (Exception e) {
            log.error("Error al obtener asistencias por fecha {}: {}", fecha, e.getMessage(), e);
            return new GenericResponse<>(0, "Error al obtener asistencias: " + e.getMessage(), null);
        }
    }

    @Override
    public GenericResponse<List<AsistenciaDTO>> obtenerAsistenciasPorUsuarioYFechas(Long usuarioId, LocalDate fechaInicio, LocalDate fechaFin) {
        try {
            log.debug("Obteniendo asistencias para usuario {} entre {} y {}", usuarioId, fechaInicio, fechaFin);

            if (usuarioId == null) {
                return new GenericResponse<>(0, "ID de usuario es requerido", null);
            }

            if (fechaInicio == null || fechaFin == null) {
                return new GenericResponse<>(0, "Fechas de inicio y fin son requeridas", null);
            }

            // Verificar que el usuario existe
            if (!userRepository.existsById(usuarioId)) {
                return new GenericResponse<>(0, "Usuario no encontrado", null);
            }

            LocalDateTime fechaHoraInicio = fechaInicio.atStartOfDay();
            LocalDateTime fechaHoraFin = fechaFin.atTime(LocalTime.MAX);

            List<Asistencia> asistencias = asistenciaRepository.findByUsuarioIdAndFechaHoraEntradaBetween(
                    usuarioId, fechaHoraInicio, fechaHoraFin);
            List<AsistenciaDTO> asistenciasDTO = AsistenciaMapper.toDTOList(asistencias);

            log.debug("Se encontraron {} asistencias para usuario {} entre {} y {}",
                    asistencias.size(), usuarioId, fechaInicio, fechaFin);

            return new GenericResponse<>(1, "Asistencias obtenidas exitosamente", asistenciasDTO);

        } catch (Exception e) {
            log.error("Error al obtener asistencias por usuario {} y fechas {}-{}: {}",
                    usuarioId, fechaInicio, fechaFin, e.getMessage(), e);
            return new GenericResponse<>(0, "Error al obtener asistencias: " + e.getMessage(), null);
        }
    }

    @Override
    public GenericResponse<Map<String, Object>> obtenerEstadisticasPorFecha(LocalDate fechaInicio, LocalDate fechaFin) {
        try {
            LocalDateTime fechaHoraInicio = fechaInicio.atStartOfDay();
            LocalDateTime fechaHoraFin = fechaFin.atTime(LocalTime.MAX);

            List<Object[]> estadisticas = asistenciaRepository.getEstadisticasPorFecha(fechaHoraInicio, fechaHoraFin);

            Map<String, Object> resultado = new HashMap<>();
            resultado.put("estadisticasPorFecha", estadisticas);
            resultado.put("fechaInicio", fechaInicio);
            resultado.put("fechaFin", fechaFin);

            return new GenericResponse<>(1, "Estadísticas obtenidas exitosamente", resultado);

        } catch (Exception e) {
            log.error("Error al obtener estadísticas por fecha: {}", e.getMessage(), e);
            return new GenericResponse<>(0, "Error al obtener estadísticas: " + e.getMessage(), null);
        }
    }

    @Override
    @Transactional
    public GenericResponse<AsistenciaDTO> registrarSalida(Long usuarioId, String ip, String dispositivo, String ubicacion) {
        try {
            // Buscar SOLO la asistencia de ENTRADA del día actual
            LocalDate hoy = LocalDate.now();
            LocalDateTime inicioDelDia = hoy.atStartOfDay();
            LocalDateTime finDelDia = hoy.atTime(LocalTime.MAX);

            Optional<Asistencia> entradaHoyOpt = asistenciaRepository.findByUsuarioIdAndFechaHoraEntradaBetween(
                            usuarioId, inicioDelDia, finDelDia
                    ).stream()
                    .filter(a -> a.getTipoActividad() == Asistencia.TipoActividad.ENTRADA)
                    .findFirst();

            if (entradaHoyOpt.isEmpty()) {
                return new GenericResponse<>(0, "No se encontró registro de entrada para hoy", null);
            }

            Asistencia entradaHoy = entradaHoyOpt.get();

            // Verificar si YA TIENE fecha de salida registrada
            if (entradaHoy.getFechaHoraSalida() != null) {
                log.warn("⚠️ Usuario {} ya tiene salida registrada: {}", usuarioId, entradaHoy.getFechaHoraSalida());
                return new GenericResponse<>(0, "Ya se registró la salida para hoy", null);
            }

            LocalDateTime ahora = LocalDateTime.now();
            LocalDateTime entrada = entradaHoy.getFechaHoraEntrada();

            // Calcular duración total en oficina
            long duracionTotalMinutos = Duration.between(entrada, ahora).toMinutes();

            // Calcular tiempo real de breaks y baños (auto-finaliza pendientes)
            long tiempoDescansosMinutos = calcularTiempoDescansos(usuarioId, entrada.toLocalDate());

            // Tiempo neto trabajado = Total - Descansos
            long tiempoNetoTrabajado = duracionTotalMinutos - tiempoDescansosMinutos;

            // Actualizar la misma asistencia de ENTRADA con datos de salida
            entradaHoy.setFechaHoraSalida(ahora);
            entradaHoy.setIpSalida(ip);
            entradaHoy.setDispositivoSalida(dispositivo);
            entradaHoy.setUbicacionSalida(ubicacion);

            // Guardar duraciones calculadas correctamente
            entradaHoy.setDuracionMinutos((int) tiempoNetoTrabajado);
            entradaHoy.setTiempoSesionMinutos((int) duracionTotalMinutos);

            // Observaciones más claras
            String observacionesActuales = entradaHoy.getObservaciones() != null ? entradaHoy.getObservaciones() : "";
            entradaHoy.setObservaciones(observacionesActuales + String.format(
                    " | Salida: Total en oficina: %d min (%.1fh), Descansos: %d min, Tiempo trabajado: %d min (%.1fh)",
                    duracionTotalMinutos, duracionTotalMinutos / 60.0,
                    tiempoDescansosMinutos,
                    tiempoNetoTrabajado, tiempoNetoTrabajado / 60.0
            ));

            entradaHoy = asistenciaRepository.save(entradaHoy);
            AsistenciaDTO asistenciaDTO = AsistenciaMapper.toDTO(entradaHoy);

            log.info("✅ Salida registrada para usuario ID: {} - Total oficina: {} min, Trabajado: {} min, Descansos: {} min",
                    usuarioId, duracionTotalMinutos, tiempoNetoTrabajado, tiempoDescansosMinutos);

            return new GenericResponse<>(1, "Salida registrada exitosamente", asistenciaDTO);

        } catch (Exception e) {
            log.error("❌ Error al registrar salida para usuario {}: {}", usuarioId, e.getMessage(), e);
            return new GenericResponse<>(0, "Error al registrar salida: " + e.getMessage(), null);
        }
    }

    @Override
    public boolean yaMarcoSalidaHoy(Long usuarioId) {
        try {
            LocalDate hoy = LocalDate.now();
            LocalDateTime inicioDelDia = hoy.atStartOfDay();
            LocalDateTime finDelDia = hoy.atTime(LocalTime.MAX);

            // Buscar entrada del día y verificar si tiene fechaHoraSalida
            Optional<Asistencia> entradaHoy = asistenciaRepository.findByUsuarioIdAndFechaHoraEntradaBetween(
                            usuarioId, inicioDelDia, finDelDia
                    ).stream()
                    .filter(a -> a.getTipoActividad() == Asistencia.TipoActividad.ENTRADA)
                    .findFirst();

            if (entradaHoy.isPresent()) {
                boolean yaMarcoSalida = entradaHoy.get().getFechaHoraSalida() != null;
                log.debug("Usuario {} {} marcó salida hoy", usuarioId, yaMarcoSalida ? "SÍ" : "NO");
                return yaMarcoSalida;
            }

            log.debug("Usuario {} no tiene entrada registrada hoy", usuarioId);
            return false;

        } catch (Exception e) {
            log.error("Error al verificar salida del día para usuario {}: {}", usuarioId, e.getMessage());
            return false;
        }
    }

    /**
     * 🔥 MÉTODO CORREGIDO: Calcular tiempo de breaks y baños del día
     */
    private long calcularTiempoDescansos(Long usuarioId, LocalDate fecha) {
        try {
            LocalDateTime inicioDelDia = fecha.atStartOfDay();
            LocalDateTime finDelDia = fecha.atTime(LocalTime.MAX);

            // Obtener todas las asistencias del día que sean breaks o baños FINALIZADOS
            List<Asistencia> breaksBanosFinalizados = asistenciaRepository.findByUsuarioIdAndFechaHoraEntradaBetween(
                            usuarioId, inicioDelDia, finDelDia
                    ).stream()
                    .filter(a -> (a.getTipoActividad() == Asistencia.TipoActividad.BREAK ||
                            a.getTipoActividad() == Asistencia.TipoActividad.BANO) &&
                            a.getFechaHoraSalida() != null)
                    .collect(Collectors.toList());

            long tiempoTotalMinutos = 0;

            for (Asistencia actividad : breaksBanosFinalizados) {
                // Calcular duración real de cada actividad finalizada
                long duracion = Duration.between(
                        actividad.getFechaHoraEntrada(),
                        actividad.getFechaHoraSalida()
                ).toMinutes();
                tiempoTotalMinutos += duracion;

                log.debug("Actividad {} finalizada: {} minutos",
                        actividad.getTipoActividad(), duracion);
            }

            // Verificar si hay actividades activas SIN finalizar
            List<Asistencia> actividadesActivas = asistenciaRepository.findByUsuarioIdAndFechaHoraEntradaBetween(
                            usuarioId, inicioDelDia, finDelDia
                    ).stream()
                    .filter(a -> (a.getTipoActividad() == Asistencia.TipoActividad.BREAK ||
                            a.getTipoActividad() == Asistencia.TipoActividad.BANO) &&
                            a.getFechaHoraSalida() == null)
                    .collect(Collectors.toList());

            if (!actividadesActivas.isEmpty()) {
                log.warn("⚠️ Usuario {} tiene {} actividades SIN finalizar al marcar salida",
                        usuarioId, actividadesActivas.size());

                // Auto-finalizar actividades pendientes al marcar salida
                LocalDateTime ahora = LocalDateTime.now();
                for (Asistencia actividadActiva : actividadesActivas) {
                    actividadActiva.setFechaHoraSalida(ahora);
                    actividadActiva.setObservaciones(actividadActiva.getObservaciones() +
                            " - Auto-finalizada al marcar salida");

                    long duracion = Duration.between(
                            actividadActiva.getFechaHoraEntrada(), ahora
                    ).toMinutes();

                    actividadActiva.setDuracionMinutos((int) duracion);
                    asistenciaRepository.save(actividadActiva);

                    tiempoTotalMinutos += duracion;

                    log.info("🔧 Auto-finalizada actividad {}: {} minutos",
                            actividadActiva.getTipoActividad(), duracion);
                }
            }

            log.info("📊 Tiempo total de descansos para usuario {}: {} minutos",
                    usuarioId, tiempoTotalMinutos);
            return tiempoTotalMinutos;

        } catch (Exception e) {
            log.error("Error al calcular tiempo de descansos: {}", e.getMessage());
            return 0;
        }
    }

    @Override
    public GenericResponse<AsistenciaDTO> obtenerAsistenciaPorId(Long id) {
        try {
            Optional<Asistencia> asistenciaOpt = asistenciaRepository.findById(id);

            if (asistenciaOpt.isEmpty()) {
                return new GenericResponse<>(0, "Asistencia no encontrada", null);
            }

            AsistenciaDTO asistenciaDTO = AsistenciaMapper.toDTO(asistenciaOpt.get());
            return new GenericResponse<>(1, "Asistencia obtenida exitosamente", asistenciaDTO);

        } catch (Exception e) {
            log.error("Error al obtener asistencia por ID: {}", e.getMessage(), e);
            return new GenericResponse<>(0, "Error al obtener asistencia: " + e.getMessage(), null);
        }
    }

    @Override
    public boolean yaRegistroAsistenciaHoy(Long usuarioId) {
        try {
            log.debug("Verificando si usuario {} ya registró asistencia hoy", usuarioId);

            if (usuarioId == null) {
                log.warn("ID de usuario es null al verificar asistencia del día");
                return false;
            }

            boolean resultado = asistenciaRepository.existsAsistenciaHoyByUsuarioId(usuarioId);
            log.debug("Usuario {} {} registró asistencia hoy", usuarioId, resultado ? "SÍ" : "NO");

            return resultado;
        } catch (Exception e) {
            log.error("Error al verificar asistencia del día para usuario {}: {}", usuarioId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional
    public GenericResponse<AsistenciaDTO> iniciarBreak(Long usuarioId, String ip, String dispositivo, String ubicacion) {
        try {
            // Buscar la asistencia de ENTRADA del día
            Optional<Asistencia> asistenciaHoyOpt = obtenerAsistenciaDelDia(usuarioId);

            if (asistenciaHoyOpt.isEmpty()) {
                return new GenericResponse<>(0, "No se encontró registro de entrada para hoy", null);
            }

            Asistencia asistenciaHoy = asistenciaHoyOpt.get();

            // Validaciones
            if (asistenciaHoy.getBreakActivo()) {
                return new GenericResponse<>(0, "Ya tiene un break activo", null);
            }

            if (asistenciaHoy.getBreakContador() >= 2) {
                return new GenericResponse<>(0, "Ya alcanzó el límite de breaks por día (máximo 2)", null);
            }

            if (asistenciaHoy.getBanoActivo()) {
                return new GenericResponse<>(0, "Debe finalizar el baño antes de tomar un break", null);
            }

            // Actualizar solo el registro de entrada
            LocalDateTime ahora = LocalDateTime.now();
            asistenciaHoy.setBreakActivo(true);
            asistenciaHoy.setBreakInicioActual(ahora);
            asistenciaHoy.setEstadoActual(Asistencia.EstadoActual.EN_BREAK);

            // Actualizar historial JSON
            actualizarHistorialBreaks(asistenciaHoy, ahora, null, "INICIO");

            asistenciaHoy = asistenciaRepository.save(asistenciaHoy);
            AsistenciaDTO dto = AsistenciaMapper.toDTO(asistenciaHoy);

            log.info("✅ Break iniciado para usuario {}: {}", usuarioId, ahora);
            return new GenericResponse<>(1, "Break iniciado correctamente", dto);

        } catch (Exception e) {
            log.error("❌ Error al iniciar break: {}", e.getMessage(), e);
            return new GenericResponse<>(0, "Error al iniciar break: " + e.getMessage(), null);
        }
    }

    @Override
    @Transactional
    public GenericResponse<AsistenciaDTO> finalizarBreak(Long usuarioId, String ip, String dispositivo, String ubicacion) {
        try {
            Optional<Asistencia> asistenciaHoyOpt = obtenerAsistenciaDelDia(usuarioId);

            if (asistenciaHoyOpt.isEmpty()) {
                return new GenericResponse<>(0, "No se encontró registro de entrada para hoy", null);
            }

            Asistencia asistenciaHoy = asistenciaHoyOpt.get();

            if (!asistenciaHoy.getBreakActivo()) {
                return new GenericResponse<>(0, "No hay break activo para finalizar", null);
            }

            LocalDateTime ahora = LocalDateTime.now();
            LocalDateTime inicioBreak = asistenciaHoy.getBreakInicioActual();

            // Calcular duración del break
            long duracionMinutos = Duration.between(inicioBreak, ahora).toMinutes();

            // Validar tiempo mínimo
            if (duracionMinutos < 1) {
                return new GenericResponse<>(0, "El break debe durar al menos 1 minuto", null);
            }

            // Actualizar contadores en el mismo registro
            asistenciaHoy.setBreakActivo(false);
            asistenciaHoy.setBreakInicioActual(null);
            asistenciaHoy.setBreakContador(asistenciaHoy.getBreakContador() + 1);
            asistenciaHoy.setBreakTiempoTotalMinutos(
                    asistenciaHoy.getBreakTiempoTotalMinutos() + (int) duracionMinutos
            );
            asistenciaHoy.setEstadoActual(Asistencia.EstadoActual.TRABAJANDO);

            // Actualizar historial JSON
            actualizarHistorialBreaks(asistenciaHoy, inicioBreak, ahora, "FIN");

            // Recalcular tiempo neto trabajado
            recalcularTiempoNetoTrabajado(asistenciaHoy);

            asistenciaHoy = asistenciaRepository.save(asistenciaHoy);
            AsistenciaDTO dto = AsistenciaMapper.toDTO(asistenciaHoy);

            String mensaje = duracionMinutos > 15 ?
                    String.format("Break finalizado (EXCEDIÓ límite: %d min)", duracionMinutos) :
                    String.format("Break finalizado correctamente (%d min)", duracionMinutos);

            log.info("✅ Break finalizado para usuario {}: {} min", usuarioId, duracionMinutos);
            return new GenericResponse<>(1, mensaje, dto);

        } catch (Exception e) {
            log.error("❌ Error al finalizar break: {}", e.getMessage(), e);
            return new GenericResponse<>(0, "Error al finalizar break: " + e.getMessage(), null);
        }
    }

    /**
     * 🔥 CORREGIDO: Recalcular tiempo neto trabajado con validaciones
     */
    private void recalcularTiempoNetoTrabajado(Asistencia asistencia) {
        try {
            if (asistencia.getFechaHoraEntrada() != null) {
                LocalDateTime entrada = asistencia.getFechaHoraEntrada();
                LocalDateTime salida = asistencia.getFechaHoraSalida() != null ?
                        asistencia.getFechaHoraSalida() : LocalDateTime.now();

                // Tiempo total en oficina
                long tiempoTotalMinutos = Duration.between(entrada, salida).toMinutes();

                // Tiempo de descansos con validación
                int tiempoBreaks = asistencia.getBreakTiempoTotalMinutos() != null ?
                        asistencia.getBreakTiempoTotalMinutos() : 0;
                int tiempoBanos = asistencia.getBanoTiempoTotalMinutos() != null ?
                        asistencia.getBanoTiempoTotalMinutos() : 0;
                int tiempoDescansos = tiempoBreaks + tiempoBanos;

                // Tiempo neto trabajado
                long tiempoNeto = Math.max(0, tiempoTotalMinutos - tiempoDescansos);

                // Actualizar campos
                asistencia.setTiempoNetoTrabajadoMinutos((int) tiempoNeto);
                asistencia.setTiempoSesionMinutos((int) tiempoTotalMinutos);
                asistencia.setPorcentajeJornada(Math.min(100.0, (tiempoNeto / 480.0) * 100));

                log.debug("✅ Tiempo recalculado para asistencia {}: {} min neto de {} min total (Breaks: {} min, Baños: {} min)",
                        asistencia.getId(), tiempoNeto, tiempoTotalMinutos, tiempoBreaks, tiempoBanos);
            }
        } catch (Exception e) {
            log.error("❌ Error al recalcular tiempo trabajado: {}", e.getMessage());
        }
    }

    @Override
    public GenericResponse<Map<String, Object>> obtenerEstadoCompleto(Long usuarioId) {
        try {
            Optional<Asistencia> asistenciaOpt = obtenerAsistenciaDelDia(usuarioId);

            if (asistenciaOpt.isEmpty()) {
                return new GenericResponse<>(0, "No hay registro de entrada para hoy", null);
            }

            Asistencia asistencia = asistenciaOpt.get();
            Map<String, Object> estado = new HashMap<>();

            // Estados de actividades
            estado.put("breakActivo", asistencia.getBreakActivo());
            estado.put("banoActivo", asistencia.getBanoActivo());
            estado.put("estadoActual", asistencia.getEstadoActual());

            // Contadores
            estado.put("totalBreaks", asistencia.getBreakContador());
            estado.put("totalBanos", asistencia.getBanoContador());
            estado.put("puedeTomarBreak", asistencia.getBreakContador() < 2);
            estado.put("puedeIrAlBano", asistencia.getBanoContador() < 2);

            // Tiempos
            estado.put("tiempoBreaksMinutos", asistencia.getBreakTiempoTotalMinutos());
            estado.put("tiempoBanosMinutos", asistencia.getBanoTiempoTotalMinutos());
            estado.put("tiempoNetoTrabajado", asistencia.getTiempoNetoTrabajadoMinutos());
            estado.put("porcentajeJornada", asistencia.getPorcentajeJornada());

            // Si hay actividad activa, calcular tiempo transcurrido
            if (asistencia.getBreakActivo() && asistencia.getBreakInicioActual() != null) {
                long minutosBreakActual = Duration.between(
                        asistencia.getBreakInicioActual(), LocalDateTime.now()
                ).toMinutes();
                estado.put("breakTiempoActual", minutosBreakActual);
            }

            if (asistencia.getBanoActivo() && asistencia.getBanoInicioActual() != null) {
                long minutosBanoActual = Duration.between(
                        asistencia.getBanoInicioActual(), LocalDateTime.now()
                ).toMinutes();
                estado.put("banoTiempoActual", minutosBanoActual);
            }

            return new GenericResponse<>(1, "Estado obtenido exitosamente", estado);

        } catch (Exception e) {
            log.error("Error al obtener estado completo: {}", e.getMessage(), e);
            return new GenericResponse<>(0, "Error al obtener estado: " + e.getMessage(), null);
        }
    }

    private Optional<Asistencia> obtenerAsistenciaDelDia(Long usuarioId) {
        LocalDate hoy = LocalDate.now();
        LocalDateTime inicioDelDia = hoy.atStartOfDay();
        LocalDateTime finDelDia = hoy.atTime(LocalTime.MAX);

        return asistenciaRepository.findByUsuarioIdAndFechaHoraEntradaBetween(
                        usuarioId, inicioDelDia, finDelDia
                ).stream()
                .filter(a -> a.getTipoActividad() == Asistencia.TipoActividad.ENTRADA)
                .findFirst();
    }

    private void actualizarHistorialBreaks(Asistencia asistencia, LocalDateTime inicio, LocalDateTime fin, String tipo) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            List<Map<String, Object>> historial;

            // Obtener historial existente o crear uno nuevo
            if (asistencia.getBreaksHistorial() != null) {
                historial = mapper.readValue(asistencia.getBreaksHistorial(), List.class);
            } else {
                historial = new ArrayList<>();
            }

            if ("INICIO".equals(tipo)) {
                // Agregar nuevo break al historial
                Map<String, Object> nuevoBreak = new HashMap<>();
                nuevoBreak.put("inicio", inicio.format(DateTimeFormatter.ofPattern("HH:mm:ss")));
                nuevoBreak.put("fecha_inicio", inicio.toString());
                historial.add(nuevoBreak);
            } else if ("FIN".equals(tipo) && !historial.isEmpty()) {
                // Completar el último break
                Map<String, Object> ultimoBreak = historial.get(historial.size() - 1);
                ultimoBreak.put("fin", fin.format(DateTimeFormatter.ofPattern("HH:mm:ss")));
                ultimoBreak.put("fecha_fin", fin.toString());
                ultimoBreak.put("duracion_minutos", Duration.between(inicio, fin).toMinutes());
            }

            asistencia.setBreaksHistorial(mapper.writeValueAsString(historial));

        } catch (Exception e) {
            log.error("Error al actualizar historial de breaks: {}", e.getMessage());
        }
    }

    @Override
    public boolean puedeTomarBreak(Long usuarioId) {
        try {
            log.debug("Verificando si usuario {} puede tomar break", usuarioId);

            if (usuarioId == null) {
                log.warn("ID de usuario es null al verificar límite de breaks");
                return false;
            }

            int breaksHoy = asistenciaRepository.contarBreaksHoy(usuarioId);
            boolean puede = breaksHoy < 2;

            log.debug("Usuario {} tiene {} breaks hoy, {} tomar más",
                    usuarioId, breaksHoy, puede ? "PUEDE" : "NO PUEDE");

            return puede;
        } catch (Exception e) {
            log.error("Error al verificar límite de breaks para usuario {}: {}", usuarioId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean puedeIrAlBano(Long usuarioId) {
        try {
            log.debug("Verificando si usuario {} puede ir al baño", usuarioId);

            if (usuarioId == null) {
                log.warn("ID de usuario es null al verificar límite de baños");
                return false;
            }

            int banosHoy = asistenciaRepository.contarBanosHoy(usuarioId);
            boolean puede = banosHoy < 2;

            log.debug("Usuario {} tiene {} idas al baño hoy, {} ir más",
                    usuarioId, banosHoy, puede ? "PUEDE" : "NO PUEDE");

            return puede;
        } catch (Exception e) {
            log.error("Error al verificar límite de baños para usuario {}: {}", usuarioId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean tieneActividadActiva(Long usuarioId, Asistencia.TipoActividad tipoActividad) {
        try {
            Optional<Asistencia> actividadActiva = asistenciaRepository.findActividadActivaHoy(usuarioId, tipoActividad);
            return actividadActiva.isPresent();
        } catch (Exception e) {
            log.error("Error al verificar actividad activa: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional
    public GenericResponse<AsistenciaDTO> iniciarBano(Long usuarioId, String ip, String dispositivo, String ubicacion) {
        try {
            Optional<Asistencia> asistenciaHoyOpt = obtenerAsistenciaDelDia(usuarioId);

            if (asistenciaHoyOpt.isEmpty()) {
                return new GenericResponse<>(0, "No se encontró registro de entrada para hoy", null);
            }

            Asistencia asistenciaHoy = asistenciaHoyOpt.get();

            // Validaciones
            if (asistenciaHoy.getBanoActivo()) {
                return new GenericResponse<>(0, "Ya tiene una ida al baño activa", null);
            }

            if (asistenciaHoy.getBanoContador() >= 2) {
                return new GenericResponse<>(0, "Ya alcanzó el límite de idas al baño por día (máximo 2)", null);
            }

            if (asistenciaHoy.getBreakActivo()) {
                return new GenericResponse<>(0, "Debe finalizar el break antes de ir al baño", null);
            }

            LocalDateTime ahora = LocalDateTime.now();
            asistenciaHoy.setBanoActivo(true);
            asistenciaHoy.setBanoInicioActual(ahora);
            asistenciaHoy.setEstadoActual(Asistencia.EstadoActual.EN_BANO);

            // Actualizar historial JSON
            actualizarHistorialBanos(asistenciaHoy, ahora, null, "INICIO");

            asistenciaHoy = asistenciaRepository.save(asistenciaHoy);
            AsistenciaDTO dto = AsistenciaMapper.toDTO(asistenciaHoy);

            log.info("✅ Baño iniciado para usuario {}: {}", usuarioId, ahora);
            return new GenericResponse<>(1, "Ida al baño iniciada correctamente", dto);

        } catch (Exception e) {
            log.error("❌ Error al iniciar ida al baño: {}", e.getMessage(), e);
            return new GenericResponse<>(0, "Error al iniciar ida al baño: " + e.getMessage(), null);
        }
    }

    @Override
    @Transactional
    public GenericResponse<AsistenciaDTO> finalizarBano(Long usuarioId, String ip, String dispositivo, String ubicacion) {
        try {
            Optional<Asistencia> asistenciaHoyOpt = obtenerAsistenciaDelDia(usuarioId);

            if (asistenciaHoyOpt.isEmpty()) {
                return new GenericResponse<>(0, "No se encontró registro de entrada para hoy", null);
            }

            Asistencia asistenciaHoy = asistenciaHoyOpt.get();

            if (!asistenciaHoy.getBanoActivo()) {
                return new GenericResponse<>(0, "No hay ida al baño activa para finalizar", null);
            }

            LocalDateTime ahora = LocalDateTime.now();
            LocalDateTime inicioBano = asistenciaHoy.getBanoInicioActual();

            // Calcular duración del baño
            long duracionMinutos = Duration.between(inicioBano, ahora).toMinutes();

            // Validar tiempo mínimo
            if (duracionMinutos < 1) {
                return new GenericResponse<>(0, "La ida al baño debe durar al menos 1 minuto", null);
            }

            // Actualizar contadores en el mismo registro
            asistenciaHoy.setBanoActivo(false);
            asistenciaHoy.setBanoInicioActual(null);
            asistenciaHoy.setBanoContador(asistenciaHoy.getBanoContador() + 1);
            asistenciaHoy.setBanoTiempoTotalMinutos(
                    asistenciaHoy.getBanoTiempoTotalMinutos() + (int) duracionMinutos
            );
            asistenciaHoy.setEstadoActual(Asistencia.EstadoActual.TRABAJANDO);

            // Actualizar historial JSON
            actualizarHistorialBanos(asistenciaHoy, inicioBano, ahora, "FIN");

            // Recalcular tiempo neto trabajado
            recalcularTiempoNetoTrabajado(asistenciaHoy);

            asistenciaHoy = asistenciaRepository.save(asistenciaHoy);
            AsistenciaDTO dto = AsistenciaMapper.toDTO(asistenciaHoy);

            String mensaje = duracionMinutos > 10 ?
                    String.format("Ida al baño finalizada (EXCEDIÓ límite: %d min)", duracionMinutos) :
                    String.format("Ida al baño finalizada correctamente (%d min)", duracionMinutos);

            log.info("✅ Baño finalizado para usuario {}: {} min", usuarioId, duracionMinutos);
            return new GenericResponse<>(1, mensaje, dto);

        } catch (Exception e) {
            log.error("❌ Error al finalizar ida al baño: {}", e.getMessage(), e);
            return new GenericResponse<>(0, "Error al finalizar ida al baño: " + e.getMessage(), null);
        }
    }

    private void actualizarHistorialBanos(Asistencia asistencia, LocalDateTime inicio, LocalDateTime fin, String tipo) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            List<Map<String, Object>> historial;

            if (asistencia.getBanosHistorial() != null) {
                historial = mapper.readValue(asistencia.getBanosHistorial(), List.class);
            } else {
                historial = new ArrayList<>();
            }

            if ("INICIO".equals(tipo)) {
                Map<String, Object> nuevoBano = new HashMap<>();
                nuevoBano.put("inicio", inicio.format(DateTimeFormatter.ofPattern("HH:mm:ss")));
                nuevoBano.put("fecha_inicio", inicio.toString());
                historial.add(nuevoBano);
            } else if ("FIN".equals(tipo) && !historial.isEmpty()) {
                Map<String, Object> ultimoBano = historial.get(historial.size() - 1);
                ultimoBano.put("fin", fin.format(DateTimeFormatter.ofPattern("HH:mm:ss")));
                ultimoBano.put("fecha_fin", fin.toString());
                ultimoBano.put("duracion_minutos", Duration.between(inicio, fin).toMinutes());
            }

            asistencia.setBanosHistorial(mapper.writeValueAsString(historial));

        } catch (Exception e) {
            log.error("Error al actualizar historial de baños: {}", e.getMessage());
        }
    }

    @Override
    public GenericResponse<Map<String, Object>> obtenerEstadoActividades(Long usuarioId) {
        try {
            Map<String, Object> estado = new HashMap<>();

            Optional<Asistencia> asistenciaOpt = obtenerAsistenciaDelDia(usuarioId);

            if (asistenciaOpt.isEmpty()) {
                estado.put("breakActivo", false);
                estado.put("banoActivo", false);
                estado.put("totalBreaks", 0);
                estado.put("totalBanos", 0);
                estado.put("puedeTomarBreak", false);
                estado.put("puedeIrAlBano", false);
                estado.put("horaSalida", null);
                estado.put("mensaje", "No hay registro de entrada para hoy");

                return new GenericResponse<>(1, "Estado obtenido (sin entrada)", estado);
            }

            Asistencia asistencia = asistenciaOpt.get();

            // Estados de actividades actuales
            estado.put("breakActivo", asistencia.getBreakActivo());
            estado.put("banoActivo", asistencia.getBanoActivo());
            estado.put("estadoActual", asistencia.getEstadoActual());

            // Contadores
            estado.put("totalBreaks", asistencia.getBreakContador());
            estado.put("totalBanos", asistencia.getBanoContador());

            // Permisos (considerando actividades activas)
            boolean puedeBreak = asistencia.getBreakContador() < 2 &&
                    !asistencia.getBreakActivo() &&
                    !asistencia.getBanoActivo();
            boolean puedeBano = asistencia.getBanoContador() < 2 &&
                    !asistencia.getBanoActivo() &&
                    !asistencia.getBreakActivo();

            estado.put("puedeTomarBreak", puedeBreak);
            estado.put("puedeIrAlBano", puedeBano);

            // Tiempos
            estado.put("tiempoBreaksMinutos", asistencia.getBreakTiempoTotalMinutos());
            estado.put("tiempoBanosMinutos", asistencia.getBanoTiempoTotalMinutos());
            estado.put("tiempoNetoTrabajado", asistencia.getTiempoNetoTrabajadoMinutos());
            estado.put("porcentajeJornada", asistencia.getPorcentajeJornada());

            // Hora de salida
            if (asistencia.getFechaHoraSalida() != null) {
                estado.put("horaSalida", asistencia.getFechaHoraSalida().format(DateTimeFormatter.ofPattern("HH:mm:ss")));
            } else {
                estado.put("horaSalida", null);
            }

            // Si hay actividad activa, calcular tiempo transcurrido
            if (asistencia.getBreakActivo() && asistencia.getBreakInicioActual() != null) {
                long minutosBreakActual = Duration.between(
                        asistencia.getBreakInicioActual(), LocalDateTime.now()
                ).toMinutes();
                estado.put("breakTiempoActual", minutosBreakActual);
                estado.put("breakExcedido", minutosBreakActual > BREAK_LIMITE_MINUTOS);
            }

            if (asistencia.getBanoActivo() && asistencia.getBanoInicioActual() != null) {
                long minutosBanoActual = Duration.between(
                        asistencia.getBanoInicioActual(), LocalDateTime.now()
                ).toMinutes();
                estado.put("banoTiempoActual", minutosBanoActual);
                estado.put("banoExcedido", minutosBanoActual > BANO_LIMITE_MINUTOS);
            }

            log.debug("📊 Estado completo obtenido para usuario {}: {}", usuarioId, estado);
            return new GenericResponse<>(1, "Estado obtenido exitosamente", estado);

        } catch (Exception e) {
            log.error("Error al obtener estado de actividades: {}", e.getMessage(), e);
            return new GenericResponse<>(0, "Error al obtener estado: " + e.getMessage(), null);
        }
    }

    @Override
    public GenericResponse<List<AsistenciaDTO>> autoFinalizarActividadesPorTiempo(Long usuarioId) {
        try {
            List<AsistenciaDTO> actividadesFinalizadas = new ArrayList<>();
            LocalDateTime ahora = LocalDateTime.now();

            // Auto-finalizar breaks excedidos
            Optional<Asistencia> breakActivo = asistenciaRepository.findActividadActivaHoy(usuarioId, Asistencia.TipoActividad.BREAK);
            if (breakActivo.isPresent()) {
                long minutos = Duration.between(breakActivo.get().getFechaHoraEntrada(), ahora).toMinutes();
                if (minutos > BREAK_LIMITE_MINUTOS) {
                    log.warn("Auto-finalizando break excedido para usuario {}: {} minutos", usuarioId, minutos);
                    GenericResponse<AsistenciaDTO> resultado = finalizarBreak(usuarioId, "Sistema", "Auto-finalización", "Sistema");
                    if (resultado.getRpta() != null && resultado.getRpta().equals(1) && resultado.getData() != null) {
                        actividadesFinalizadas.add(resultado.getData());
                    }
                }
            }

            // Auto-finalizar baños excedidos
            Optional<Asistencia> banoActivo = asistenciaRepository.findActividadActivaHoy(usuarioId, Asistencia.TipoActividad.BANO);
            if (banoActivo.isPresent()) {
                long minutos = Duration.between(banoActivo.get().getFechaHoraEntrada(), ahora).toMinutes();
                if (minutos > BANO_LIMITE_MINUTOS) {
                    log.warn("Auto-finalizando baño excedido para usuario {}: {} minutos", usuarioId, minutos);
                    GenericResponse<AsistenciaDTO> resultado = finalizarBano(usuarioId, "Sistema", "Auto-finalización", "Sistema");
                    if (resultado.getRpta() != null && resultado.getRpta().equals(1) && resultado.getData() != null) {
                        actividadesFinalizadas.add(resultado.getData());
                    }
                }
            }

            return new GenericResponse<>(1, "Auto-finalización completada", actividadesFinalizadas);

        } catch (Exception e) {
            log.error("Error en auto-finalización: {}", e.getMessage(), e);
            return new GenericResponse<>(0, "Error en auto-finalización: " + e.getMessage(), null);
        }
    }

    @Override
    @Transactional
    public GenericResponse<AsistenciaDTO> iniciarSesionCrm(Long usuarioId, String ip, String dispositivo, String ubicacion) {
        try {
            if (tieneActividadActiva(usuarioId, Asistencia.TipoActividad.SESION_CRM)) {
                return new GenericResponse<>(0, "Ya tiene una sesión CRM activa", null);
            }

            AsistenciaCreateDTO dto = new AsistenciaCreateDTO();
            dto.setUsuarioId(usuarioId);
            dto.setFechaHoraEntrada(LocalDateTime.now());
            dto.setIpEntrada(ip);
            dto.setDispositivoEntrada(dispositivo);
            dto.setUbicacionEntrada(ubicacion);
            dto.setTipoActividad(Asistencia.TipoActividad.SESION_CRM);
            dto.setSubtipoActividad(Asistencia.SubtipoActividad.INICIO_SESION);
            dto.setObservaciones("Inicio de sesión CRM");

            return registrarAsistencia(dto);

        } catch (Exception e) {
            log.error("Error al iniciar sesión CRM: {}", e.getMessage(), e);
            return new GenericResponse<>(0, "Error al iniciar sesión CRM: " + e.getMessage(), null);
        }
    }

    @Override
    @Transactional
    public GenericResponse<AsistenciaDTO> finalizarSesionCrm(Long usuarioId, String ip, String dispositivo, String ubicacion) {
        try {
            Optional<Asistencia> sesionActivaOpt = asistenciaRepository.findSesionCrmActiva(usuarioId);

            if (sesionActivaOpt.isEmpty()) {
                return new GenericResponse<>(0, "No se encontró sesión CRM activa", null);
            }

            Asistencia sesionActiva = sesionActivaOpt.get();
            LocalDateTime ahora = LocalDateTime.now();

            long duracionMinutos = Duration.between(sesionActiva.getFechaHoraEntrada(), ahora).toMinutes();

            sesionActiva.setFechaHoraSalida(ahora);
            sesionActiva.setIpSalida(ip);
            sesionActiva.setDispositivoSalida(dispositivo);
            sesionActiva.setUbicacionSalida(ubicacion);
            sesionActiva.setSubtipoActividad(Asistencia.SubtipoActividad.FIN_SESION);
            sesionActiva.setTiempoSesionMinutos((int) duracionMinutos);
            sesionActiva.setObservaciones("Fin de sesión CRM - Duración: " + duracionMinutos + " minutos");

            sesionActiva = asistenciaRepository.save(sesionActiva);
            AsistenciaDTO asistenciaDTO = AsistenciaMapper.toDTO(sesionActiva);

            log.info("Sesión CRM finalizada exitosamente para usuario ID: {} - Duración: {} minutos", usuarioId, duracionMinutos);
            return new GenericResponse<>(1, "Sesión CRM finalizada exitosamente", asistenciaDTO);

        } catch (Exception e) {
            log.error("Error al finalizar sesión CRM: {}", e.getMessage(), e);
            return new GenericResponse<>(0, "Error al finalizar sesión CRM: " + e.getMessage(), null);
        }
    }

    /**
     * 🔥 MÉTODO AUXILIAR: Info básica SIN consultas adicionales
     */
    private Map<String, Object> crearInfoUsuarioRapida(User usuario) {
        Map<String, Object> info = new HashMap<>();

        // Solo información básica del usuario (sin consultas extra)
        info.put("id", usuario.getId());
        info.put("nombre", usuario.getNombres());
        info.put("username", usuario.getUsername());
        info.put("email", usuario.getEmail());
        info.put("role", usuario.getRole());
        info.put("dni", usuario.getDni());
        info.put("telefono", usuario.getTelefono());
        info.put("fechaCreacion", usuario.getFechaCreacion());

        // Info de estado sin consultas DB adicionales
        info.put("estadoAsistencia", "DISPONIBLE");
        info.put("ultimaAsistencia", null);
        info.put("tiempoTrabajadoHoy", 0);
        info.put("yaMarcoEntrada", false);
        info.put("yaMarcoSalida", false);

        return info;
    }

    /**
     * 🔥 MÉTODO AUXILIAR: Crear info de usuario ultra-optimizada
     */
    private Map<String, Object> crearInfoUsuarioOptimizada(User usuario) {
        Map<String, Object> info = new HashMap<>();

        // Información básica (sin consultas adicionales)
        info.put("id", usuario.getId());
        info.put("nombre", usuario.getNombres());
        info.put("username", usuario.getUsername());
        info.put("email", usuario.getEmail());
        info.put("role", usuario.getRole());
        info.put("dni", usuario.getDni());
        info.put("telefono", usuario.getTelefono());
        info.put("fechaCreacion", usuario.getFechaCreacion());

        // 🔥 INFO BÁSICA SIN CONSULTAS ADICIONALES (para velocidad)
        info.put("ultimaAsistencia", null);
        info.put("estadoAsistencia", "PENDIENTE_CARGA");
        info.put("tiempoTrabajadoHoy", 0);
        info.put("yaMarcoEntrada", false);
        info.put("yaMarcoSalida", false);

        return info;
    }


    /**
     * 🔥 NUEVO: Método para cargar asistencias de usuarios específicos bajo demanda
     */
    @Transactional(readOnly = true)
    @Override
    public GenericResponse<Map<String, Object>> obtenerAsistenciaUsuario(Long usuarioId) {
        try {
            Map<String, Object> asistenciaInfo = new HashMap<>();

            // Obtener última asistencia
            Optional<Asistencia> ultimaAsistencia = asistenciaRepository
                    .findFirstByUsuarioIdOrderByFechaHoraEntradaDesc(usuarioId);

            if (ultimaAsistencia.isPresent()) {
                Asistencia asistencia = ultimaAsistencia.get();
                asistenciaInfo.put("ultimaAsistencia", asistencia.getFechaHoraEntrada());
                asistenciaInfo.put("estadoAsistencia", asistencia.getEstadoActual());
            }

            // Obtener asistencia del día
            LocalDate hoy = LocalDate.now();
            Optional<Asistencia> asistenciaHoy = obtenerAsistenciaDelDiaInterno(usuarioId);
            if (asistenciaHoy.isPresent()) {
                Asistencia asistHoy = asistenciaHoy.get();
                asistenciaInfo.put("tiempoTrabajadoHoy", asistHoy.getTiempoNetoTrabajadoMinutos() != null ?
                        asistHoy.getTiempoNetoTrabajadoMinutos() : 0);
                asistenciaInfo.put("yaMarcoEntrada", true);
                asistenciaInfo.put("yaMarcoSalida", asistHoy.getFechaHoraSalida() != null);
            } else {
                asistenciaInfo.put("tiempoTrabajadoHoy", 0);
                asistenciaInfo.put("yaMarcoEntrada", false);
                asistenciaInfo.put("yaMarcoSalida", false);
            }

            return new GenericResponse<>(1, "Asistencia de usuario obtenida", asistenciaInfo);

        } catch (Exception e) {
            log.error("❌ Error al obtener asistencia de usuario {}: {}", usuarioId, e.getMessage());
            return new GenericResponse<>(0, "Error: " + e.getMessage(), null);
        }
    }

    /**
     * 🔥 MÉTODO AUXILIAR: Obtener asistencia del día sin lazy loading
     */
    private Optional<Asistencia> obtenerAsistenciaDelDiaInterno(Long usuarioId) {
        try {
            LocalDate hoy = LocalDate.now();
            LocalDateTime inicioDelDia = hoy.atStartOfDay();
            LocalDateTime finDelDia = hoy.atTime(LocalTime.MAX);

            return asistenciaRepository.findByUsuarioIdAndFechaHoraEntradaBetween(
                            usuarioId, inicioDelDia, finDelDia
                    ).stream()
                    .filter(a -> a.getTipoActividad() == Asistencia.TipoActividad.ENTRADA)
                    .findFirst();
        } catch (Exception e) {
            log.warn("⚠️ Error al obtener asistencia del día para usuario {}: {}", usuarioId, e.getMessage());
            return Optional.empty();
        }
    }

    /**
     * 🔥 ULTRA-OPTIMIZADO: Dashboard con consultas nativas más rápidas
     */
    @Override
    @Transactional(readOnly = true, timeout = 10) // Timeout de 10 segundos
    public GenericResponse<Map<String, Object>> obtenerDashboardSede(Long sedeId, LocalDate fecha) {
        try {
            log.info("📊 Generando dashboard OPTIMIZADO para sede {} en fecha {}", sedeId, fecha);

            Map<String, Object> dashboard = new HashMap<>();

            // 🔥 CONSULTA 1: Información de la sede (rápida)
            Optional<Sede> sedeOpt = sedeRepository.findById(sedeId);
            if (sedeOpt.isPresent()) {
                Sede sede = sedeOpt.get();
                dashboard.put("sede", Map.of(
                        "id", sede.getId(),
                        "nombre", sede.getNombre(),
                        "direccion", sede.getDireccion() != null ? sede.getDireccion() : "N/A",
                        "ciudad", sede.getCiudad() != null ? sede.getCiudad() : "N/A"
                ));
            }

            // 🔥 CONSULTA 2: Total de usuarios (consulta COUNT optimizada)
            long totalUsuarios = userRepository.countBySedeIdAndEstado(sedeId, "A");
            dashboard.put("totalUsuarios", totalUsuarios);

            // 🔥 CONSULTA 3: Asistencias del día (una sola consulta optimizada)
            List<Asistencia> entradasHoy = asistenciaRepository.findEntradasPorSedeYFecha(sedeId, fecha);
            dashboard.put("usuariosConEntrada", entradasHoy.size());

            // 🔥 CÁLCULOS EN MEMORIA (más rápido que consultas SQL)
            long usuariosConSalida = entradasHoy.stream()
                    .mapToLong(a -> a.getFechaHoraSalida() != null ? 1 : 0)
                    .sum();
            dashboard.put("usuariosConSalida", usuariosConSalida);
            dashboard.put("usuariosTrabajando", entradasHoy.size() - usuariosConSalida);

            // 🔥 CONSULTAS 4 y 5: Breaks y baños (optimizadas)
            long breaksActivos = asistenciaRepository.countBreaksActivosPorSede(sedeId, fecha);
            long banosActivos = asistenciaRepository.countBanosActivosPorSede(sedeId, fecha);
            dashboard.put("breaksActivos", breaksActivos);
            dashboard.put("banosActivos", banosActivos);

            // 🔥 CÁLCULO OPTIMIZADO: Promedio y Top usuarios
            double promedioHoras = calcularPromedioRapido(entradasHoy);
            dashboard.put("promedioHorasTrabajadas", promedioHoras);

            List<Map<String, Object>> topUsuarios = obtenerTopUsuariosRapido(entradasHoy);
            dashboard.put("topUsuarios", topUsuarios);

            // 🔥 DISTRIBUCIÓN POR HORA (cálculo en memoria)
            Map<String, Long> asistenciasPorHora = entradasHoy.stream()
                    .collect(Collectors.groupingBy(
                            a -> a.getFechaHoraEntrada().getHour() + ":00",
                            Collectors.counting()
                    ));
            dashboard.put("asistenciasPorHora", asistenciasPorHora);

            dashboard.put("fecha", fecha);
            dashboard.put("fechaConsulta", LocalDateTime.now());

            log.info("✅ Dashboard OPTIMIZADO generado: {} usuarios, {} entradas, {} trabajando",
                    totalUsuarios, entradasHoy.size(), (entradasHoy.size() - usuariosConSalida));

            return new GenericResponse<>(1, "Dashboard obtenido exitosamente", dashboard);

        } catch (Exception e) {
            log.error("❌ Error al obtener dashboard OPTIMIZADO de sede {}: {}", sedeId, e.getMessage(), e);
            return new GenericResponse<>(0, "Error al obtener dashboard: " + e.getMessage(), null);
        }
    }


    /**
     * 🔥 MÉTODO AUXILIAR: Cálculo de promedio ultra-rápido
     */
    private double calcularPromedioRapido(List<Asistencia> asistencias) {
        if (asistencias.isEmpty()) return 0.0;

        LocalDateTime ahora = LocalDateTime.now();

        return asistencias.stream()
                .filter(a -> a.getFechaHoraEntrada() != null)
                .mapToDouble(a -> {
                    LocalDateTime entrada = a.getFechaHoraEntrada();
                    LocalDateTime salida = a.getFechaHoraSalida() != null ? a.getFechaHoraSalida() : ahora;

                    long tiempoTotalMinutos = Duration.between(entrada, salida).toMinutes();
                    int tiempoBreaks = a.getBreakTiempoTotalMinutos() != null ? a.getBreakTiempoTotalMinutos() : 0;
                    int tiempoBanos = a.getBanoTiempoTotalMinutos() != null ? a.getBanoTiempoTotalMinutos() : 0;

                    long tiempoNeto = Math.max(0, tiempoTotalMinutos - tiempoBreaks - tiempoBanos);
                    return tiempoNeto / 60.0;
                })
                .average()
                .orElse(0.0);
    }

    /**
     * 🔥 MÉTODO AUXILIAR: Top usuarios ultra-rápido
     */
    private List<Map<String, Object>> obtenerTopUsuariosRapido(List<Asistencia> asistencias) {
        if (asistencias.isEmpty()) return new ArrayList<>();

        LocalDateTime ahora = LocalDateTime.now();

        return asistencias.stream()
                .map(a -> {
                    Map<String, Object> info = new HashMap<>();
                    info.put("usuario", a.getUsuario().getNombres());

                    int tiempoTrabajado = 0;
                    if (a.getFechaHoraEntrada() != null) {
                        LocalDateTime entrada = a.getFechaHoraEntrada();
                        LocalDateTime salida = a.getFechaHoraSalida() != null ? a.getFechaHoraSalida() : ahora;

                        long tiempoTotalMinutos = Duration.between(entrada, salida).toMinutes();
                        int tiempoBreaks = a.getBreakTiempoTotalMinutos() != null ? a.getBreakTiempoTotalMinutos() : 0;
                        int tiempoBanos = a.getBanoTiempoTotalMinutos() != null ? a.getBanoTiempoTotalMinutos() : 0;

                        tiempoTrabajado = (int) Math.max(0, tiempoTotalMinutos - tiempoBreaks - tiempoBanos);
                    }

                    info.put("tiempoMinutos", tiempoTrabajado);
                    info.put("tiempoHoras", String.format("%.1f", tiempoTrabajado / 60.0));
                    info.put("porcentaje", Math.min(100.0, (tiempoTrabajado / 480.0) * 100));

                    return info;
                })
                .sorted((a, b) -> Integer.compare((Integer) b.get("tiempoMinutos"), (Integer) a.get("tiempoMinutos")))
                .limit(5)
                .collect(Collectors.toList());
    }


    /**
     * 🔥 CORREGIDO: Obtener top usuarios con cálculo en tiempo real
     */
    private List<Map<String, Object>> obtenerTopUsuariosPorTiempoCorregido(List<Asistencia> asistencias) {
        try {
            LocalDateTime ahora = LocalDateTime.now();

            return asistencias.stream()
                    .map(a -> {
                        Map<String, Object> info = new HashMap<>();
                        info.put("usuario", a.getUsuario().getNombres());

                        // Calcular tiempo trabajado (real o estimado)
                        int tiempoTrabajado = 0;
                        double porcentajeJornada = 0.0;

                        if (a.getTiempoNetoTrabajadoMinutos() != null && a.getTiempoNetoTrabajadoMinutos() > 0) {
                            // Usar tiempo ya calculado
                            tiempoTrabajado = a.getTiempoNetoTrabajadoMinutos();
                            porcentajeJornada = a.getPorcentajeJornada() != null ? a.getPorcentajeJornada() : 0.0;
                        } else if (a.getFechaHoraEntrada() != null) {
                            // Calcular tiempo en tiempo real
                            LocalDateTime entrada = a.getFechaHoraEntrada();
                            LocalDateTime salida = a.getFechaHoraSalida() != null ? a.getFechaHoraSalida() : ahora;

                            long tiempoTotalMinutos = Duration.between(entrada, salida).toMinutes();
                            int tiempoBreaks = a.getBreakTiempoTotalMinutos() != null ? a.getBreakTiempoTotalMinutos() : 0;
                            int tiempoBanos = a.getBanoTiempoTotalMinutos() != null ? a.getBanoTiempoTotalMinutos() : 0;

                            tiempoTrabajado = (int) Math.max(0, tiempoTotalMinutos - tiempoBreaks - tiempoBanos);
                            porcentajeJornada = Math.min(100.0, (tiempoTrabajado / 480.0) * 100); // 480 min = 8 horas
                        }

                        info.put("tiempoMinutos", tiempoTrabajado);
                        info.put("tiempoHoras", String.format("%.1f", tiempoTrabajado / 60.0));
                        info.put("porcentaje", porcentajeJornada);

                        return info;
                    })
                    .sorted((a, b) -> Integer.compare((Integer) b.get("tiempoMinutos"), (Integer) a.get("tiempoMinutos")))
                    .limit(5)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Error al obtener top usuarios: {}", e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * 🔥 CORREGIDO: Calcular promedio de horas trabajadas con validación
     */
    private double calcularPromedioHorasTrabajadasCorregido(List<Asistencia> asistencias) {
        try {
            // Filtrar asistencias que tienen tiempo calculado
            List<Asistencia> asistenciasConTiempo = asistencias.stream()
                    .filter(a -> a.getTiempoNetoTrabajadoMinutos() != null && a.getTiempoNetoTrabajadoMinutos() > 0)
                    .collect(Collectors.toList());

            if (asistenciasConTiempo.isEmpty()) {
                // Si no hay tiempo neto, calcular tiempo total trabajado hasta ahora
                LocalDateTime ahora = LocalDateTime.now();
                return asistencias.stream()
                        .filter(a -> a.getFechaHoraEntrada() != null)
                        .mapToDouble(a -> {
                            LocalDateTime entrada = a.getFechaHoraEntrada();
                            LocalDateTime salida = a.getFechaHoraSalida() != null ? a.getFechaHoraSalida() : ahora;

                            // Calcular tiempo trabajado descontando breaks y baños
                            long tiempoTotalMinutos = Duration.between(entrada, salida).toMinutes();
                            int tiempoBreaks = a.getBreakTiempoTotalMinutos() != null ? a.getBreakTiempoTotalMinutos() : 0;
                            int tiempoBanos = a.getBanoTiempoTotalMinutos() != null ? a.getBanoTiempoTotalMinutos() : 0;

                            long tiempoNeto = Math.max(0, tiempoTotalMinutos - tiempoBreaks - tiempoBanos);
                            return tiempoNeto / 60.0; // Convertir a horas
                        })
                        .average()
                        .orElse(0.0);
            }

            return asistenciasConTiempo.stream()
                    .mapToDouble(a -> a.getTiempoNetoTrabajadoMinutos() / 60.0)
                    .average()
                    .orElse(0.0);
        } catch (Exception e) {
            log.error("Error al calcular promedio de horas: {}", e.getMessage());
            return 0.0;
        }
    }

    /**
     * 🔥 OPTIMIZADO: Exportar asistencias a Excel con manejo de errores mejorado
     */
    @Override
    public byte[] exportarAsistenciasExcel(AsistenciaFilterDTO filtros) {
        try {
            log.info("🔥 Iniciando exportación Excel con filtros: {}", filtros);

            // Validaciones previas
            if (filtros == null) {
                throw new IllegalArgumentException("Los filtros no pueden ser nulos");
            }

            // Configurar fechas por defecto si no están presentes
            if (filtros.getFechaInicio() == null) {
                filtros.setFechaInicio(LocalDate.now());
            }
            if (filtros.getFechaFin() == null) {
                filtros.setFechaFin(LocalDate.now());
            }

            // Convertir fechas a LocalDateTime para la consulta
            LocalDateTime fechaHoraInicio = filtros.getFechaInicio().atStartOfDay();
            LocalDateTime fechaHoraFin = filtros.getFechaFin().atTime(LocalTime.MAX);

            // Configurar paginación para exportación
            Sort sort = Sort.by(Sort.Direction.fromString(
                            filtros.getSortDirection() != null ? filtros.getSortDirection() : "DESC"),
                    filtros.getSortBy() != null ? filtros.getSortBy() : "fechaHoraEntrada"
            );

            Pageable pageable = PageRequest.of(0, 10000, sort);

            log.info("📊 Buscando asistencias entre {} y {} para sede {}",
                    fechaHoraInicio, fechaHoraFin, filtros.getSedeId());

            // 🔥 USAR CONSULTA OPTIMIZADA con JOIN FETCH
            Page<Asistencia> asistenciasPage = asistenciaRepository.findWithFilters(
                    filtros.getUsuarioId(),
                    filtros.getUsuarioSearch(),
                    fechaHoraInicio,
                    fechaHoraFin,
                    filtros.getTipoActividad(),
                    filtros.getEstado() != null ? filtros.getEstado() : "A",
                    filtros.getSedeId(),
                    pageable
            );

            List<Asistencia> asistencias = asistenciasPage.getContent();

            log.info("✅ Encontradas {} asistencias para exportar", asistencias.size());

            if (asistencias.isEmpty()) {
                log.warn("⚠️ No se encontraron asistencias para exportar con los filtros aplicados");
                return generarExcelVacio("No se encontraron registros para el rango de fechas seleccionado");
            }

            return generarExcelAsistencias(asistencias);

        } catch (Exception e) {
            log.error("❌ Error al exportar asistencias a Excel: {}", e.getMessage(), e);
            throw new RuntimeException("Error al generar archivo Excel: " + e.getMessage(), e);
        }
    }

    /**
     * 🔥 MÉTODO AUXILIAR: Generar Excel optimizado
     */
    private byte[] generarExcelAsistencias(List<Asistencia> asistencias) throws java.io.IOException {
        log.info("📝 Generando Excel OPTIMIZADO con {} registros", asistencias.size());

        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Asistencias");

        try {
            // Crear estilos una sola vez
            CellStyle headerStyle = crearEstiloHeader(workbook);
            CellStyle dataStyle = crearEstiloData(workbook);
            CellStyle numberStyle = crearEstiloNumero(workbook);

            // Crear header
            Row headerRow = sheet.createRow(0);
            headerRow.setHeight((short) 500);

            String[] headers = {
                    "ID", "Usuario", "Nombre", "DNI", "Sede",
                    "Fecha Entrada", "Hora Entrada", "Fecha Salida", "Hora Salida",
                    "Tipo Actividad", "Duración (min)", "Estado",
                    "Breaks", "Tiempo Breaks", "Baños", "Tiempo Baños",
                    "Tiempo Neto (min)", "Tiempo Neto (h)", "% Jornada", "Observaciones"
            };

            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }

            // Formatters
            DateTimeFormatter fechaFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
            DateTimeFormatter horaFormatter = DateTimeFormatter.ofPattern("HH:mm:ss");

            // Crear datos con procesamiento eficiente
            int rowNum = 1;
            for (Asistencia asistencia : asistencias) {
                Row row = sheet.createRow(rowNum++);

                if (asistencia.getUsuario() == null) {
                    log.warn("⚠️ Asistencia {} tiene usuario null, saltando", asistencia.getId());
                    continue;
                }

                try {
                    crearFilaAsistenciaOptimizada(row, asistencia, dataStyle, numberStyle, fechaFormatter, horaFormatter);
                } catch (Exception e) {
                    log.error("❌ Error al procesar fila {}: {}", rowNum - 1, e.getMessage());
                }
            }

            // Auto-ajustar columnas eficientemente
            for (int i = 0; i < headers.length; i++) {
                try {
                    sheet.autoSizeColumn(i);
                    if (sheet.getColumnWidth(i) > 8000) {
                        sheet.setColumnWidth(i, 8000);
                    }
                } catch (Exception e) {
                    sheet.setColumnWidth(i, 3000);
                }
            }

            sheet.createFreezePane(0, 1);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            byte[] result = outputStream.toByteArray();

            log.info("✅ Excel OPTIMIZADO generado: {} bytes", result.length);
            return result;

        } finally {
            try {
                workbook.close();
            } catch (Exception e) {
                log.warn("⚠️ Error al cerrar workbook: {}", e.getMessage());
            }
        }
    }

    /**
     * 🔥 MÉTODOS AUXILIARES PARA EXCEL
     */
    private CellStyle crearEstiloHeader(Workbook workbook) {
        CellStyle headerStyle = workbook.createCellStyle();
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerFont.setColor(IndexedColors.WHITE.getIndex());
        headerFont.setFontHeightInPoints((short) 12);
        headerStyle.setFont(headerFont);
        headerStyle.setFillForegroundColor(IndexedColors.DARK_BLUE.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        return headerStyle;
    }

    private CellStyle crearEstiloData(Workbook workbook) {
        CellStyle dataStyle = workbook.createCellStyle();
        dataStyle.setAlignment(HorizontalAlignment.LEFT);
        dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        return dataStyle;
    }

    private CellStyle crearEstiloNumero(Workbook workbook) {
        CellStyle numberStyle = workbook.createCellStyle();
        numberStyle.setAlignment(HorizontalAlignment.RIGHT);
        return numberStyle;
    }

    private void crearFilaAsistenciaOptimizada(Row row, Asistencia asistencia, CellStyle dataStyle,
                                               CellStyle numberStyle, DateTimeFormatter fechaFormatter,
                                               DateTimeFormatter horaFormatter) {
        // ID
        Cell cellId = row.createCell(0);
        cellId.setCellValue(asistencia.getId() != null ? asistencia.getId() : 0);
        cellId.setCellStyle(numberStyle);

        // Usuario
        row.createCell(1).setCellValue(asistencia.getUsuario().getUsername() != null ?
                asistencia.getUsuario().getUsername() : "N/A");

        // Nombre
        row.createCell(2).setCellValue(asistencia.getUsuario().getNombres());

        // DNI
        row.createCell(3).setCellValue(asistencia.getUsuario().getDni() != null ?
                asistencia.getUsuario().getDni() : "N/A");

        // Sede
        String sedeNombre = "N/A";
        if (asistencia.getUsuario().getSede() != null) {
            sedeNombre = asistencia.getUsuario().getSede().getNombre();
        } else if (asistencia.getUsuario().getSedeNombre() != null) {
            sedeNombre = asistencia.getUsuario().getSedeNombre();
        }
        row.createCell(4).setCellValue(sedeNombre);

        // Fechas y horas
        if (asistencia.getFechaHoraEntrada() != null) {
            row.createCell(5).setCellValue(asistencia.getFechaHoraEntrada().format(fechaFormatter));
            row.createCell(6).setCellValue(asistencia.getFechaHoraEntrada().format(horaFormatter));
        } else {
            row.createCell(5).setCellValue("N/A");
            row.createCell(6).setCellValue("N/A");
        }

        if (asistencia.getFechaHoraSalida() != null) {
            row.createCell(7).setCellValue(asistencia.getFechaHoraSalida().format(fechaFormatter));
            row.createCell(8).setCellValue(asistencia.getFechaHoraSalida().format(horaFormatter));
        } else {
            row.createCell(7).setCellValue("N/A");
            row.createCell(8).setCellValue("N/A");
        }

        // Resto de campos
        row.createCell(9).setCellValue(asistencia.getTipoActividad() != null ?
                asistencia.getTipoActividad().toString() : "N/A");

        Cell cellDuracion = row.createCell(10);
        cellDuracion.setCellValue(asistencia.getDuracionMinutos() != null ? asistencia.getDuracionMinutos() : 0);
        cellDuracion.setCellStyle(numberStyle);

        row.createCell(11).setCellValue(asistencia.getEstado() != null ? asistencia.getEstado() : "A");

        // Contadores
        Cell cellBreaks = row.createCell(12);
        cellBreaks.setCellValue(asistencia.getBreakContador() != null ? asistencia.getBreakContador() : 0);
        cellBreaks.setCellStyle(numberStyle);

        Cell cellTiempoBreaks = row.createCell(13);
        cellTiempoBreaks.setCellValue(asistencia.getBreakTiempoTotalMinutos() != null ? asistencia.getBreakTiempoTotalMinutos() : 0);
        cellTiempoBreaks.setCellStyle(numberStyle);

        Cell cellBanos = row.createCell(14);
        cellBanos.setCellValue(asistencia.getBanoContador() != null ? asistencia.getBanoContador() : 0);
        cellBanos.setCellStyle(numberStyle);

        Cell cellTiempoBanos = row.createCell(15);
        cellTiempoBanos.setCellValue(asistencia.getBanoTiempoTotalMinutos() != null ? asistencia.getBanoTiempoTotalMinutos() : 0);
        cellTiempoBanos.setCellStyle(numberStyle);

        // Tiempo neto
        Cell cellTiempoNeto = row.createCell(16);
        int tiempoNeto = asistencia.getTiempoNetoTrabajadoMinutos() != null ? asistencia.getTiempoNetoTrabajadoMinutos() : 0;
        cellTiempoNeto.setCellValue(tiempoNeto);
        cellTiempoNeto.setCellStyle(numberStyle);

        Cell cellTiempoNetoHoras = row.createCell(17);
        cellTiempoNetoHoras.setCellValue(tiempoNeto / 60.0);
        cellTiempoNetoHoras.setCellStyle(numberStyle);

        Cell cellPorcentaje = row.createCell(18);
        cellPorcentaje.setCellValue(asistencia.getPorcentajeJornada() != null ? asistencia.getPorcentajeJornada() : 0.0);
        cellPorcentaje.setCellStyle(numberStyle);

        // Observaciones
        String observaciones = asistencia.getObservaciones() != null ? asistencia.getObservaciones() : "";
        if (observaciones.length() > 255) {
            observaciones = observaciones.substring(0, 252) + "...";
        }
        row.createCell(19).setCellValue(observaciones);
    }

    private byte[] generarExcelVacio(String mensaje) throws java.io.IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Sin Datos");

        try {
            Row row = sheet.createRow(0);
            Cell cell = row.createCell(0);
            cell.setCellValue(mensaje);

            CellStyle messageStyle = workbook.createCellStyle();
            Font messageFont = workbook.createFont();
            messageFont.setBold(true);
            messageFont.setFontHeightInPoints((short) 14);
            messageStyle.setFont(messageFont);
            cell.setCellStyle(messageStyle);

            sheet.setColumnWidth(0, 15000);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            return outputStream.toByteArray();

        } finally {
            workbook.close();
        }
    }

    /**
     * 🔥 MÉTODO AUXILIAR: Extraer userId del token JWT
     */
    @Override
    public Long extractUserIdFromToken(String token) {
        try {
            if (token != null && token.startsWith("Bearer ")) {
                String jwt = token.substring(7);

                // Usar Spring Security Context
                Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
                if (authentication != null && authentication.getPrincipal() instanceof UserDetails) {
                    UserDetails userDetails = (UserDetails) authentication.getPrincipal();
                    Optional<User> userOpt = userRepository.findByUsername(userDetails.getUsername());
                    return userOpt.map(User::getId).orElse(null);
                }
            }
        } catch (Exception e) {
            log.error("Error al extraer userId del token: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 🔥 NUEVO: Exportar asistencias a CSV
     */
    @Override
    public byte[] exportarAsistenciasCSV(AsistenciaFilterDTO filtros) {
        try {
            log.info("🔥 Iniciando exportación CSV con filtros: {}", filtros);

            // Validaciones previas
            if (filtros == null) {
                throw new IllegalArgumentException("Los filtros no pueden ser nulos");
            }

            // Configurar fechas por defecto si no están presentes
            if (filtros.getFechaInicio() == null) {
                filtros.setFechaInicio(LocalDate.now());
            }
            if (filtros.getFechaFin() == null) {
                filtros.setFechaFin(LocalDate.now());
            }

            // Configurar paginación para exportación
            if (filtros.getPage() == null) filtros.setPage(0);
            if (filtros.getSize() == null) filtros.setSize(10000);

            LocalDateTime fechaHoraInicio = filtros.getFechaInicio().atStartOfDay();
            LocalDateTime fechaHoraFin = filtros.getFechaFin().atTime(23, 59, 59);

            Pageable pageable = PageRequest.of(
                    filtros.getPage(),
                    filtros.getSize(),
                    Sort.by(Sort.Direction.fromString(filtros.getSortDirection() != null ? filtros.getSortDirection() : "DESC"),
                            filtros.getSortBy() != null ? filtros.getSortBy() : "fechaHoraEntrada")
            );

            // Obtener asistencias con consulta optimizada
            Page<Asistencia> asistenciasPage = asistenciaRepository.findWithFilters(
                    filtros.getUsuarioId(),
                    filtros.getUsuarioSearch(),
                    fechaHoraInicio,
                    fechaHoraFin,
                    filtros.getTipoActividad(),
                    filtros.getEstado() != null ? filtros.getEstado() : "A",
                    filtros.getSedeId(),
                    pageable
            );

            List<Asistencia> asistencias = asistenciasPage.getContent();

            log.info("✅ Encontradas {} asistencias para exportar a CSV", asistencias.size());

            if (asistencias.isEmpty()) {
                log.warn("⚠️ No se encontraron asistencias para exportar con los filtros aplicados");
                return generarCSVVacio("No se encontraron registros para el rango de fechas seleccionado");
            }

            return generarCSVAsistencias(asistencias);

        } catch (Exception e) {
            log.error("❌ Error al exportar asistencias a CSV: {}", e.getMessage(), e);
            throw new RuntimeException("Error al generar archivo CSV: " + e.getMessage(), e);
        }
    }

    /**
     * 🔥 NUEVO: Generar reporte PDF de asistencias
     */
    @Override
    public byte[] generarReportePDF(AsistenciaFilterDTO filtros) {
        try {
            log.info("🔥 Iniciando generación de PDF con filtros: {}", filtros);

            // Validaciones previas
            if (filtros == null) {
                throw new IllegalArgumentException("Los filtros no pueden ser nulos");
            }

            // Configurar fechas por defecto si no están presentes
            if (filtros.getFechaInicio() == null) {
                filtros.setFechaInicio(LocalDate.now());
            }
            if (filtros.getFechaFin() == null) {
                filtros.setFechaFin(LocalDate.now());
            }

            // Configurar paginación para exportación
            if (filtros.getPage() == null) filtros.setPage(0);
            if (filtros.getSize() == null) filtros.setSize(1000); // Menos registros para PDF

            LocalDateTime fechaHoraInicio = filtros.getFechaInicio().atStartOfDay();
            LocalDateTime fechaHoraFin = filtros.getFechaFin().atTime(23, 59, 59);

            Pageable pageable = PageRequest.of(
                    filtros.getPage(),
                    filtros.getSize(),
                    Sort.by(Sort.Direction.fromString(filtros.getSortDirection() != null ? filtros.getSortDirection() : "DESC"),
                            filtros.getSortBy() != null ? filtros.getSortBy() : "fechaHoraEntrada")
            );

            // Obtener asistencias con consulta optimizada
            Page<Asistencia> asistenciasPage = asistenciaRepository.findWithFilters(
                    filtros.getUsuarioId(),
                    filtros.getUsuarioSearch(),
                    fechaHoraInicio,
                    fechaHoraFin,
                    filtros.getTipoActividad(),
                    filtros.getEstado() != null ? filtros.getEstado() : "A",
                    filtros.getSedeId(),
                    pageable
            );

            List<Asistencia> asistencias = asistenciasPage.getContent();

            log.info("✅ Encontradas {} asistencias para generar PDF", asistencias.size());

            if (asistencias.isEmpty()) {
                log.warn("⚠️ No se encontraron asistencias para generar PDF con los filtros aplicados");
                return generarPDFVacio("No se encontraron registros para el rango de fechas seleccionado");
            }

            return generarPDFAsistencias(asistencias, filtros);

        } catch (Exception e) {
            log.error("❌ Error al generar PDF de asistencias: {}", e.getMessage(), e);
            throw new RuntimeException("Error al generar archivo PDF: " + e.getMessage(), e);
        }
    }

    /**
     * 🔥 MÉTODO AUXILIAR: Generar CSV vacío
     */
    private byte[] generarCSVVacio(String mensaje) {
        StringBuilder csv = new StringBuilder();
        csv.append("Mensaje\n");
        csv.append(mensaje).append("\n");
        return csv.toString().getBytes(StandardCharsets.UTF_8);
    }

    /**
     * 🔥 MÉTODO AUXILIAR: Generar CSV con asistencias
     */
    private byte[] generarCSVAsistencias(List<Asistencia> asistencias) {
        StringBuilder csv = new StringBuilder();

        // Header CSV
        csv.append("ID,Usuario,Nombre Completo,DNI,Sede,")
                .append("Fecha Entrada,Hora Entrada,Fecha Salida,Hora Salida,")
                .append("Tipo Actividad,Duración (min),Estado Actual,")
                .append("Total Breaks,Tiempo Breaks (min),Total Baños,Tiempo Baños (min),")
                .append("Tiempo Neto (min),Tiempo Neto (horas),% Jornada,Observaciones\n");

        // Datos
        for (Asistencia asistencia : asistencias) {
            csv.append(asistencia.getId()).append(",");
            csv.append(asistencia.getUsuario() != null ? asistencia.getUsuario().getUsername() : "N/A").append(",");
            csv.append(asistencia.getUsuario() != null ? asistencia.getUsuario().getNombres() : "N/A").append(",");
            csv.append(asistencia.getUsuario() != null ? asistencia.getUsuario().getDni() : "N/A").append(",");
            csv.append(asistencia.getUsuario() != null && asistencia.getUsuario().getSede() != null ?
                    asistencia.getUsuario().getSede().getNombre() : "N/A").append(",");

            // Fechas y horas
            if (asistencia.getFechaHoraEntrada() != null) {
                csv.append(asistencia.getFechaHoraEntrada().toLocalDate()).append(",");
                csv.append(asistencia.getFechaHoraEntrada().toLocalTime()).append(",");
            } else {
                csv.append("N/A,N/A,");
            }

            if (asistencia.getFechaHoraSalida() != null) {
                csv.append(asistencia.getFechaHoraSalida().toLocalDate()).append(",");
                csv.append(asistencia.getFechaHoraSalida().toLocalTime()).append(",");
            } else {
                csv.append("N/A,N/A,");
            }

            csv.append(asistencia.getTipoActividad()).append(",");
            csv.append(asistencia.getDuracionMinutos() != null ? asistencia.getDuracionMinutos() : 0).append(",");
            csv.append(asistencia.getEstado()).append(",");
            csv.append(asistencia.getContadorBreakDia() != null ? asistencia.getContadorBreakDia() : 0).append(",");
            csv.append(asistencia.getBreakTiempoTotalMinutos() != null ? asistencia.getBreakTiempoTotalMinutos() : 0).append(",");
            csv.append(asistencia.getContadorBanoDia() != null ? asistencia.getContadorBanoDia() : 0).append(",");
            csv.append(asistencia.getBanoTiempoTotalMinutos() != null ? asistencia.getBanoTiempoTotalMinutos() : 0).append(",");

            // Cálculos adicionales
            int tiempoNeto = (asistencia.getDuracionMinutos() != null ? asistencia.getDuracionMinutos() : 0) -
                    (asistencia.getBreakTiempoTotalMinutos() != null ? asistencia.getBreakTiempoTotalMinutos() : 0) -
                    (asistencia.getBanoTiempoTotalMinutos() != null ? asistencia.getBanoTiempoTotalMinutos() : 0);

            csv.append(tiempoNeto).append(",");
            csv.append(String.format("%.2f", tiempoNeto / 60.0)).append(",");
            csv.append(String.format("%.1f", (tiempoNeto / 480.0) * 100)).append(","); // 480 min = 8 horas
            csv.append(asistencia.getObservaciones() != null ?
                    asistencia.getObservaciones().replace(",", ";") : "").append("\n");
        }

        return csv.toString().getBytes(StandardCharsets.UTF_8);
    }

    /**
     * 🔥 MÉTODO AUXILIAR: Generar PDF vacío
     */
    private byte[] generarPDFVacio(String mensaje) {
        // Por simplicidad, retornamos un mensaje como texto
        // En una implementación real usarías una librería como iText
        String content = "REPORTE DE ASISTENCIAS\n\n" + mensaje;
        return content.getBytes(StandardCharsets.UTF_8);
    }

    /**
     * 🔥 MÉTODO AUXILIAR: Generar PDF con asistencias
     */
    private byte[] generarPDFAsistencias(List<Asistencia> asistencias, AsistenciaFilterDTO filtros) {
        // Por simplicidad, generamos un reporte en texto plano
        // En una implementación real usarías una librería como iText para generar PDF real
        StringBuilder pdf = new StringBuilder();

        pdf.append("REPORTE DE ASISTENCIAS\n");
        pdf.append("======================\n\n");
        pdf.append("Período: ").append(filtros.getFechaInicio()).append(" - ").append(filtros.getFechaFin()).append("\n");
        pdf.append("Total de registros: ").append(asistencias.size()).append("\n\n");

        for (Asistencia asistencia : asistencias) {
            pdf.append("ID: ").append(asistencia.getId()).append("\n");
            pdf.append("Usuario: ").append(asistencia.getUsuario() != null ?
                    asistencia.getUsuario().getNombres() : "N/A").append("\n");
            pdf.append("Entrada: ").append(asistencia.getFechaHoraEntrada()).append("\n");
            pdf.append("Salida: ").append(asistencia.getFechaHoraSalida() != null ?
                    asistencia.getFechaHoraSalida() : "Activa").append("\n");
            pdf.append("Tipo: ").append(asistencia.getTipoActividad()).append("\n");
            pdf.append("Duración: ").append(asistencia.getDuracionMinutos() != null ?
                    asistencia.getDuracionMinutos() + " min" : "N/A").append("\n");
            pdf.append("----------------------------------------\n");
        }

        return pdf.toString().getBytes(StandardCharsets.UTF_8);
    }

    /**
     * 🔥 ULTRA-OPTIMIZADO: Obtener usuarios de sede con consulta por lotes
     */
    @Override
    @Transactional(readOnly = true, timeout = 15) // Timeout de 15 segundos
    public GenericResponse<List<Map<String, Object>>> obtenerUsuariosDeSede(Long sedeId) {
        try {
            log.info("🏢 Obteniendo usuarios de sede ID: {} (ULTRA-OPTIMIZADO)", sedeId);

            // 🔥 PASO 1: Verificar sede (rápido)
            Optional<Sede> sedeOpt = sedeRepository.findById(sedeId);
            if (sedeOpt.isEmpty()) {
                log.error("❌ Sede {} no encontrada", sedeId);
                return new GenericResponse<>(0, "Sede no encontrada", null);
            }

            Sede sede = sedeOpt.get();
            log.info("✅ Sede encontrada: {}", sede.getNombre());

            // 🔥 PASO 2: Obtener usuarios con consulta optimizada
            List<User> usuarios = userRepository.findBySedeIdAndEstado(sedeId, "A");
            log.info("✅ Encontrados {} usuarios activos", usuarios.size());

            // 🔥 PASO 3: Procesar en lotes pequeños para evitar timeout
            List<Map<String, Object>> usuariosInfo = new ArrayList<>();
            int batchSize = 50; // Procesar de 50 en 50

            for (int i = 0; i < usuarios.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, usuarios.size());
                List<User> batch = usuarios.subList(i, endIndex);

                log.debug("📦 Procesando lote {}-{} de {}", i + 1, endIndex, usuarios.size());

                // Procesar lote
                for (User usuario : batch) {
                    usuariosInfo.add(crearInfoUsuarioOptimizada(usuario));
                }
            }

            log.info("📊 Información procesada para {} usuarios en total", usuariosInfo.size());
            return new GenericResponse<>(1, "Usuarios obtenidos exitosamente", usuariosInfo);

        } catch (Exception e) {
            log.error("❌ Error al obtener usuarios de sede {}: {}", sedeId, e.getMessage(), e);
            return new GenericResponse<>(0, "Error al obtener usuarios: " + e.getMessage(), null);
        }
    }

    /**
     * 🔥 PAGINADO: Obtener usuarios de sede con paginación y búsqueda
     */
    @Override
    @Transactional(readOnly = true, timeout = 15)
    public GenericResponse<Map<String, Object>> obtenerUsuariosDeSedePaginado(Long sedeId, int page, int size, String search) {
        try {
            log.info("🏢 Obteniendo usuarios de sede ID: {} (PAGINADO) - Page: {}, Size: {}, Search: '{}'",
                    sedeId, page, size, search);

            // 🔥 PASO 1: Verificar sede
            Optional<Sede> sedeOpt = sedeRepository.findById(sedeId);
            if (sedeOpt.isEmpty()) {
                log.error("❌ Sede {} no encontrada", sedeId);
                return new GenericResponse<>(0, "Sede no encontrada", null);
            }

            Sede sede = sedeOpt.get();
            log.info("✅ Sede encontrada: {}", sede.getNombre());

            // 🔥 PASO 2: Crear Pageable
            Pageable pageable = PageRequest.of(page, size, Sort.by("nombre").ascending().and(Sort.by("apellido").ascending()));

            // 🔥 PASO 3: Obtener usuarios con paginación y búsqueda
            Page<User> usuariosPage;
            if (search != null && !search.trim().isEmpty()) {
                // Búsqueda por nombre, apellido o username
                usuariosPage = userRepository.findBySedeIdAndEstadoAndSearch(sedeId, "A", search.trim(), pageable);
            } else {
                // Sin búsqueda, solo paginación
                usuariosPage = userRepository.findBySedeIdAndEstado(sedeId, "A", pageable);
            }

            log.info("✅ Encontrados {} usuarios (página {} de {})",
                    usuariosPage.getNumberOfElements(),
                    usuariosPage.getNumber() + 1,
                    usuariosPage.getTotalPages());

            // 🔥 PASO 4: Procesar usuarios
            List<Map<String, Object>> usuariosInfo = usuariosPage.getContent().stream()
                    .map(this::crearInfoUsuarioOptimizada)
                    .collect(Collectors.toList());

            // 🔥 PASO 5: Crear respuesta paginada
            Map<String, Object> response = new HashMap<>();
            response.put("usuarios", usuariosInfo);
            response.put("currentPage", usuariosPage.getNumber());
            response.put("totalPages", usuariosPage.getTotalPages());
            response.put("totalElements", usuariosPage.getTotalElements());
            response.put("size", usuariosPage.getSize());
            response.put("hasNext", usuariosPage.hasNext());
            response.put("hasPrevious", usuariosPage.hasPrevious());

            log.info("📊 Respuesta paginada creada exitosamente");
            return new GenericResponse<>(1, "Usuarios obtenidos exitosamente", response);

        } catch (Exception e) {
            log.error("❌ Error al obtener usuarios paginados de sede {}: {}", sedeId, e.getMessage(), e);
            return new GenericResponse<>(0, "Error al obtener usuarios: " + e.getMessage(), null);
        }
    }

    /**
     * 🔥 NUEVO: Cierra una asistencia abierta por la tarea programada.
     * Este método contiene la lógica de negocio y es llamado desde TareaProgramadaService.
     */
    @Transactional
    public void cerrarAsistenciaAbiertaPorSistema(Asistencia asistencia) {
        if (asistencia == null || asistencia.getFechaHoraSalida() != null) {
            return;
        }

        log.warn("Cerrando por sistema la asistencia ID: {} del usuario: {}",
                asistencia.getId(), asistencia.getUsuario().getUsername());

        // 1. 🔥 ESTABLECE LOS TIEMPOS A CERO, como solicitaste.
        asistencia.setTiempoNetoTrabajadoMinutos(0);
        asistencia.setPorcentajeJornada(0.0);
        // El campo duracionMinutos también podría ir a 0 si lo usas en reportes.
        asistencia.setDuracionMinutos(0);

        // 2. Cambia el estado para "congelar" el registro.
        asistencia.setEstadoActual(Asistencia.EstadoActual.CERRADO_SISTEMA);

        // 3. Añade la observación.
        String observacionesPrevias = asistencia.getObservaciones() != null ? asistencia.getObservaciones() : "";
        asistencia.setObservaciones(observacionesPrevias + " | Cierre automático: Salida no marcada.");

        // 4. Guarda los cambios.
        asistenciaRepository.save(asistencia);
    }

    @Override
    @Transactional
    public GenericResponse<AsistenciaDTO> actualizarAsistenciaAdmin(Long asistenciaId, AsistenciaUpdateDTO dto) {
        try {
            // 1. Buscar la asistencia que se va a corregir.
            Optional<Asistencia> asistenciaOpt = asistenciaRepository.findById(asistenciaId);
            if (asistenciaOpt.isEmpty()) {
                return new GenericResponse<>(0, "Asistencia no encontrada con ID: " + asistenciaId, null);
            }
            Asistencia asistencia = asistenciaOpt.get();

            // 2. Validar que la nueva hora de salida sea posterior a la entrada.
            if (dto.getNuevaFechaHoraSalida().isBefore(asistencia.getFechaHoraEntrada())) {
                return new GenericResponse<>(0, "La hora de salida no puede ser anterior a la de entrada.", null);
            }

            // 3. Actualizar los campos con la corrección del admin.
            asistencia.setFechaHoraSalida(dto.getNuevaFechaHoraSalida());
            asistencia.setEstadoActual(Asistencia.EstadoActual.SALIDA); // Cambia a una salida normal

            String obsAdmin = dto.getObservacionAdmin() != null ? dto.getObservacionAdmin() : "Corrección manual.";
            asistencia.setObservaciones(asistencia.getObservaciones() + " | CORRECCIÓN ADMIN: " + obsAdmin);

            // 4. 🔥 REUTILIZAR tu lógica existente para recalcular TODO.
            recalcularTiempoNetoTrabajado(asistencia);

            // 5. Guardar y devolver la asistencia corregida.
            Asistencia asistenciaCorregida = asistenciaRepository.save(asistencia);
            AsistenciaDTO asistenciaCorregidaDTO = AsistenciaMapper.toDTO(asistenciaCorregida);

            log.info("Asistencia ID {} corregida manualmente por un administrador.", asistenciaId);
            return new GenericResponse<>(1, "Asistencia corregida exitosamente.", asistenciaCorregidaDTO);

        } catch (Exception e) {
            log.error("Error al corregir asistencia: {}", e.getMessage(), e);
            return new GenericResponse<>(0, "Error al corregir asistencia: " + e.getMessage(), null);
        }
    }


}
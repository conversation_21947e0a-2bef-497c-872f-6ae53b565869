package com.midas.crm.service;

import com.midas.crm.entity.DTO.modulo.ModuloCreateDTO;
import com.midas.crm.entity.DTO.modulo.ModuloDTO;
import com.midas.crm.entity.DTO.modulo.ModuloUpdateDTO;

import java.util.List;

public interface ModuloService {
    ModuloDTO createModulo(ModuloCreateDTO dto);
    List<ModuloDTO> listModulos();
    List<ModuloDTO> listModulosByCursoId(Long cursoId);
    ModuloDTO getModuloById(Long id);
    ModuloDTO updateModulo(Long id, ModuloUpdateDTO dto);
    void deleteModulo(Long id);
}

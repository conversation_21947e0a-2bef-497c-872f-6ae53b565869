package com.midas.crm.entity.DTO.gmail;


import java.util.List;

/**
 * DTOs para las operaciones de Gmail.
 */
public class EmailDtos {

    // DTO para enviar emails con remitente y destinatario específicos
    public record EnviarEmailRequest(String de, String para, String asunto, String cuerpo) {}
    public record EmailMensajeResumen(String id, String de, String asunto, String snippet, String fecha) {}


    /**
     * DTO para el contenido detallado de un único correo.
     */
    public record EmailMensajeDetalle(
            String id,
            String de,
            String para,
            String asunto,
            String cuerpoHtml,
            String fecha,
            List<AdjuntoDetalle> adjuntos // <-- Usa el AdjuntoDetalle actualizado
    ) {}

    /**
     * DTO para los detalles de un archivo adjunto.
     *
     * @param attachmentId  El ID del adjunto, necesario para descargarlo.
     * @param nombreArchivo El nombre del archivo adjunto.
     * @param mimeType      El tipo MIME del adjunto.
     * @param tamanio       El tamaño del adjunto en bytes.
     */
    public record AdjuntoDetalle(
            String attachmentId, // <-- CAMBIO: Se añade el ID del adjunto.
            String nombreArchivo,
            String mimeType,
            int tamanio
    ) {}
}
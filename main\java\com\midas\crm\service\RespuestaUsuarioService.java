package com.midas.crm.service;

import com.midas.crm.entity.DTO.cuestionario.DetalleRespuestaUsuarioCreateDTO;
import com.midas.crm.entity.DTO.cuestionario.RespuestaUsuarioCreateDTO;
import com.midas.crm.entity.DTO.cuestionario.RespuestaUsuarioDTO;

import java.util.List;
import java.util.Map;

public interface RespuestaUsuarioService {
    RespuestaUsuarioDTO iniciarCuestionario(RespuestaUsuarioCreateDTO dto);
    RespuestaUsuarioDTO responderPregunta(Long respuestaUsuarioId, DetalleRespuestaUsuarioCreateDTO dto);
    RespuestaUsuarioDTO finalizarCuestionario(Long respuestaUsuarioId);
    RespuestaUsuarioDTO getRespuestaUsuarioById(Long id);
    List<RespuestaUsuarioDTO> getRespuestaUsuarioByUsuarioAndCuestionario(Long usuarioId, Long cuestionarioId);
    RespuestaUsuarioDTO getMejorIntentoByUsuarioAndCuestionario(Long usuarioId, Long cuestionarioId);
    Map<String, Object> getResumenCuestionariosByCursoAndUsuario(Long cursoId, Long usuarioId);
    boolean puedeIntentarCuestionario(Long usuarioId, Long cuestionarioId);
}

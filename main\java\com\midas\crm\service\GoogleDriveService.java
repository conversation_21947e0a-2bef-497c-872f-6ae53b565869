package com.midas.crm.service;

import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * Servicio para operaciones con Google Drive
 */
public interface GoogleDriveService {

    /**
     * Sube un archivo a Google Drive
     * @param file Archivo a subir
     * @param fileName Nombre del archivo
     * @param folderId ID de la carpeta destino (opcional)
     * @return ID del archivo subido
     */
    String uploadFile(MultipartFile file, String fileName, String folderId) throws IOException;

    /**
     * Sube un archivo a Google Drive en la carpeta raíz
     * @param file Archivo a subir
     * @param fileName Nombre del archivo
     * @return ID del archivo subido
     */
    String uploadFile(MultipartFile file, String fileName) throws IOException;

    /**
     * Sube un archivo a Google Drive desde byte array
     * @param fileContent Contenido del archivo como byte array
     * @param fileName Nombre del archivo
     * @param mimeType Tipo MIME del archivo
     * @param folderId ID de la carpeta destino (opcional)
     * @return ID del archivo subido
     */
    String uploadFile(byte[] fileContent, String fileName, String mimeType, String folderId) throws IOException;

    /**
     * Sube un archivo a Google Drive desde byte array en la carpeta raíz
     * @param fileContent Contenido del archivo como byte array
     * @param fileName Nombre del archivo
     * @param mimeType Tipo MIME del archivo
     * @return ID del archivo subido
     */
    String uploadFile(byte[] fileContent, String fileName, String mimeType) throws IOException;

    /**
     * Descarga un archivo de Google Drive
     * @param fileId ID del archivo
     * @return Contenido del archivo como array de bytes
     */
    byte[] downloadFile(String fileId) throws IOException;

    /**
     * Elimina un archivo de Google Drive
     * @param fileId ID del archivo a eliminar
     */
    void deleteFile(String fileId) throws IOException;

    /**
     * Lista archivos en Google Drive
     * @param folderId ID de la carpeta (opcional)
     * @param pageSize Número máximo de archivos a retornar
     * @return Lista de archivos
     */
    List<Map<String, Object>> listFiles(String folderId, int pageSize) throws IOException;

    /**
     * Lista archivos con paginación completa y soporte para archivos compartidos
     * @param folderId ID de la carpeta (opcional)
     * @param pageSize Número máximo de archivos a retornar
     * @param pageToken Token de página para paginación
     * @param includeShared Si incluir archivos compartidos
     * @return Mapa con archivos y metadatos de paginación
     */
    Map<String, Object> listFilesWithPagination(String folderId, int pageSize, String pageToken, boolean includeShared) throws IOException;

    /**
     * Lista archivos en la carpeta raíz
     * @param pageSize Número máximo de archivos a retornar
     * @return Lista de archivos
     */
    List<Map<String, Object>> listFiles(int pageSize) throws IOException;

    /**
     * Crea una carpeta en Google Drive
     * @param folderName Nombre de la carpeta
     * @param parentFolderId ID de la carpeta padre (opcional)
     * @return ID de la carpeta creada
     */
    String createFolder(String folderName, String parentFolderId) throws IOException;

    /**
     * Crea una carpeta en la raíz de Google Drive
     * @param folderName Nombre de la carpeta
     * @return ID de la carpeta creada
     */
    String createFolder(String folderName) throws IOException;

    /**
     * Obtiene información de un archivo
     * @param fileId ID del archivo
     * @return Información del archivo
     */
    Map<String, Object> getFileInfo(String fileId) throws IOException;

    /**
     * Comparte un archivo con permisos específicos
     * @param fileId ID del archivo
     * @param email Email del usuario con quien compartir
     * @param role Rol del usuario (reader, writer, owner)
     * @return ID del permiso creado
     */
    String shareFile(String fileId, String email, String role) throws IOException;

    /**
     * Obtiene el enlace público de un archivo
     * @param fileId ID del archivo
     * @return URL pública del archivo
     */
    String getPublicLink(String fileId) throws IOException;

    /**
     * Busca archivos MP3 por número de agente
     * @param numeroAgente Número del agente (normalizado)
     * @return Lista de archivos MP3 encontrados
     */
    List<Map<String, Object>> findMp3FilesByAgent(String numeroAgente) throws IOException;

    /**
     * Busca archivos MP3 por nombre que contenga el número de agente
     * @param numeroAgente Número del agente
     * @param folderId ID de la carpeta donde buscar (opcional)
     * @return URL del primer archivo MP3 encontrado o null si no se encuentra
     */
    String findMp3UrlByAgent(String numeroAgente, String folderId) throws IOException;

    /**
     * Busca archivo de audio por número móvil y agente (más preciso)
     * @param numeroMovil Número móvil del cliente
     * @param numeroAgente Número del agente
     * @param folderId ID de la carpeta donde buscar (opcional)
     * @return URL del archivo de audio encontrado o null si no se encuentra
     */
    String findAudioByMovilAndAgent(String numeroMovil, String numeroAgente, String folderId) throws IOException;

    /**
     * Busca archivo de audio por número móvil, agente y fecha específica
     * @param numeroMovil Número móvil del cliente
     * @param numeroAgente Número del agente
     * @param fechaCreacion Fecha de creación del lead (formato yyyy-MM-dd)
     * @param folderId ID de la carpeta donde buscar (opcional, si no se especifica busca por fecha)
     * @return URL del archivo de audio encontrado o null si no se encuentra
     */
    String findAudioByMovilAgentAndDate(String numeroMovil, String numeroAgente, String fechaCreacion, String folderId) throws IOException;

    /**
     * Busca una carpeta por nombre
     * @param folderName Nombre de la carpeta a buscar
     * @return ID de la carpeta si se encuentra, null si no existe
     */
    String findFolderByName(String folderName) throws IOException;

    /**
     * Obtiene todos los archivos de audio de una fecha específica
     * @param fechaCreacion Fecha de creación en formato yyyy-MM-dd
     * @return Lista de archivos de audio encontrados en esa fecha
     */
    List<Map<String, Object>> getAllAudioFilesByDate(String fechaCreacion) throws IOException;

    /**
     * Lista todas las carpetas disponibles
     * @param pageSize Número máximo de carpetas a retornar
     * @return Lista de carpetas
     */
    List<Map<String, Object>> listFolders(int pageSize) throws IOException;

    /**
     * Lista carpetas con paginación completa y soporte para carpetas compartidas
     * @param pageSize Número máximo de carpetas a retornar
     * @param pageToken Token de página para paginación
     * @param includeShared Si incluir carpetas compartidas
     * @return Mapa con carpetas y metadatos de paginación
     */
    Map<String, Object> listFoldersWithPagination(int pageSize, String pageToken, boolean includeShared) throws IOException;

    /**
     * Obtiene URL de descarga directa para un archivo
     * @param fileId ID del archivo
     * @return URL de descarga directa
     */
    String getDirectDownloadUrl(String fileId) throws IOException;

    /**
     * Búsqueda profesional de audios con paginación
     * @param fechaCreacion Fecha de creación en formato YYYYMMDD
     * @param numeroMovil Número móvil del contacto
     * @param numeroAgente Número del agente
     * @param folderId ID de la carpeta específica (opcional)
     * @param page Número de página
     * @param size Tamaño de página
     * @return Mapa con resultados paginados
     */
    Map<String, Object> searchAudiosProfessional(String fechaCreacion, String numeroMovil,
                                                 String numeroAgente, String folderId,
                                                 int page, int size) throws IOException;

    /**
     * Obtiene carpetas de forma recursiva
     * @param parentId ID de la carpeta padre (null para raíz)
     * @param maxDepth Profundidad máxima de búsqueda
     * @return Lista de carpetas con estructura jerárquica
     */
    List<Map<String, Object>> getFoldersRecursive(String parentId, int maxDepth) throws IOException;

    /**
     * Obtiene el nombre de un archivo por su ID
     * @param fileId ID del archivo
     * @return Nombre del archivo o null si no se encuentra
     */
    String getFileName(String fileId) throws IOException;

    /**
     * Obtiene todos los archivos de audio de Google Drive con información de leads asociados
     * @param fecha Fecha específica (opcional)
     * @param fechaInicio Fecha inicio para rango (opcional)
     * @param fechaFin Fecha fin para rango (opcional)
     * @param numeroAgente Filtrar por agente (opcional)
     * @param numeroMovil Filtrar por móvil (opcional)
     * @param incluirProcesados Si incluir audios ya procesados
     * @param page Número de página
     * @param size Tamaño de página
     * @return Mapa con audios y estadísticas
     */
    Map<String, Object> getAllAudioFiles(
            LocalDate fecha,
            LocalDate fechaInicio,
            LocalDate fechaFin,
            String numeroAgente,
            String numeroMovil,
            boolean incluirProcesados,
            int page,
            int size
    ) throws IOException;


// ... other method declarations ...

    Map<String,Object> listarAudiosPorAgentes(
            List<String> agentes,
            LocalDate fecha,
            LocalDate fechaInicio,
            LocalDate fechaFin,
            int page,
            int size
    );


}

package com.midas.crm.utils;

import com.google.api.services.drive.Drive;
import com.google.api.services.drive.model.File;
import com.google.api.services.drive.model.FileList;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.TextStyle;
import java.util.*;

/**
 * Utilidad para crear y gestionar la estructura organizada de carpetas en Google Drive
 *
 * ESTRUCTURA:
 * 📂 TEXTOS_TRANSCRITOS
 *    └── 📂 YYYY
 *        └── 📂 MM-MMMM
 *            └── 📂 DD-MM-YYYY
 *
 * 📂 TRANSCRIPCIONES_INDEPENDIENTES
 *    └── 📂 YYYY
 *        └── 📂 MM-MMMM
 *            └── 📂 DD-MM-YYYY
 *
 * 📂 CONVERTED_MP3
 *    └── 📂 YYYY
 *        └── 📂 MM-MMMM
 *            └── 📂 DD-MM-YYYY
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class GoogleDriveOrganizationHelper {

    private final Drive driveService;

    // Cache para evitar búsquedas repetidas
    private final Map<String, String> folderIdCache = new HashMap<>();

    /**
     * Tipos de carpetas principales soportadas
     */
    public enum FolderType {
        TEXTOS_TRANSCRITOS("TEXTOS_TRANSCRITOS"),
        TRANSCRIPCIONES_INDEPENDIENTES("TRANSCRIPCIONES_INDEPENDIENTES"),
        CONVERTED_MP3("CONVERTED_MP3");

        private final String folderName;

        FolderType(String folderName) {
            this.folderName = folderName;
        }

        public String getFolderName() {
            return folderName;
        }
    }

    /**
     * Obtiene o crea la estructura completa de carpetas para una fecha específica
     * @param folderType Tipo de carpeta principal
     * @param fecha Fecha para la organización
     * @return ID de la carpeta del día donde se deben guardar los archivos
     */
    public String getOrCreateDateStructure(FolderType folderType, LocalDate fecha) throws IOException {
        log.info("📁 Creando/obteniendo estructura para {} - Fecha: {}", folderType.getFolderName(), fecha);

        try {
            // 1. Carpeta principal
            String mainFolderId = getOrCreateMainFolder(folderType);

            // 2. Carpeta del año
            String yearFolderId = getOrCreateYearFolder(mainFolderId, fecha);

            // 3. Carpeta del mes
            String monthFolderId = getOrCreateMonthFolder(yearFolderId, fecha);

            // 4. Carpeta del día
            String dayFolderId = getOrCreateDayFolder(monthFolderId, fecha);

            log.info("✅ Estructura completa lista: {}/{}/{}/{}",
                    folderType.getFolderName(),
                    fecha.getYear(),
                    formatMonth(fecha),
                    formatDay(fecha));

            return dayFolderId;

        } catch (Exception e) {
            log.error("❌ Error creando estructura de carpetas: {}", e.getMessage(), e);
            throw new IOException("Error creando estructura de carpetas", e);
        }
    }

    /**
     * Obtiene o crea la carpeta principal
     */
    private String getOrCreateMainFolder(FolderType folderType) throws IOException {
        String folderName = folderType.getFolderName();
        String cacheKey = "MAIN_" + folderName;

        // Verificar cache
        if (folderIdCache.containsKey(cacheKey)) {
            return folderIdCache.get(cacheKey);
        }

        // Buscar carpeta existente
        String folderId = findFolderByNameAndParent(folderName, null);

        if (folderId == null) {
            // Crear nueva carpeta
            folderId = createFolder(folderName, null);
            log.info("📂 Carpeta principal creada: {} (ID: {})", folderName, folderId);
        } else {
            log.debug("📂 Carpeta principal encontrada: {} (ID: {})", folderName, folderId);
        }

        // Guardar en cache
        folderIdCache.put(cacheKey, folderId);
        return folderId;
    }

    /**
     * Obtiene o crea la carpeta del año
     */
    private String getOrCreateYearFolder(String parentId, LocalDate fecha) throws IOException {
        String yearName = String.valueOf(fecha.getYear());
        String cacheKey = parentId + "_YEAR_" + yearName;

        // Verificar cache
        if (folderIdCache.containsKey(cacheKey)) {
            return folderIdCache.get(cacheKey);
        }

        String folderId = findFolderByNameAndParent(yearName, parentId);

        if (folderId == null) {
            folderId = createFolder(yearName, parentId);
            log.info("📂 Carpeta de año creada: {} (ID: {})", yearName, folderId);
        } else {
            log.debug("📂 Carpeta de año encontrada: {} (ID: {})", yearName, folderId);
        }

        folderIdCache.put(cacheKey, folderId);
        return folderId;
    }

    /**
     * Obtiene o crea la carpeta del mes
     */
    private String getOrCreateMonthFolder(String parentId, LocalDate fecha) throws IOException {
        String monthName = formatMonth(fecha);
        String cacheKey = parentId + "_MONTH_" + monthName;

        // Verificar cache
        if (folderIdCache.containsKey(cacheKey)) {
            return folderIdCache.get(cacheKey);
        }

        String folderId = findFolderByNameAndParent(monthName, parentId);

        if (folderId == null) {
            folderId = createFolder(monthName, parentId);
            log.info("📂 Carpeta de mes creada: {} (ID: {})", monthName, folderId);
        } else {
            log.debug("📂 Carpeta de mes encontrada: {} (ID: {})", monthName, folderId);
        }

        folderIdCache.put(cacheKey, folderId);
        return folderId;
    }

    /**
     * Obtiene o crea la carpeta del día
     */
    private String getOrCreateDayFolder(String parentId, LocalDate fecha) throws IOException {
        String dayName = formatDay(fecha);
        String cacheKey = parentId + "_DAY_" + dayName;

        // Verificar cache
        if (folderIdCache.containsKey(cacheKey)) {
            return folderIdCache.get(cacheKey);
        }

        String folderId = findFolderByNameAndParent(dayName, parentId);

        if (folderId == null) {
            folderId = createFolder(dayName, parentId);
            log.info("📂 Carpeta de día creada: {} (ID: {})", dayName, folderId);
        } else {
            log.debug("📂 Carpeta de día encontrada: {} (ID: {})", dayName, folderId);
        }

        folderIdCache.put(cacheKey, folderId);
        return folderId;
    }

    /**
     * Busca una carpeta por nombre en un padre específico
     */
    private String findFolderByNameAndParent(String folderName, String parentId) throws IOException {
        try {
            StringBuilder queryBuilder = new StringBuilder();
            queryBuilder.append("mimeType='application/vnd.google-apps.folder'");
            queryBuilder.append(" and name='").append(folderName).append("'");
            queryBuilder.append(" and trashed=false");

            if (parentId != null) {
                queryBuilder.append(" and '").append(parentId).append("' in parents");
            }

            FileList result = driveService.files().list()
                    .setQ(queryBuilder.toString())
                    .setPageSize(1)
                    .setFields("files(id,name)")
                    .execute();

            List<File> folders = result.getFiles();
            if (folders != null && !folders.isEmpty()) {
                return folders.get(0).getId();
            }

            return null;

        } catch (Exception e) {
            log.error("Error buscando carpeta '{}' en padre '{}': {}", folderName, parentId, e.getMessage());
            throw e;
        }
    }

    /**
     * Crea una nueva carpeta
     */
    private String createFolder(String folderName, String parentId) throws IOException {
        try {
            File fileMetadata = new File();
            fileMetadata.setName(folderName);
            fileMetadata.setMimeType("application/vnd.google-apps.folder");

            if (parentId != null) {
                fileMetadata.setParents(Collections.singletonList(parentId));
            }

            File folder = driveService.files().create(fileMetadata)
                    .setFields("id,name")
                    .execute();

            return folder.getId();

        } catch (Exception e) {
            log.error("Error creando carpeta '{}' en padre '{}': {}", folderName, parentId, e.getMessage());
            throw e;
        }
    }

    /**
     * Formatea el mes como MM-MMMM (ej: 06-JUNIO)
     */
    private String formatMonth(LocalDate fecha) {
        return String.format("%02d-%s",
                fecha.getMonthValue(),
                fecha.getMonth().getDisplayName(TextStyle.FULL, new Locale("es", "ES")).toUpperCase());
    }

    /**
     * Formatea el día como DD-MM-YYYY (ej: 30-06-2025)
     */
    private String formatDay(LocalDate fecha) {
        return fecha.format(DateTimeFormatter.ofPattern("dd-MM-yyyy"));
    }

    /**
     * Limpia la cache de IDs de carpetas
     */
    public void clearCache() {
        folderIdCache.clear();
        log.info("🧹 Cache de carpetas limpiada");
    }

    /**
     * Obtiene estadísticas de la cache
     */
    public Map<String, Object> getCacheStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("size", folderIdCache.size());
        stats.put("entries", new ArrayList<>(folderIdCache.keySet()));
        return stats;
    }
}
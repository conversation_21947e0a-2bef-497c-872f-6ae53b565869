import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

// Angular Material
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDividerModule } from '@angular/material/divider';
import { MatChipsModule } from '@angular/material/chips';

// Routing
import { CorreosRoutingModule } from './correos-routing.module';

// Components
import { CorreosComponent } from './correos.component';
import { BandejaEntradaComponent } from './pages/bandeja-entrada/bandeja-entrada.component';
import { DetalleMensajeComponent } from './pages/detalle-mensaje/detalle-mensaje.component';

// Services
import { GmailService } from './services/gmail.service';

@NgModule({
  declarations: [
    CorreosComponent,
    BandejaEntradaComponent,
    DetalleMensajeComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    CorreosRoutingModule,
    
    // Angular Material
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatProgressSpinnerModule,
    MatSelectModule,
    MatFormFieldModule,
    MatInputModule,
    MatTooltipModule,
    MatDividerModule,
    MatChipsModule
  ],
  providers: [
    GmailService
  ]
})
export class CorreosModule { }
